このノードは、生成モデルのコンディショニングを修正するために、特定のエリアに指定された強度のマスクを適用するように設計されています。これにより、コンディショニング内でのターゲットを絞った調整が可能となり、生成プロセスをより精密に制御できます。

## 入力

### 必須

| パラメータ     | データ型 | 説明 |
|---------------|--------------|-------------|
| `CONDITIONING` | CONDITIONING | 修正されるコンディショニングデータ。マスクと強度調整を適用する基礎として機能します。 |
| `mask`        | `MASK`       | コンディショニング内で修正されるエリアを指定するマスクテンソル。 |
| `strength`    | `FLOAT`      | コンディショニングに対するマスクの効果の強度を示し、適用される修正を微調整することができます。 |
| `set_cond_area` | COMBO[STRING] | マスクの効果がデフォルトエリアに適用されるか、マスク自体によって制限されるかを決定し、特定の領域をターゲットにする柔軟性を提供します。 |

## 出力

| パラメータ     | データ型 | 説明 |
|---------------|--------------|-------------|
| `CONDITIONING` | CONDITIONING | マスクと強度調整が適用された修正済みのコンディショニングデータ。 |
