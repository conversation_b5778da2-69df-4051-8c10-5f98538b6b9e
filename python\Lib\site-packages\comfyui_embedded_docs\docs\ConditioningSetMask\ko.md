이 노드는 생성 모델의 컨디셔닝을 수정하기 위해 특정 영역에 지정된 강도로 마스크를 적용하도록 설계되었습니다. 이를 통해 컨디셔닝 내에서 목표 조정이 가능하며, 생성 과정에 대한 보다 정밀한 제어가 가능합니다.

## 입력

### 필수

| 매개변수     | 데이터 유형 | 설명 |
|---------------|--------------|-------------|
| `CONDITIONING` | CONDITIONING | 수정할 컨디셔닝 데이터입니다. 마스크 및 강도 조정을 적용하는 기초로 사용됩니다. |
| `mask`        | `MASK`       | 컨디셔닝 내에서 수정할 영역을 지정하는 마스크 텐서입니다. |
| `strength`    | `FLOAT`      | 컨디셔닝에 대한 마스크 효과의 강도로, 적용된 수정 사항을 미세 조정할 수 있습니다. |
| `set_cond_area` | COMBO[STRING] | 마스크의 효과가 기본 영역에 적용될지 아니면 마스크 자체에 의해 제한될지를 결정하여 특정 영역을 목표로 하는 유연성을 제공합니다. |

## 출력

| 매개변수     | 데이터 유형 | 설명 |
|---------------|--------------|-------------|
| `CONDITIONING` | CONDITIONING | 마스크 및 강도 조정이 적용된 수정된 컨디셔닝 데이터입니다. |
