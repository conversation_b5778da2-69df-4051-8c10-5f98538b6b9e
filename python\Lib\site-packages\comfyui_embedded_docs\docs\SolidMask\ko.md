
SolidMask 노드는 전체 영역에 걸쳐 지정된 값으로 균일한 마스크를 생성합니다. 이는 특정 차원과 강도의 마스크를 생성하도록 설계되어 다양한 이미지 처리 및 마스킹 작업에 유용합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                   |
| -------- | ----------- | -------------------------------------------------------------------------------------- |
| `value`  | FLOAT       | 마스크의 강도 값을 지정하여, 후속 작업에서의 전체적인 외관과 유용성에 영향을 미칩니다. |
| `width`  | INT         | 생성된 마스크의 너비를 결정하여, 크기와 종횡비에 직접적인 영향을 미칩니다.             |
| `height` | INT         | 생성된 마스크의 높이를 설정하여, 크기와 종횡비에 영향을 미칩니다.                      |

## 출력

| 매개변수 | 데이터 유형 | 설명                                             |
| -------- | ----------- | ------------------------------------------------ |
| `mask`   | MASK        | 지정된 차원과 값으로 균일한 마스크를 출력합니다. |
