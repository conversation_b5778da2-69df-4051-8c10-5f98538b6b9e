このノードは、`ComfyUI/models/checkpoints` フォルダーにあるモデルを検出し、
また、extra_model_paths.yaml ファイルで設定された追加パスのモデルも読み込みます。
場合によっては、**ComfyUIのインターフェースを更新する**必要があるかもしれませんので、対応するフォルダー内のモデルファイルを読み込むことができます。

UpscaleModelLoaderノードは、指定されたディレクトリからアップスケールモデルをロードするために設計されています。画像のアップスケーリングタスクのために、アップスケールモデルの取得と準備を容易にし、評価のために正しくロードおよび設定されることを保証します。

## 入力

| フィールド       | Comfy dtype       | 説明                                                                       |
|----------------|-------------------|-----------------------------------------------------------------------------------|
| `model_name`   | `COMBO[STRING]`    | ロードするアップスケールモデルの名前を指定し、アップスケールモデルディレクトリから正しいモデルファイルを特定して取得します。 |

## 出力

| フィールド          | Comfy dtype         | 説明                                                              |
|-------------------|---------------------|--------------------------------------------------------------------------|
| `upscale_model`  | `UPSCALE_MODEL`     | ロードされ準備されたアップスケールモデルを返し、画像のアップスケーリングタスクで使用できるようにします。 |
