Metadata-Version: 2.1
Name: cssselect2
Version: 0.7.0
Summary: CSS selectors for Python ElementTree
Keywords: css,elementtree
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: CourtBouillon <<EMAIL>>
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Dist: tinycss2
Requires-Dist: webencodings
Requires-Dist: sphinx ; extra == "doc"
Requires-Dist: sphinx_rtd_theme ; extra == "doc"
Requires-Dist: pytest ; extra == "test"
Requires-Dist: isort ; extra == "test"
Requires-Dist: flake8 ; extra == "test"
Project-URL: Changelog, https://github.com/Kozea/cssselect2/releases
Project-URL: Code, https://github.com/Kozea/cssselect2/
Project-URL: Documentation, https://doc.courtbouillon.org/cssselect2/
Project-URL: Donation, https://opencollective.com/courtbouillon
Project-URL: Homepage, https://doc.courtbouillon.org/cssselect2/
Project-URL: Issues, https://github.com/Kozea/cssselect2/issues
Provides-Extra: doc
Provides-Extra: test

cssselect2 is a straightforward implementation of CSS4 Selectors for markup
documents (HTML, XML, etc.) that can be read by ElementTree-like parsers
(including cElementTree, lxml, html5lib, etc.)

* Free software: BSD license
* For Python 3.7+, tested on CPython and PyPy
* Documentation: https://doc.courtbouillon.org/cssselect2
* Changelog: https://github.com/Kozea/cssselect2/releases
* Code, issues, tests: https://github.com/Kozea/cssselect2
* Code of conduct: https://www.courtbouillon.org/code-of-conduct.html
* Professional support: https://www.courtbouillon.org
* Donation: https://opencollective.com/courtbouillon

cssselect2 has been created and developed by Kozea (https://kozea.fr/).
Professional support, maintenance and community management is provided by
CourtBouillon (https://www.courtbouillon.org/).

Copyrights are retained by their contributors, no copyright assignment is
required to contribute to cssselect2. Unless explicitly stated otherwise, any
contribution intentionally submitted for inclusion is licensed under the BSD
3-clause license, without any additional terms or conditions. For full
authorship information, see the version control history.

