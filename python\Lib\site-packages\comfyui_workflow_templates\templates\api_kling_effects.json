{"id": "c93669fa-4940-4195-9073-77db06af3d0c", "revision": 0, "last_node_id": 7, "last_link_id": 2, "nodes": [{"id": 3, "type": "SaveVideo", "pos": [1173.5587158203125, 1359.8980712890625], "size": [315, 413], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 2}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo", "cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 1, "type": "KlingSingleImageVideoEffectNode", "pos": [829.906982421875, 1357.8106689453125], "size": [315, 146], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [2]}, {"name": "video_id", "type": "STRING", "links": null}, {"name": "duration", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "KlingSingleImageVideoEffectNode", "cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["expansion", "kling-v1-6", "5"]}, {"id": 5, "type": "<PERSON>downNote", "pos": [79.13523864746094, 1365.1146240234375], "size": [378.2833251953125, 274.91668701171875], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 2, "type": "LoadImage", "pos": [484.*************, 1362.************], "size": [315, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["input.png", "image"]}], "links": [[1, 2, 0, 1, 0, "IMAGE"], [2, 1, 0, 3, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [296.90326439492, -1052.3463959596124]}, "frontendVersion": "1.18.5", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}