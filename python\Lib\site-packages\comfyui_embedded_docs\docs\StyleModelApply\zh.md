
此节点将风格模型应用于给定的条件，基于CLIP视觉模型的输出增强或改变其风格。它将风格模型的条件整合到现有条件中，允许在生成过程中风格无缝融合。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `CONDITIONING` | CONDITIONING | 原始条件数据，用于对文本进行编码的CLIP模型将应用风格模型的条件。它对于定义将被增强或改变的基础上下文或风格至关重要。 |
| `style_model` | `STYLE_MODEL` | 用于基于CLIP视觉模型的输出生成新条件的风格模型。它在定义要应用的新风格中起着关键作用。 |
| `clip_vision_output` | `CLIP_VISION_OUTPUT` | 来自CLIP视觉模型的输出，风格模型用它来生成新条件。它为风格应用提供了必要的视觉上下文。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `CONDITIONING` | CONDITIONING | 增强或改变的条件，融入了风格模型的输出。它代表了最终的风格化条件，准备进行进一步处理或生成。 |
