{"id": "dbedd4b2-c963-475d-8057-72a15e532fd5", "revision": 0, "last_node_id": 56, "last_link_id": 447, "nodes": [{"id": 47, "type": "RandomNoise", "pos": [1510, -100], "size": [315, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [434]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "RandomNoise"}, "widgets_values": [705826023365990, "randomize"]}, {"id": 41, "type": "UNETLoader", "pos": [266.0548095703125, 63.377586364746094], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [439]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "UNETLoader", "models": [{"name": "hidream_e1_full_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/diffusion_models/hidream_e1_full_bf16.safetensors", "hash": "b645503c5d5d583a135d7e77c655237ebc6dc132d49d763759c1dd51683bd1dd", "hash_type": "SHA256", "directory": "diffusion_models"}]}, "widgets_values": ["hidream_e1_full_bf16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "QuadrupleCLIPLoader", "pos": [267.21856689453125, 190.70782470703125], "size": [315, 130], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [420, 421]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "QuadrupleCLIPLoader", "models": [{"name": "clip_g_hidream.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_g_hidream.safetensors", "hash": "3771e70e36450e5199f30bad61a53faae85a2e02606974bcda0a6a573c0519d5", "hash_type": "SHA256", "directory": "text_encoders"}, {"name": "clip_l_hidream.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_l_hidream.safetensors", "hash": "706fdb88e22e18177b207837c02f4b86a652abca0302821f2bfa24ac6aea4f71", "hash_type": "SHA256", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/t5xxl_fp8_e4m3fn_scaled.safetensors", "hash": "a498f0485dc9536735258018417c3fd7758dc3bccc0a645feaa472b34955557a", "hash_type": "SHA256", "directory": "text_encoders"}, {"name": "llama_3.1_8b_instruct_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/llama_3.1_8b_instruct_fp8_scaled.safetensors", "hash": "9f86897bbeb933ef4fd06297740edb8dd962c94efcd92b373a11460c33765ea6", "hash_type": "SHA256", "directory": "text_encoders"}]}, "widgets_values": ["clip_g_hidream.safetensors", "clip_l_hidream.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "llama_3.1_8b_instruct_fp8_scaled.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 43, "type": "VAELoader", "pos": [267.9156188964844, 371.0837707519531], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [422, 442]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/vae/ae.safetensors", "hash": "afc8e28272cd15db3919bacdb6918ce9c1ed22e96cb12c4d5ed0fba823529e38", "hash_type": "SHA256", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [750, 550], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 420}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [445]}], "title": "Positive", "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Let the girl put on the VR glasses full of a sense of technology, just like the scenes in Ready Player One, with CG rendering and ultra-realism."], "color": "#232", "bgcolor": "#353"}, {"id": 13, "type": "LoadImage", "pos": [260, 540], "size": [440, 420], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [425]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LoadImage"}, "widgets_values": ["input.webp", "image"]}, {"id": 44, "type": "ImageScale", "pos": [730, 320], "size": [315, 130], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 425}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [443]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 768, "center"]}, {"id": 56, "type": "Note", "pos": [730, 160], "size": [304.758056640625, 120.64888763427734], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The image will be scaled to 768*768. Using other dimensions may not yield particularly good results."], "color": "#432", "bgcolor": "#653"}, {"id": 52, "type": "InstructPixToPixConditioning", "pos": [1240, 60], "size": [235.1999969482422, 86], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 445}, {"name": "negative", "type": "CONDITIONING", "link": 446}, {"name": "vae", "type": "VAE", "link": 442}, {"name": "pixels", "type": "IMAGE", "link": 443}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [431]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [432]}, {"name": "latent", "type": "LATENT", "slot_index": 2, "links": [438]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "InstructPixToPixConditioning"}, "widgets_values": []}, {"id": 48, "type": "Reroute", "pos": [1240, -10], "size": [75, 26], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 439}], "outputs": [{"name": "", "type": "MODEL", "links": [427, 430]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 53, "type": "DualCFGGuider", "pos": [1510, 20], "size": [315, 166], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 430}, {"name": "cond1", "type": "CONDITIONING", "link": 431}, {"name": "cond2", "type": "CONDITIONING", "link": 432}, {"name": "negative", "type": "CONDITIONING", "link": 447}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [435]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "DualCFGGuider"}, "widgets_values": [5, 2, "regular"]}, {"id": 46, "type": "KSamplerSelect", "pos": [1510, 230], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [436]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 49, "type": "BasicScheduler", "pos": [1510, 330], "size": [315, 106], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 427}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [437]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["normal", 28, 1]}, {"id": 8, "type": "VAEDecode", "pos": [1850, 50], "size": [210, 46], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 444}, {"name": "vae", "type": "VAE", "link": 422}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 54, "type": "SamplerCustomAdvanced", "pos": [1850, -100], "size": [230, 106], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 434}, {"name": "guider", "type": "GUIDER", "link": 435}, {"name": "sampler", "type": "SAMPLER", "link": 436}, {"name": "sigmas", "type": "SIGMAS", "link": 437}, {"name": "latent_image", "type": "LATENT", "link": 438}], "outputs": [{"name": "output", "type": "LATENT", "slot_index": 0, "links": [444]}, {"name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1200, 500], "size": [900, 640], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [750, 750], "size": [414.3643798828125, 155.3189697265625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 421}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [446, 447]}], "title": "Negative", "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, blurry, distorted"], "color": "#223", "bgcolor": "#335"}, {"id": 55, "type": "<PERSON>downNote", "pos": [-210, 30], "size": [440, 420], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org/tutorials/image/hidream/hidream-e1) | [教程](https://docs.comfy.org/zh-CN/tutorials/advanced/hidream-e1)\n\n**Diffusion model**\n- [hidream_e1_full_bf16.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/diffusion_models/hidream_e1_full_bf16.safetensors)\n\n**Text encoder**：\n\n- [clip_l_hidream.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_l_hidream.safetensors)\n- [clip_g_hidream.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_g_hidream.safetensors)\n- [t5xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/t5xxl_fp8_e4m3fn_scaled.safetensors) \n- [llama_3.1_8b_instruct_fp8_scaled.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/llama_3.1_8b_instruct_fp8_scaled.safetensors)\n\n**VAE**\n-  [ae.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/vae/ae.safetensors)\n\n\n\nFile saved location\n```\n📂 ComfyUI/\n├── 📂 models/\n│   ├── 📂 text_encoders/\n│   │   ├─── clip_l_hidream.safetensors\n│   │   ├─── clip_g_hidream.safetensors\n│   │   ├─── t5xxl_fp8_e4m3fn_scaled.safetensors\n│   │   └─── llama_3.1_8b_instruct_fp8_scaled.safetensors\n│   └── 📂 vae/\n│   │   └── ae.safetensors\n│   └── 📂 diffusion_models/\n│       └── hidream_e1_full_bf16.safetensors   \n```\n"], "color": "#432", "bgcolor": "#653"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [420, 42, 0, 6, 0, "CLIP"], [421, 42, 0, 7, 0, "CLIP"], [422, 43, 0, 8, 1, "VAE"], [425, 13, 0, 44, 0, "IMAGE"], [427, 48, 0, 49, 0, "MODEL"], [430, 48, 0, 53, 0, "MODEL"], [431, 52, 0, 53, 1, "CONDITIONING"], [432, 52, 1, 53, 2, "CONDITIONING"], [434, 47, 0, 54, 0, "NOISE"], [435, 53, 0, 54, 1, "GUIDER"], [436, 46, 0, 54, 2, "SAMPLER"], [437, 49, 0, 54, 3, "SIGMAS"], [438, 52, 2, 54, 4, "LATENT"], [439, 41, 0, 48, 0, "*"], [442, 43, 0, 52, 2, "VAE"], [443, 44, 0, 52, 3, "IMAGE"], [444, 54, 0, 8, 0, "LATENT"], [445, 6, 0, 52, 0, "CONDITIONING"], [446, 7, 0, 52, 1, "CONDITIONING"], [447, 7, 0, 53, 3, "CONDITIONING"]], "groups": [{"id": 1, "title": "IP2PSampler", "bounding": [1210, -190, 890, 630], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step1 - Load models", "bounding": [250, -10, 346.5339050292969, 460.7162780761719], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - Prompt", "bounding": [730, 470, 442.4894104003906, 458.2039794921875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Step2 - Upload Image", "bounding": [250, 470, 460, 510], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 2.1435888100000016, "offset": [314.75308047605, 111.01216015615556]}, "frontendVersion": "1.23.4", "groupNodes": {}}, "version": 0.4}