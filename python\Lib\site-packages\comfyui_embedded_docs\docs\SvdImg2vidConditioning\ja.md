
<PhotoProvider>
      <PhotoView src="/conditioning/video_models/SVD_img2vid_Conditioning.jpg">
        <img src="/conditioning/video_models/SVD_img2vid_Conditioning.jpg" alt="comfyUIノード-SVD_img2vid_Conditioning|SVD画像から動画へのコンディショニング" className='rounded-lg' priority/>
      </PhotoView>
</PhotoProvider>

このノードは、SVD_img2vidモデルを使用したビデオ生成タスクのためのコンディショニングデータを生成するように設計されています。初期画像、ビデオパラメータ、VAEモデルなどのさまざまな入力を受け取り、ビデオフレームの生成をガイドするためのコンディショニングデータを生成します。

## 入力

| パラメータ             | Comfy dtype        | 説明 |
|----------------------|--------------------|-------------|
| `clip_vision`         | `CLIP_VISION`      | 初期画像から視覚的特徴をエンコードするために使用されるCLIPビジョンモデルを表し、ビデオ生成のための画像の内容とコンテキストを理解する上で重要な役割を果たします。 |
| `init_image`          | `IMAGE`            | ビデオが生成される初期画像で、ビデオ生成プロセスの出発点として機能します。 |
| `vae`                 | `VAE`              | 初期画像を潜在空間にエンコードするために使用される変分オートエンコーダー（VAE）モデルで、一貫した連続的なビデオフレームの生成を促進します。 |
| `width`               | `INT`              | 生成されるビデオフレームの希望する幅で、ビデオの解像度をカスタマイズすることができます。 |
| `height`              | `INT`              | ビデオフレームの希望する高さで、ビデオのアスペクト比と解像度を制御できます。 |
| `video_frames`        | `INT`              | 生成されるビデオのフレーム数を指定し、ビデオの長さを決定します。 |
| `motion_bucket_id`    | `INT`              | ビデオ生成に適用されるモーションのタイプを分類するための識別子で、動的で魅力的なビデオの作成を支援します。 |
| `fps`                 | `INT`              | ビデオのフレーム毎秒（fps）レートで、生成されたビデオの滑らかさとリアリズムに影響を与えます。 |
| `augmentation_level`  | `FLOAT`            | 初期画像に適用される拡張のレベルを制御するパラメータで、生成されたビデオフレームの多様性と変動性に影響を与えます。 |

## 出力

| パラメータ     | Comfy dtype        | 説明 |
|---------------|--------------------|-------------|
| `positive`    | `CONDITIONING`     | エンコードされた特徴とビデオ生成プロセスを望ましい方向に導くためのパラメータからなるポジティブなコンディショニングデータ。 |
| `negative`    | `CONDITIONING`     | ポジティブなコンディショニングと対比を提供するネガティブなコンディショニングデータで、生成されたビデオで特定のパターンや特徴を避けるために使用できます。 |
| `latent`      | `LATENT`           | ビデオの各フレームに対して生成された潜在表現で、ビデオ生成プロセスの基礎的なコンポーネントとして機能します。 |
