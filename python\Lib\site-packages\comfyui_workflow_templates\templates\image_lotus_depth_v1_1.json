{"id": "5bfb5805-0127-4895-983d-351adc2654fe", "revision": 0, "last_node_id": 73, "last_link_id": 242, "nodes": [{"id": 8, "type": "VAEDecode", "pos": [1380, -240], "size": [210, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 232}, {"name": "vae", "type": "VAE", "link": 240}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [35]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1380, -140], "size": [342, 407], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 242}], "outputs": [], "title": "output", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}, {"id": 10, "type": "UNETLoader", "pos": [135.34181213378906, -290.1947937011719], "size": [305.93701171875, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [31, 241]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "widget_ue_connectable": {}, "models": [{"name": "lotus-depth-d-v1-1.safetensors", "url": "https://huggingface.co/Comfy-Org/lotus/resolve/main/lotus-depth-d-v1-1.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["lotus-depth-d-v1-1.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "LoadImage", "pos": [124.40740966796875, -35.11737060546875], "size": [320.8564147949219, 429.6639099121094], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [37]}, {"name": "MASK", "type": "MASK", "links": []}], "title": "input", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["input.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 14, "type": "VAELoader", "pos": [134.531494140625, -165.18197631835938], "size": [305.93701171875, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [38, 240]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAELoader", "widget_ue_connectable": {}, "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors", "directory": "vae"}]}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 16, "type": "SamplerCustomAdvanced", "pos": [990.6585693359375, -319.9144287109375], "size": [355.20001220703125, 326], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 237}, {"name": "guider", "type": "GUIDER", "link": 27}, {"name": "sampler", "type": "SAMPLER", "link": 33}, {"name": "sigmas", "type": "SIGMAS", "link": 194}, {"name": "latent_image", "type": "LATENT", "link": 201}], "outputs": [{"name": "output", "type": "LATENT", "slot_index": 0, "links": [232]}, {"name": "denoised_output", "type": "LATENT", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SamplerCustomAdvanced", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 18, "type": "<PERSON><PERSON><PERSON><PERSON>", "pos": [730.47705078125, -320], "size": [210, 26], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [237]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 19, "type": "BasicGuider", "pos": [730.2631225585938, -251.22537231445312], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 241}, {"name": "conditioning", "type": "CONDITIONING", "link": 238}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [27]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "BasicGuider", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 20, "type": "BasicScheduler", "pos": [488.64459228515625, -147.67201232910156], "size": [210, 106], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 31}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "slot_index": 0, "links": [66]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "BasicScheduler", "widget_ue_connectable": {}}, "widgets_values": ["normal", 1, 1]}, {"id": 21, "type": "KSamplerSelect", "pos": [730.2631225585938, -161.22537231445312], "size": [210, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "slot_index": 0, "links": [33]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "KSamplerSelect", "widget_ue_connectable": {}}, "widgets_values": ["euler"]}, {"id": 22, "type": "ImageInvert", "pos": [1380, -310], "size": [210, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 35}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [242]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "ImageInvert", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 23, "type": "VAEEncode", "pos": [730.2631225585938, 38.77463912963867], "size": [210, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 37}, {"name": "vae", "type": "VAE", "link": 38}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [201]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEEncode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 28, "type": "SetFirstSigma", "pos": [730.2631225585938, -61.22536087036133], "size": [210, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "sigmas", "type": "SIGMAS", "link": 66}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "slot_index": 0, "links": [194]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SetFirstSigma", "widget_ue_connectable": {}}, "widgets_values": [999.0000000000002]}, {"id": 68, "type": "LotusConditioning", "pos": [490, -230], "size": [210, 26], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "conditioning", "type": "CONDITIONING", "slot_index": 0, "links": [238]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "LotusConditioning", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 72, "type": "<PERSON>downNote", "pos": [-301.2855224609375, -342.20196533203125], "size": [400, 250], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["**Diffusion Model**\n\nDownload [lotus-depth-d-v1-1.safetensors](https://huggingface.co/Comfy-Org/lotus/resolve/main/lotus-depth-d-v1-1.safetensors) \n and place it in **ComfyUI/models/diffusion_models**\n\n**VAE Model**\n\nDownload  [vae-ft-mse-840000-ema-pruned.safetensors](https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors)  and place it in **ComfyUI/models/vae** or you can use any SD1.5 VAE if you prefer.\n\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   └─── lotus-depth-d-v1-1.safetensors\n│   └── vae/\n│       └──  lvae-ft-mse-840000-ema-pruned.safetensors\n```\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 73, "type": "<PERSON>downNote", "pos": [738.2103271484375, 178.4742431640625], "size": [210, 119.88951873779297], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Adjust the value of SetFirstSigma to get different results.\n\n---\n\n调整 `SetFirstSigma` 的数值来获得不同的结果"], "color": "#432", "bgcolor": "#653"}], "links": [[27, 19, 0, 16, 1, "GUIDER"], [31, 10, 0, 20, 0, "MODEL"], [33, 21, 0, 16, 2, "SAMPLER"], [35, 8, 0, 22, 0, "IMAGE"], [37, 12, 0, 23, 0, "IMAGE"], [38, 14, 0, 23, 1, "VAE"], [66, 20, 0, 28, 0, "SIGMAS"], [194, 28, 0, 16, 3, "SIGMAS"], [201, 23, 0, 16, 4, "LATENT"], [232, 16, 0, 8, 0, "LATENT"], [237, 18, 0, 16, 0, "NOISE"], [238, 68, 0, 19, 1, "CONDITIONING"], [241, 10, 0, 19, 0, "MODEL"], [242, 22, 0, 9, 0, "IMAGE"], [240, 14, 0, 8, 1, "VAE"]], "groups": [{"id": 1, "title": "Load Models Here", "bounding": [120, -370, 335, 281.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6727499949325646, "offset": [603.3997348092605, 633.1173993385003]}, "frontendVersion": "1.23.4", "node_versions": {"comfy-core": "0.3.34"}, "ue_links": [], "groupNodes": {}, "links_added_by_ue": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}