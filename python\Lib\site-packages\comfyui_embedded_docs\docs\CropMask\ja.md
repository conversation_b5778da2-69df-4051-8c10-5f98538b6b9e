CropMaskノードは、指定されたマスクから特定の領域を切り取るために設計されています。ユーザーは座標と寸法を指定することで、関心領域を定義し、マスクの一部を効果的に抽出してさらなる処理や分析に利用できます。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `mask`    | MASK        | マスク入力は、切り取るべきマスク画像を表します。指定された座標と寸法に基づいて抽出する領域を定義するために不可欠です。 |
| `x`       | INT         | x座標は、切り取りを開始する水平軸上の開始点を指定します。 |
| `y`       | INT         | y座標は、切り取り操作のための垂直軸上の開始点を決定します。 |
| `width`   | INT         | 幅は、開始点からの切り取り領域の水平範囲を定義します。 |
| `height`  | INT         | 高さは、開始点からの切り取り領域の垂直範囲を指定します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `mask`    | MASK        | 出力は、指定された座標と寸法によって定義された元のマスクの一部である切り取られたマスクです。 |
