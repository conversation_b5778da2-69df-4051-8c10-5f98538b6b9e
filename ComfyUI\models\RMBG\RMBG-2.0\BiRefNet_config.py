from transformers import PretrainedConfig

class BiRefNetConfig(PretrainedConfig):
    model_type = "SegformerForSemanticSegmentation"
    is_composition = False
    
    def __init__(
        self,
        bb_pretrained=False,
        **kwargs
    ):
        self.bb_pretrained = bb_pretrained
        super().__init__(**kwargs)
    
    def get_text_config(self, decoder=False):
        return self
        
    def to_dict(self):
        """
        Serializes this instance to a Python dictionary.
        Returns:
            :obj:`Dict[str, any]`: Dictionary of all the attributes that make up this configuration instance.
        """
        output = super().to_dict()
        output.update(
            bb_pretrained=self.bb_pretrained,
        )
        return output
    
    @property
    def tie_word_embeddings(self):
        return False