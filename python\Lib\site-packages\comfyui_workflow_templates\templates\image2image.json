{"id": "7cb6261d-3b03-4171-bbd1-a4b256b42404", "revision": 0, "last_node_id": 19, "last_link_id": 19, "nodes": [{"id": 14, "type": "CheckpointLoaderSimple", "pos": [-90, 200], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [13]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [14, 15]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [16, 17]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "v1-5-pruned-emaonly-fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["v1-5-pruned-emaonly-fp16.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [280, 210], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 14}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["photograph of victorian woman with wings, sky clouds, meadow grass\n"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [280, 410], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 15}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["watermark, text\n"], "color": "#223", "bgcolor": "#335"}, {"id": 16, "type": "<PERSON>downNote", "pos": [-440, 160], "size": [314.95745849609375, 133.0336456298828], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorials](https://docs.comfy.org/tutorials/basic/image-to-image)|[教程](https://docs.comfy.org/zh-CN/tutorials/basic/image-to-image)\n\nDownload  [v1-5-pruned-emaonly-fp16.safetensors](https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors?download=true) and save it under  **ComfyUI/models/checkpoints** "], "color": "#432", "bgcolor": "#653"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [740, 180], "size": [315, 262], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 13}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 11}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": [280823642470253, "randomize", 20, 8, "dpmpp_2m", "normal", 0.8700000000000001]}, {"id": 8, "type": "VAEDecode", "pos": [1080, 180], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1080, 280], "size": [450, 430], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}, {"id": 10, "type": "LoadImage", "pos": [-90, 410], "size": [310, 314], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [18]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["example.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "VAEEncode", "pos": [560, 650], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 19}, {"name": "vae", "type": "VAE", "link": 16}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [11]}], "properties": {"Node name for S&R": "VAEEncode", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": []}, {"id": 17, "type": "<PERSON>downNote", "pos": [740, 490], "size": [310, 110], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["When using the image - to - image workflow, you should remember that the **denoise** value should be less than 1. The closer the value is to 0, the more features of the input image the output image will retain."], "color": "#432", "bgcolor": "#653"}, {"id": 18, "type": "ImageScaleToTotalPixels", "pos": [280, 650], "size": [260, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 18}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [19]}], "properties": {"Node name for S&R": "ImageScaleToTotalPixels"}, "widgets_values": ["nearest-exact", 0.25]}, {"id": 19, "type": "<PERSON>downNote", "pos": [280, 780], "size": [270, 120], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "About Scale Image to Total Pixels", "properties": {}, "widgets_values": ["The model this template uses is trained based on a 512*512 image dataset. So if you use an input image that's too large, it might cause some issues. We've added the Scale image to total pixels node to scale images. If you're quite familiar with this model, you can remove it."], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [11, 12, 0, 3, 3, "LATENT"], [13, 14, 0, 3, 0, "MODEL"], [14, 14, 1, 6, 0, "CLIP"], [15, 14, 1, 7, 0, "CLIP"], [16, 14, 2, 12, 1, "VAE"], [17, 14, 2, 8, 1, "VAE"], [18, 10, 0, 18, 0, "IMAGE"], [19, 18, 0, 12, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Step2 - Upload image", "bounding": [-100, 330, 340, 420], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step1 - Load model", "bounding": [-100, 130, 340, 180], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - Prompt", "bounding": [270, 130, 450, 470], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.925107135909973, "offset": [590.5116922244889, -18.70663404599139]}, "frontendVersion": "1.24.1"}, "version": 0.4}