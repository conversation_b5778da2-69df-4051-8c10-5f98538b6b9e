{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 27, "last_link_id": 10, "nodes": [{"id": 24, "type": "SaveImage", "pos": [1020, 490], "size": [315, 270], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 10}], "outputs": [], "properties": {}, "widgets_values": ["DALL-E3"]}, {"id": 21, "type": "OpenAIDalle3", "pos": [590, 490], "size": [400, 260], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10]}], "properties": {"Node name for S&R": "OpenAIDalle3"}, "widgets_values": ["Create a 3D digital art of a delicate figure. She has short, pastel - pink hair with iridescent shimmering effects, as if catching and reflecting light magically. Her eyes are gently closed, exuding a serene and peaceful expression. She's wearing a translucent off-the-shoulder dress with puffed sleeves and flowing fabric that also has an iridescent sheen. Set against a plain, muted gray background to highlight the figure, making her the focal point of an enchanting and otherworldly scene.", 233657781, "randomize", "hd", "vivid", "1024x1024"]}, {"id": 26, "type": "<PERSON>downNote", "pos": [225.7735137939453, 494.646484375], "size": [342.0454406738281, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-3) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/openai/dall-e-)"], "color": "#432", "bgcolor": "#653"}, {"id": 27, "type": "<PERSON>downNote", "pos": [233.7741241455078, 628.5989990234375], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[10, 21, 0, 24, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.****************, "offset": [9.**************, -355.**************]}, "frontendVersion": "1.18.5", "node_versions": {"comfy-core": "0.3.29"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}