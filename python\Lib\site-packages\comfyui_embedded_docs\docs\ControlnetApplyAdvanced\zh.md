这个节点在新的 ComfyUI 版本中已被重命名为 Apply ControlNet 取代了旧版本 Apply ControlNet (OLD) 的名称,由于  原先的Apply ControlNet (OLD) 目前有点类似启用状态，所以关于本节点的最新文档已经调整到 `Apply ControlNet` 来进行说明

此节点基于图像和控制网模型对条件数据应用高级控制网变换。它允许对控制网对生成内容的影响进行微调调整，从而对条件进行更精确和多样化的修改。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `positive` | `CONDITIONING` | 将应用控制网变换的正面条件数据。它代表在生成内容中增强或保持的期望属性或特征。 |
| `negative` | `CONDITIONING` | 负面条件数据，代表要从生成内容中减少或移除的属性或特征。控制网变换也应用于这些数据，允许平衡调整内容的特性。 |
| `control_net` | `CONTROL_NET` | 控制网模型对于定义条件数据的具体调整和增强至关重要。它解释参考图像和强度参数以应用变换，通过修改正面和负面条件数据中的属性，显著影响最终输出。 |
| `image` | `IMAGE` | 作为控制网变换的参考图像。它影响控制网对条件数据的调整，引导特定特征的增强或抑制。 |
| `strength` | `FLOAT` | 标量值，确定控制网对条件数据的影响强度。更高的值会导致更显著的调整。 |
| `start_percent` | `FLOAT` | 控制网效果的起始百分比，允许在指定范围内逐步应用变换。 |
| `end_percent` | `FLOAT` | 控制网效果的结束百分比，定义变换应用的范围。这允许对调整过程进行更微妙的控制。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `positive` | `CONDITIONING` | 应用控制网变换后的修改正面条件数据，反映了基于输入参数所做增强。 |
| `negative` | `CONDITIONING` | 应用控制网变换后的修改负面条件数据，反映了基于输入参数对特定特征的抑制或移除。 |
