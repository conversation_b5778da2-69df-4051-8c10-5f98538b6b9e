{"id": "00000000-0000-0000-0000-000000000000", "revision": 0, "last_node_id": 54, "last_link_id": 22, "nodes": [{"id": 39, "type": "<PERSON>downNote", "pos": [-2950, 1080], "size": [380, 320], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 36, "type": "LoadImage", "pos": [-2550, 1070], "size": [270, 326], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["panda.webp", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 49, "type": "<PERSON>downNote", "pos": [-2260, 850], "size": [280, 150], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "File location", "properties": {}, "widgets_values": ["Output model will be auto saved to \"ComfyUI/output/\"\n\nOr you can use the export function in the preview 3D node to download it.\n\n---\n\n输出模型将自动保存到 “ComfyUI/output/”。\n\n或者你可以使用 3D 预览节点中的导出功能来下载它。"], "color": "#432", "bgcolor": "#653"}, {"id": 52, "type": "Preview3D", "pos": [-1610, 1530], "size": [600, 490], "flags": {}, "order": 7, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 20}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 53, "type": "Preview3D", "pos": [-960, 1530], "size": [600, 490], "flags": {}, "order": 8, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 21}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 54, "type": "Preview3D", "pos": [-320, 1530], "size": [600, 490], "flags": {}, "order": 10, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 22}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 51, "type": "Preview3D", "pos": [-2250, 1530], "size": [600, 490], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 19}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 35, "type": "TripoImageToModelNode", "pos": [-2260, 1070], "size": [278.25372314453125, 380], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 9}], "outputs": [{"name": "model_file", "type": "STRING", "links": [19]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": [11, 13]}], "properties": {"Node name for S&R": "TripoImageToModelNode"}, "widgets_values": ["v2.5-20250123", "None", true, true, 42, "default", 42, "standard", "original_image", -1, false], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "TripoTextureNode", "pos": [-1618.64404296875, 1070], "size": [270, 174], "flags": {}, "order": 5, "mode": 4, "inputs": [{"name": "model_task_id", "type": "MODEL_TASK_ID", "link": 11}], "outputs": [{"name": "model_file", "type": "STRING", "links": [20]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": []}], "properties": {"Node name for S&R": "TripoTextureNode"}, "widgets_values": [true, true, 42, "standard", "original_image"], "color": "#432", "bgcolor": "#653"}, {"id": 41, "type": "TripoRigNode", "pos": [-985.541748046875, 1070], "size": [222.439453125, 46], "flags": {}, "order": 6, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "MODEL_TASK_ID", "link": 13}], "outputs": [{"name": "model_file", "type": "STRING", "links": [21]}, {"name": "rig task_id", "type": "RIG_TASK_ID", "links": [14]}], "properties": {"Node name for S&R": "TripoRigNode"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 42, "type": "TripoRetargetNode", "pos": [-400, 1070], "size": [310.453125, 78], "flags": {}, "order": 9, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "RIG_TASK_ID", "link": 14}], "outputs": [{"name": "model_file", "type": "STRING", "links": [22]}, {"name": "retarget task_id", "type": "RETARGET_TASK_ID", "links": null}], "properties": {"Node name for S&R": "TripoRetargetNode"}, "widgets_values": ["preset:walk"], "color": "#432", "bgcolor": "#653"}], "links": [[9, 36, 0, 35, 0, "IMAGE"], [11, 35, 1, 40, 0, "MODEL_TASK_ID"], [13, 35, 1, 41, 0, "MODEL_TASK_ID"], [14, 41, 1, 42, 0, "RIG_TASK_ID"], [19, 35, 0, 51, 1, "STRING"], [20, 40, 0, 52, 1, "STRING"], [21, 41, 0, 53, 1, "STRING"], [22, 42, 0, 54, 1, "STRING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.313842837672126, "offset": [4208.168909662408, 101.71544337223187]}, "frontendVersion": "1.21.0"}, "version": 0.4}