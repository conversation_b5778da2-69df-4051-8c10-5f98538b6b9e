{"version": 3, "file": "AboutPanel-BKQisLWc.js", "sources": ["../../src/components/common/DeviceInfo.vue", "../../src/components/common/SystemStatsPanel.vue", "../../src/components/dialog/content/setting/AboutPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"grid grid-cols-2 gap-2\">\n    <template v-for=\"col in deviceColumns\" :key=\"col.field\">\n      <div class=\"font-medium\">\n        {{ col.header }}\n      </div>\n      <div>\n        {{ formatValue(props.device[col.field], col.field) }}\n      </div>\n    </template>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { DeviceStats } from '@/schemas/apiSchema'\nimport { formatSize } from '@/utils/formatUtil'\n\nconst props = defineProps<{\n  device: DeviceStats\n}>()\n\nconst deviceColumns: { field: keyof DeviceStats; header: string }[] = [\n  { field: 'name', header: 'Name' },\n  { field: 'type', header: 'Type' },\n  { field: 'vram_total', header: 'VRAM Total' },\n  { field: 'vram_free', header: 'VRAM Free' },\n  { field: 'torch_vram_total', header: 'Torch VRAM Total' },\n  { field: 'torch_vram_free', header: 'Torch VRAM Free' }\n]\n\nconst formatValue = (value: any, field: string) => {\n  if (\n    ['vram_total', 'vram_free', 'torch_vram_total', 'torch_vram_free'].includes(\n      field\n    )\n  ) {\n    return formatSize(value)\n  }\n  return value\n}\n</script>\n", "<template>\n  <div class=\"system-stats\">\n    <div class=\"mb-6\">\n      <h2 class=\"text-2xl font-semibold mb-4\">\n        {{ $t('g.systemInfo') }}\n      </h2>\n      <div class=\"grid grid-cols-2 gap-2\">\n        <template v-for=\"col in systemColumns\" :key=\"col.field\">\n          <div class=\"font-medium\">\n            {{ col.header }}\n          </div>\n          <div>{{ formatValue(systemInfo[col.field], col.field) }}</div>\n        </template>\n      </div>\n    </div>\n\n    <Divider />\n\n    <div>\n      <h2 class=\"text-2xl font-semibold mb-4\">\n        {{ $t('g.devices') }}\n      </h2>\n      <TabView v-if=\"props.stats.devices.length > 1\">\n        <TabPanel\n          v-for=\"device in props.stats.devices\"\n          :key=\"device.index\"\n          :header=\"device.name\"\n          :value=\"device.index\"\n        >\n          <DeviceInfo :device=\"device\" />\n        </TabPanel>\n      </TabView>\n      <DeviceInfo v-else :device=\"props.stats.devices[0]\" />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Divider from 'primevue/divider'\nimport TabPanel from 'primevue/tabpanel'\nimport TabView from 'primevue/tabview'\nimport { computed } from 'vue'\n\nimport DeviceInfo from '@/components/common/DeviceInfo.vue'\nimport type { SystemStats } from '@/schemas/apiSchema'\nimport { formatSize } from '@/utils/formatUtil'\n\nconst props = defineProps<{\n  stats: SystemStats\n}>()\n\nconst systemInfo = computed(() => ({\n  ...props.stats.system,\n  argv: props.stats.system.argv.join(' ')\n}))\n\nconst systemColumns: { field: keyof SystemStats['system']; header: string }[] =\n  [\n    { field: 'os', header: 'OS' },\n    { field: 'python_version', header: 'Python Version' },\n    { field: 'embedded_python', header: 'Embedded Python' },\n    { field: 'pytorch_version', header: 'Pytorch Version' },\n    { field: 'argv', header: 'Arguments' },\n    { field: 'ram_total', header: 'RAM Total' },\n    { field: 'ram_free', header: 'RAM Free' }\n  ]\n\nconst formatValue = (value: any, field: string) => {\n  if (['ram_total', 'ram_free'].includes(field)) {\n    return formatSize(value)\n  }\n  return value\n}\n</script>\n", "<template>\n  <PanelTemplate value=\"About\" class=\"about-container\">\n    <h2 class=\"text-2xl font-bold mb-2\">\n      {{ $t('g.about') }}\n    </h2>\n    <div class=\"space-y-2\">\n      <a\n        v-for=\"badge in aboutPanelStore.badges\"\n        :key=\"badge.url\"\n        :href=\"badge.url\"\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n        class=\"about-badge inline-flex items-center no-underline\"\n        :title=\"badge.url\"\n      >\n        <Tag class=\"mr-2\">\n          <template #icon>\n            <i :class=\"[badge.icon, 'mr-2 text-xl']\" />\n          </template>\n          {{ badge.label }}\n        </Tag>\n      </a>\n    </div>\n\n    <Divider />\n\n    <SystemStatsPanel\n      v-if=\"systemStatsStore.systemStats\"\n      :stats=\"systemStatsStore.systemStats\"\n    />\n  </PanelTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Divider from 'primevue/divider'\nimport Tag from 'primevue/tag'\nimport { onMounted } from 'vue'\n\nimport SystemStatsPanel from '@/components/common/SystemStatsPanel.vue'\nimport { useAboutPanelStore } from '@/stores/aboutPanelStore'\nimport { useSystemStatsStore } from '@/stores/systemStatsStore'\n\nimport PanelTemplate from './PanelTemplate.vue'\n\nconst systemStatsStore = useSystemStatsStore()\nconst aboutPanelStore = useAboutPanelStore()\n\nonMounted(async () => {\n  if (!systemStatsStore.systemStats) {\n    await systemStatsStore.fetchSystemStats()\n  }\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAiBA,UAAM,QAAQ;AAId,UAAM,gBAAgE;AAAA,MACpE,EAAE,OAAO,QAAQ,QAAQ,OAAO;AAAA,MAChC,EAAE,OAAO,QAAQ,QAAQ,OAAO;AAAA,MAChC,EAAE,OAAO,cAAc,QAAQ,aAAa;AAAA,MAC5C,EAAE,OAAO,aAAa,QAAQ,YAAY;AAAA,MAC1C,EAAE,OAAO,oBAAoB,QAAQ,mBAAmB;AAAA,MACxD,EAAE,OAAO,mBAAmB,QAAQ,kBAAkB;AAAA,IAAA;AAGlD,UAAA,cAAc,wBAAC,OAAY,UAAkB;AACjD,UACE,CAAC,cAAc,aAAa,oBAAoB,iBAAiB,EAAE;AAAA,QACjE;AAAA,MAAA,GAEF;AACA,eAAO,WAAW,KAAK;AAAA,MACzB;AACO,aAAA;AAAA,IAAA,GARW;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiBpB,UAAM,QAAQ;AAIR,UAAA,aAAa,SAAS,OAAO;AAAA,MACjC,GAAG,MAAM,MAAM;AAAA,MACf,MAAM,MAAM,MAAM,OAAO,KAAK,KAAK,GAAG;AAAA,IACtC,EAAA;AAEF,UAAM,gBACJ;AAAA,MACE,EAAE,OAAO,MAAM,QAAQ,KAAK;AAAA,MAC5B,EAAE,OAAO,kBAAkB,QAAQ,iBAAiB;AAAA,MACpD,EAAE,OAAO,mBAAmB,QAAQ,kBAAkB;AAAA,MACtD,EAAE,OAAO,mBAAmB,QAAQ,kBAAkB;AAAA,MACtD,EAAE,OAAO,QAAQ,QAAQ,YAAY;AAAA,MACrC,EAAE,OAAO,aAAa,QAAQ,YAAY;AAAA,MAC1C,EAAE,OAAO,YAAY,QAAQ,WAAW;AAAA,IAAA;AAGtC,UAAA,cAAc,wBAAC,OAAY,UAAkB;AACjD,UAAI,CAAC,aAAa,UAAU,EAAE,SAAS,KAAK,GAAG;AAC7C,eAAO,WAAW,KAAK;AAAA,MACzB;AACO,aAAA;AAAA,IAAA,GAJW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvBpB,UAAM,mBAAmB;AACzB,UAAM,kBAAkB;AAExB,cAAU,YAAY;AAChB,UAAA,CAAC,iBAAiB,aAAa;AACjC,cAAM,iBAAiB;MACzB;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}