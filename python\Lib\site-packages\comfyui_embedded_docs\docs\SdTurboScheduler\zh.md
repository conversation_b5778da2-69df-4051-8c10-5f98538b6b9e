
SDTurbo调度器旨在为图像采样生成一系列sigma值，根据指定的去噪级别和步骤数调整序列。它利用特定模型的采样能力来产生这些sigma值，这些值对于控制在图像生成过程中的去噪过程至关重要。

## 输入

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| `model`    | `MODEL`  | 模型参数指定用于生成sigma值的生成模型。这对于确定调度器的具体采样行为和能力至关重要。 |
| `steps`    | `INT`    | 步骤参数决定了要生成的sigma序列的长度，直接影响去噪过程的粒度。 |
| `denoise`  | `FLOAT`  | 去噪参数调整sigma序列的起始点，允许在图像生成期间对应用的去噪级别进行更精细的控制。 |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `sigmas` | `SIGMAS`| 根据指定的模型、步骤和去噪级别生成的一系列sigma值。这些值对于控制在图像生成过程中的去噪过程至关重要。 |
