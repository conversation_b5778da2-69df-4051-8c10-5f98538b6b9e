이 노드는 비디오 생성을 위한 <PERSON>ba<PERSON> Wan Fun Control 모델을 지원하기 위해 추가되었으며, [이 커밋](https://github.com/comfyanonymous/ComfyUI/commit/3661c833bcc41b788a7c9f0e7bc48524f8ee5f82) 이후에 추가되었습니다.

- **목적:** Wan 2.1 Fun Control 모델을 사용하여 비디오 생성을 위해 필요한 조건 정보를 준비합니다.

WanFunControlToVideo 노드는 비디오 생성을 위한 Wan Fun Control 모델을 지원하도록 설계된 ComfyUI의 추가 기능으로, 비디오 제작을 위해 WanFun 제어를 활용하는 것을 목표로 합니다.

이 노드는 필수 조건 정보를 준비하는 지점 역할을 하며, 잠재 공간의 중심점을 초기화하여 Wan 2.1 Fun 모델을 사용하여 후속 비디오 생성 프로세스를 안내합니다. 노드의 이름은 그 기능을 명확하게 나타내며, 다양한 입력을 수용하고 이를 WanFun 프레임워크 내에서 비디오 생성을 제어하는 데 적합한 형식으로 변환합니다.

노드의 ComfyUI 노드 계층 내 위치는 비디오 생성 파이프라인의 초기 단계에서 작동하며, 실제 샘플링이나 비디오 프레임의 디코딩 전에 조건 신호를 조작하는 데 중점을 둡니다.

## 입력

| 매개변수 이름      | 필수 | 데이터 유형           | 설명                                                  | 기본값 |
|:-------------------|:---------|:-------------------|:-------------------------------------------------------------|:-------------|
| positive           | 예      | CONDITIONING       | 표준 ComfyUI 긍정 조건 데이터로, 일반적으로 "CLIP Text Encode" 노드에서 가져옵니다. 긍정 프롬프트는 사용자가 생성할 비디오의 내용, 주제 및 예술 스타일을 설명합니다. | N/A  |
| negative           | 예      | CONDITIONING       | 표준 ComfyUI 부정 조건 데이터로, 일반적으로 "CLIP Text Encode" 노드에 의해 생성됩니다. 부정 프롬프트는 사용자가 생성할 비디오에서 피하고 싶은 요소, 스타일 또는 아티팩트를 지정합니다. | N/A  |
| vae                | 예      | VAE                | Wan 2.1 Fun 모델 패밀리와 호환되는 VAE(변분 오토인코더) 모델이 필요하며, 이미지/비디오 데이터의 인코딩 및 디코딩에 사용됩니다. | N/A  |
| width              | 예      | INT                | 출력 비디오 프레임의 원하는 너비(픽셀 단위)로, 기본값은 832, 최소값은 16, 최대값은 nodes.MAX_RESOLUTION에 의해 결정되며, 단계 크기는 16입니다. | 832  |
| height             | 예      | INT                | 출력 비디오 프레임의 원하는 높이(픽셀 단위)로, 기본값은 480, 최소값은 16, 최대값은 nodes.MAX_RESOLUTION에 의해 결정되며, 단계 크기는 16입니다. | 480  |
| length             | 예      | INT                | 생성된 비디오의 총 프레임 수로, 기본값은 81, 최소값은 1, 최대값은 nodes.MAX_RESOLUTION에 의해 결정되며, 단계 크기는 4입니다. | 81   |
| batch_size         | 예      | INT                | 한 번에 생성되는 비디오 수로, 기본값은 1, 최소값은 1, 최대값은 4096입니다. | 1    |
| clip_vision_output | 아니오       | CLIP_VISION_OUTPUT | (선택 사항) CLIP 비전 모델에 의해 추출된 시각적 특징으로, 시각적 스타일 및 콘텐츠 안내를 가능하게 합니다. | 없음 |
| start_image        | 아니오       | IMAGE              | (선택 사항) 생성된 비디오의 시작에 영향을 미치는 초기 이미지입니다. | 없음 |
| control_video      | 아니오       | IMAGE              | (선택 사항) 사용자가 생성된 비디오의 움직임과 잠재적 구조를 안내할 수 있도록 전처리된 ControlNet 참조 비디오를 제공할 수 있게 합니다.| 없음 |

## 출력

| 매개변수 이름      | 데이터 유형           | 설명                                                  |
|:-------------------|:-------------------|:-------------------------------------------------------------|
| positive           | CONDITIONING       | 인코딩된 start_image와 control_video를 포함한 향상된 긍정 조건 데이터를 제공합니다. |
| negative           | CONDITIONING       | 동일한 concat_latent_image를 포함한 향상된 부정 조건 데이터를 제공합니다. |
| latent             | LATENT             | "samples" 키를 가진 빈 잠재 텐서를 포함하는 사전입니다. |
