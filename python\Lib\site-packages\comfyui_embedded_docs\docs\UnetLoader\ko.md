UNETLoader 노드는 이름으로 U-Net 모델을 로드하도록 설계되어 시스템 내에서 사전 훈련된 U-Net 아키텍처를 쉽게 사용할 수 있게 합니다.

## 입력

| 매개변수       | 데이터 유형   | 설명                                                                                                                                                        |
| -------------- | ------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `unet_name`    | COMBO[STRING] | 로드할 U-Net 모델의 이름을 지정합니다. 이 이름은 미리 정의된 디렉토리 구조 내에서 모델을 찾는 데 사용되며, 다양한 U-Net 모델의 동적 로딩을 가능하게 합니다. |
| `weight_dtype` | ...           | 🚧  fp8_e4m3fn fp9_e5m2                                                                                                                                      |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                 |
| -------- | ----------- | ------------------------------------------------------------------------------------ |
| `model`  | MODEL       | 로드된 U-Net 모델을 반환하여 시스템 내에서 추가 처리나 추론에 사용할 수 있게 합니다. |
