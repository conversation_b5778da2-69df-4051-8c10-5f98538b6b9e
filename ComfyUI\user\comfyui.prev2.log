## ComfyUI-Manager: installing dependencies done.
[2025-07-28 19:29:05.859] ** ComfyUI startup time: 2025-07-28 19:29:05.859
[2025-07-28 19:29:05.859] ** Platform: Windows
[2025-07-28 19:29:05.859] ** Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-28 19:29:05.859] ** Python executable: D:\AI\koutu\python\python.exe
[2025-07-28 19:29:05.859] ** ComfyUI Path: D:\AI\koutu\ComfyUI
[2025-07-28 19:29:05.859] ** ComfyUI Base Folder Path: D:\AI\koutu\ComfyUI
[2025-07-28 19:29:05.859] ** User directory: D:\AI\koutu\ComfyUI\user
[2025-07-28 19:29:05.859] ** ComfyUI-Manager config path: D:\AI\koutu\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-28 19:29:05.859] ** Log path: D:\AI\koutu\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-28 19:29:09.615]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
[2025-07-28 19:29:09.615]    7.6 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager
[2025-07-28 19:29:09.615] 
[2025-07-28 19:29:23.218] Checkpoint files will always be loaded safely.
[2025-07-28 19:29:23.500] Total VRAM 24564 MB, total RAM 65149 MB
[2025-07-28 19:29:23.500] pytorch version: 2.5.1+cu124
[2025-07-28 19:29:26.408] xformers version: 0.0.28.post3
[2025-07-28 19:29:26.408] Set vram state to: NORMAL_VRAM
[2025-07-28 19:29:26.408] Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
[2025-07-28 19:29:27.056] Using xformers attention
[2025-07-28 19:29:28.838] Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-28 19:29:28.838] ComfyUI version: 0.3.45
[2025-07-28 19:29:28.935] ComfyUI frontend version: 1.23.4
[2025-07-28 19:29:28.937] [Prompt Server] web root: D:\AI\koutu\python\Lib\site-packages\comfyui_frontend_package\static
[2025-07-28 19:29:30.023] [AnimateDiffEvo] - [0;31mERROR[0m - No motion models found. Please download one and place in: ['D:\\AI\\koutu\\ComfyUI\\custom_nodes\\comfyui-animatediff-evolved\\models', 'D:\\AI\\koutu\\ComfyUI\\models\\animatediff_models']
[2025-07-28 19:29:30.385] [Crystools [0;32mINFO[0m] Crystools version: 1.22.1
[2025-07-28 19:29:30.407] [Crystools [0;32mINFO[0m] CPU: Intel(R) Core(TM) Ultra 9 285K - Arch: AMD64 - OS: Windows 10
[2025-07-28 19:29:30.427] [Crystools [0;32mINFO[0m] Pynvml (Nvidia) initialized.
[2025-07-28 19:29:30.427] [Crystools [0;32mINFO[0m] GPU/s:
[2025-07-28 19:29:30.438] [Crystools [0;32mINFO[0m] 0) NVIDIA GeForce RTX 4090 D
[2025-07-28 19:29:30.438] [Crystools [0;32mINFO[0m] NVIDIA Driver: 576.02
[2025-07-28 19:29:31.198] Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\__init__.py", line 1, in <module>
    from .nodes import NODE_CLASS_MAPPINGS as NODES_CLASS, NODE_DISPLAY_NAME_MAPPINGS as NODES_DISPLAY
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\nodes.py", line 10, in <module>
    from .hyvideo.text_encoder import TextEncoder
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\hyvideo\text_encoder\__init__.py", line 9, in <module>
    from .processing_llava import LlavaProcessor
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\hyvideo\text_encoder\processing_llava.py", line 23, in <module>
    from transformers.processing_utils import ProcessingKwargs, ProcessorMixin, Unpack, _validate_images_text_input_order
ImportError: cannot import name '_validate_images_text_input_order' from 'transformers.processing_utils' (D:\AI\koutu\python\Lib\site-packages\transformers\processing_utils.py)

[2025-07-28 19:29:31.199] Cannot import D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper module for custom nodes: cannot import name '_validate_images_text_input_order' from 'transformers.processing_utils' (D:\AI\koutu\python\Lib\site-packages\transformers\processing_utils.py)
[2025-07-28 19:29:31.201] ### Loading: ComfyUI-Impact-Pack (V8.8.5)
[2025-07-28 19:29:31.627] [Impact Pack] Wildcards loading done.
[2025-07-28 19:29:31.638] ### Loading: ComfyUI-Inspire-Pack (V1.14.1)
[2025-07-28 19:29:31.704] Total VRAM 24564 MB, total RAM 65149 MB
[2025-07-28 19:29:31.704] pytorch version: 2.5.1+cu124
[2025-07-28 19:29:31.704] xformers version: 0.0.28.post3
[2025-07-28 19:29:31.706] Set vram state to: NORMAL_VRAM
[2025-07-28 19:29:31.706] Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
[2025-07-28 19:29:31.939] ### Loading: ComfyUI-Manager (V3.31.1)
[2025-07-28 19:29:31.939] [ComfyUI-Manager] network_mode: public
[2025-07-28 19:29:32.394] ### ComfyUI Version: v0.3.45-25-ge6d9f627 | Released on '2025-07-27'
[2025-07-28 19:29:32.968] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-28 19:29:32.969] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-28 19:29:33.051] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-28 19:29:33.152] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-28 19:29:33.163] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: TensorrtExecutionProvider, CUDAExecutionProvider, CPUExecutionProvider
[2025-07-28 19:29:33.164] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-07-28 19:29:33.170] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-28 19:29:33.171] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-28 19:29:33.172] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-28 19:29:33.179] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-28 19:29:33.697] DWPose: Onnxruntime with acceleration providers detected
[2025-07-28 19:29:33.784] 
[36mEfficiency Nodes:[0m Attempting to add Control Net options to the 'HiRes-Fix Script' Node (comfyui_controlnet_aux add-on)...[92mSuccess![0m
[2025-07-28 19:29:36.293] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-28 19:29:36.293] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-28 19:29:37.906] FETCH ComfyRegistry Data: 5/92
[2025-07-28 19:29:38.041] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-28 19:29:38.041] 
	[3m[93m"Art is the universal language that transcends boundaries and speaks to all."[0m[3m - Unknown[0m
[2025-07-28 19:29:38.041] 
[2025-07-28 19:29:38.062] 
[2025-07-28 19:29:38.062] [92m[rgthree-comfy] Loaded 42 extraordinary nodes. 🎉[00m
[2025-07-28 19:29:38.062] 
[2025-07-28 19:29:38.066] 
Import times for custom nodes:
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\sd-dynamic-thresholding
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-WD14-Tagger
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_essentials
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_UltimateSDUpscale
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\AIGODLIKE-COMFYUI-TRANSLATION
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-advanced-controlnet
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-animatediff-evolved
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-07-28 19:29:38.066]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Inspire-Pack
[2025-07-28 19:29:38.066]    0.1 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-layerdiffuse
[2025-07-28 19:29:38.066]    0.2 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-KJNodes
[2025-07-28 19:29:38.066]    0.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-crystools
[2025-07-28 19:29:38.066]    0.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Impact-Pack
[2025-07-28 19:29:38.066]    0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-VideoHelperSuite
[2025-07-28 19:29:38.066]    0.6 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-28 19:29:38.066]    0.7 seconds (IMPORT FAILED): D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper
[2025-07-28 19:29:38.066]    0.7 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager
[2025-07-28 19:29:38.066]    4.3 seconds: D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894
[2025-07-28 19:29:38.066] 
[2025-07-28 19:29:38.388] Context impl SQLiteImpl.
[2025-07-28 19:29:38.388] Will assume non-transactional DDL.
[2025-07-28 19:29:38.389] No target revision found.
[2025-07-28 19:29:38.412] Starting server

[2025-07-28 19:29:38.413] To see the GUI go to: http://127.0.0.1:8188
[2025-07-28 19:29:42.077] FETCH ComfyRegistry Data: 10/92
[2025-07-28 19:29:43.857] got prompt
[2025-07-28 19:29:44.656] [RMBG ERROR] Error in batch processing: BiRefNet.__init__() got an unexpected keyword argument 'tie_weights'
[2025-07-28 19:29:44.656] [RMBG ERROR] Error in image processing: Error in batch processing: BiRefNet.__init__() got an unexpected keyword argument 'tie_weights'
[2025-07-28 19:29:44.683] !!! Exception during processing !!! Error in image processing: Error in batch processing: BiRefNet.__init__() got an unexpected keyword argument 'tie_weights'
[2025-07-28 19:29:44.690] Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 162, in load_model
    self.model = AutoModelForImageSegmentation.from_pretrained(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\auto_factory.py", line 593, in from_pretrained
    return model_class.from_pretrained(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\modeling_utils.py", line 315, in _wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\modeling_utils.py", line 4933, in from_pretrained
    model.tie_weights()
  File "D:\AI\koutu\python\Lib\site-packages\transformers\modeling_utils.py", line 2943, in tie_weights
    if getattr(self.config.get_text_config(decoder=True), "tie_word_embeddings", True):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Config' object has no attribute 'get_text_config'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 189, in process_image
    self.load_model(model_name)
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 170, in load_model
    self.model = AutoModelForImageSegmentation.from_pretrained(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\auto_factory.py", line 593, in from_pretrained
    return model_class.from_pretrained(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\modeling_utils.py", line 315, in _wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\modeling_utils.py", line 4927, in from_pretrained
    model = cls(config, *model_args, **model_kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: BiRefNet.__init__() got an unexpected keyword argument 'tie_weights'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 528, in process_image
    mask = model_instance.process_image(img, model, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 232, in process_image
    handle_model_error(f"Error in batch processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 88, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in batch processing: BiRefNet.__init__() got an unexpected keyword argument 'tie_weights'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "D:\AI\koutu\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 596, in process_image
    handle_model_error(f"Error in image processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 88, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in image processing: Error in batch processing: BiRefNet.__init__() got an unexpected keyword argument 'tie_weights'

[2025-07-28 19:29:44.690] Prompt executed in 0.83 seconds
[2025-07-28 19:29:46.364] FETCH ComfyRegistry Data: 15/92
[2025-07-28 19:29:51.142] FETCH ComfyRegistry Data: 20/92
[2025-07-28 19:29:55.236] FETCH ComfyRegistry Data: 25/92
[2025-07-28 19:29:59.779] FETCH ComfyRegistry Data: 30/92
[2025-07-28 19:30:03.985] FETCH ComfyRegistry Data: 35/92
[2025-07-28 19:30:07.872] FETCH ComfyRegistry Data: 40/92
[2025-07-28 19:30:11.893] FETCH ComfyRegistry Data: 45/92
[2025-07-28 19:30:16.704] FETCH ComfyRegistry Data: 50/92
[2025-07-28 19:30:20.767] FETCH ComfyRegistry Data: 55/92
[2025-07-28 19:30:25.749] FETCH ComfyRegistry Data: 60/92
[2025-07-28 19:30:30.052] FETCH ComfyRegistry Data: 65/92
[2025-07-28 19:30:34.160] FETCH ComfyRegistry Data: 70/92
[2025-07-28 19:30:38.306] FETCH ComfyRegistry Data: 75/92
[2025-07-28 19:30:42.570] FETCH ComfyRegistry Data: 80/92
[2025-07-28 19:30:47.571] FETCH ComfyRegistry Data: 85/92
[2025-07-28 19:30:51.753] FETCH ComfyRegistry Data: 90/92
[2025-07-28 19:30:54.007] FETCH ComfyRegistry Data [DONE]
[2025-07-28 19:30:54.094] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-28 19:30:54.127] nightly_channel: 
[2025-07-28 19:30:54.128] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-07-28 19:30:54.128] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-28 19:30:54.327] [ComfyUI-Manager] All startup tasks have been completed.
