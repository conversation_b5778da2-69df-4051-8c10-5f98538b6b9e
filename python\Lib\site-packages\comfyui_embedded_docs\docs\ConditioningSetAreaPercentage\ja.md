ConditioningSetAreaPercentageノードは、条件要素の影響範囲を割合値に基づいて調整することに特化しています。画像全体のサイズに対する割合としてエリアの寸法と位置を指定でき、条件効果の強度を調整するためのパラメータも備えています。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `CONDITIONING` | CONDITIONING | 修正される条件要素を表し、エリアと強度の調整を適用する基礎として機能します。 |
| `width`   | `FLOAT`     | エリアの幅を画像全体の幅の割合として指定し、条件が水平方向にどれだけ影響を与えるかを決定します。 |
| `height`  | `FLOAT`     | エリアの高さを画像全体の高さの割合として決定し、条件の影響が垂直方向にどれだけ及ぶかを決定します。 |
| `x`       | `FLOAT`     | エリアの水平開始点を画像全体の幅の割合として示し、条件効果の位置を決定します。 |
| `y`       | `FLOAT`     | エリアの垂直開始点を画像全体の高さの割合として指定し、条件効果の位置を決定します。 |
| `strength`| `FLOAT`     | 指定されたエリア内での条件効果の強度を制御し、その影響を微調整することができます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `CONDITIONING` | CONDITIONING | 更新されたエリアと強度パラメータを持つ修正された条件要素を返し、さらなる処理や適用の準備が整っています。 |
