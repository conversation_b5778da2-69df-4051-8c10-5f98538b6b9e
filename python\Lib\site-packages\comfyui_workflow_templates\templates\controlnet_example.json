{"id": "4fa99a93-08af-4ce1-b32d-8d0285efeb01", "revision": 0, "last_node_id": 36, "last_link_id": 59, "nodes": [{"id": 6, "type": "CLIPTextEncode", "pos": [280, -310], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 21}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [53]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Masterpiece,best quality,high definition,high level of detail,3D,3D style,cute Q-version,<PERSON><PERSON>,a vibrant product photo created for innovative advertising,featuring a little boy with black hair and a big laugh,<PERSON>,holding a wooden crate,wooden crate,pasture,blue sky,white clouds,brick paved path,with pleasant houses in the background,chimneys,big trees,maple leaves,maple trees,and a lot of wheat,wheat,wheat ears along the roadside,The atmosphere of autumn,yellow grass and leaves,fallen leaves,fences,barriers,orange and yellow colors,autumn,windows,doors,gravel roads,captured using a Sony Alpha A7R IV camera with a 35mm f/1.4 lens,aperture set to f/2.8,shutter speed of 1/100 second,"], "color": "#232", "bgcolor": "#353"}, {"id": 14, "type": "CheckpointLoaderSimple", "pos": [-150, -290], "size": [380, 100], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [19]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [20, 21]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "DreamShaper_8_pruned.safetensors", "url": "https://huggingface.co/Lykon/DreamShaper/resolve/main/DreamShaper_8_pruned.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["DreamShaper_8_pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 5, "type": "EmptyLatentImage", "pos": [-150, 150], "size": [300, 110], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1], "color": "#323", "bgcolor": "#535"}, {"id": 11, "type": "LoadImage", "pos": [-140, 380], "size": [387.97003173828125, 465.5097961425781], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [57]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (2).png", "image"]}, {"id": 8, "type": "VAEDecode", "pos": [1220, -330], "size": [210, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 7}, {"label": "vae", "name": "vae", "type": "VAE", "link": 14}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1100, -150], "size": [315, 474], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 19}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 55}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 56}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [599575228983496, "randomize", 20, 6, "euler", "normal", 1]}, {"id": 9, "type": "SaveImage", "pos": [1450, -330], "size": [600, 660], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 35, "type": "PreviewImage", "pos": [280, 520], "size": [350, 320], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 58}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 33, "type": "<PERSON>downNote", "pos": [-520, -350], "size": [336, 152], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [ControlNet - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/controlnet/) — Overview\n> \n> [ControlNet Usage - docs.comfy.org](https://docs.comfy.org/tutorials/controlnet/controlnet) — Explanation of concepts and step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}, {"id": 34, "type": "<PERSON><PERSON>", "pos": [280, 380], "size": [270, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 57}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [58, 59]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "<PERSON><PERSON>"}, "widgets_values": [0.3500000000000001, 0.8], "color": "#322", "bgcolor": "#533"}, {"id": 13, "type": "VAELoader", "pos": [-150, -140], "size": [380, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [14]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAELoader", "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "ControlNetLoader", "pos": [-150, -30], "size": [370, 60], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ControlNetLoader", "models": [{"name": "control_v11p_sd15_scribble_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_scribble_fp16.safetensors?download=true", "directory": "controlnet"}]}, "widgets_values": ["control_v11p_sd15_scribble_fp16.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "CLIPTextEncode", "pos": [280, -100], "size": [420, 130], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 20}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [54]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis)"], "color": "#223", "bgcolor": "#335"}, {"id": 36, "type": "<PERSON>downNote", "pos": [290, 200], "size": [330, 88], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Select the Canny node, and click the \"?\" on the selection toolbox to learn about the Canny preprocessor."], "color": "#432", "bgcolor": "#653"}, {"id": 32, "type": "ControlNetApplyAdvanced", "pos": [740, 110], "size": [315, 186], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 53}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 54}, {"label": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 51}, {"label": "image", "name": "image", "type": "IMAGE", "link": 59}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [55]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [56]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1], "color": "#322", "bgcolor": "#533"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [14, 13, 0, 8, 1, "VAE"], [19, 14, 0, 3, 0, "MODEL"], [20, 14, 1, 7, 0, "CLIP"], [21, 14, 1, 6, 0, "CLIP"], [51, 12, 0, 32, 2, "CONTROL_NET"], [53, 6, 0, 32, 0, "CONDITIONING"], [54, 7, 0, 32, 1, "CONDITIONING"], [55, 32, 0, 3, 1, "CONDITIONING"], [56, 32, 1, 3, 2, "CONDITIONING"], [57, 11, 0, 34, 0, "IMAGE"], [58, 34, 0, 35, 0, "IMAGE"], [59, 34, 0, 32, 3, "IMAGE"]], "groups": [{"id": 1, "title": "Step1 - Load models", "bounding": [-160, -380, 410, 440], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step 2 - Upload and preprocess the image", "bounding": [-160, 300, 820, 560], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - Prompt", "bounding": [270, -380, 440, 440], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Image size", "bounding": [-160, 80, 320, 193.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5713283933319003, "offset": [1616.4480513827432, 985.783318374038]}, "node_versions": {"comfy-core": "0.3.14"}, "frontendVersion": "1.23.4"}, "version": 0.4}