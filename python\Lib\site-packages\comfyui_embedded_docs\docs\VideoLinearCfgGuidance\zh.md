
这个节点在 ComfyUI 中的作用是稍微改善视频模型的采样过程。它通过在不同帧之间线性缩放 cfg 参数来实现这一点。具体来说，远离初始帧的帧会逐渐获得更高的 cfg 值，从而使得视频的后续帧与初始帧相比，会有更明显的视觉效果变化。这种方法有助于生成更加流畅和动态的视频内容

## Input 输入

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| model      | MODEL    | 应用线性CFG引导的视频模型，定义基础模型并用引导比例尺修改。 |
| min_cfg    | FLOAT    | 指定线性比例尺调整的最小条件引导比例尺，影响模型输出的下限。 |

## Output 输出

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| model      | MODEL    | 输出修改后的模型，应用了线性CFG引导比例尺，可生成不同条件化程度的输出。 |
