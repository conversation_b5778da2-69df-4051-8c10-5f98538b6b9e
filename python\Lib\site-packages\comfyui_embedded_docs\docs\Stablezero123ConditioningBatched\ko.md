
이 노드는 StableZero123 모델에 맞춰 배치 방식으로 조건 정보를 처리하도록 설계되었습니다. 여러 세트의 조건 데이터를 동시에 효율적으로 처리하여 배치 처리가 중요한 시나리오에서 워크플로우를 최적화합니다.

## 입력

| 매개변수                    | 데이터 유형   | 설명                                                                                 |
| --------------------------- | ------------- | ------------------------------------------------------------------------------------ |
| `clip_vision`               | `CLIP_VISION` | 조건 처리 과정에 시각적 컨텍스트를 제공하는 CLIP 비전 임베딩입니다.                  |
| `init_image`                | `IMAGE`       | 생성 과정의 시작점으로 사용되는 초기 이미지입니다.                                   |
| `vae`                       | `VAE`         | 조건 처리 과정에서 이미지를 인코딩하고 디코딩하는 데 사용되는 변분 오토인코더입니다. |
| `width`                     | `INT`         | 출력 이미지의 너비입니다.                                                            |
| `height`                    | `INT`         | 출력 이미지의 높이입니다.                                                            |
| `batch_size`                | `INT`         | 한 번에 처리할 조건 세트의 수입니다.                                                 |
| `elevation`                 | `FLOAT`       | 3D 모델 조건에 대한 고도 각도로, 생성된 이미지의 시점을 결정합니다.                  |
| `azimuth`                   | `FLOAT`       | 3D 모델 조건에 대한 방위각으로, 생성된 이미지의 방향을 결정합니다.                   |
| `elevation_batch_increment` | `FLOAT`       | 배치 전반에 걸쳐 고도 각도의 점진적 변화로, 다양한 시점을 제공합니다.                |
| `azimuth_batch_increment`   | `FLOAT`       | 배치 전반에 걸쳐 방위각의 점진적 변화로, 다양한 방향을 제공합니다.                   |

## 출력

| 매개변수   | 데이터 유형    | 설명                                                                                |
| ---------- | -------------- | ----------------------------------------------------------------------------------- |
| `positive` | `CONDITIONING` | 생성된 콘텐츠에서 특정 기능이나 측면을 촉진하도록 조정된 긍정적 조건 출력입니다.    |
| `negative` | `CONDITIONING` | 생성된 콘텐츠에서 특정 기능이나 측면을 억제하도록 조정된 부정적 조건 출력입니다.    |
| `latent`   | `LATENT`       | 조건 처리 과정에서 파생된 잠재 표현으로, 추가 처리나 생성 단계에 준비되어 있습니다. |
