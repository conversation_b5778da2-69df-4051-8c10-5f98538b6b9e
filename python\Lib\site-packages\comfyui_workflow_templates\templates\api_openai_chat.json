{"id": "083c898c-0a6e-4f9a-b9c1-7e8ce36506da", "revision": 0, "last_node_id": 18, "last_link_id": 14, "nodes": [{"id": 2, "type": "PreviewAny", "pos": [-5390, 2080], "size": [490, 350], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "source", "type": "*", "link": 2}], "outputs": [], "properties": {"Node name for S&R": "PreviewAny"}, "widgets_values": []}, {"id": 4, "type": "OpenAIChatConfig", "pos": [-6230, 2420], "size": [390, 230], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "OPENAI_CHAT_CONFIG", "type": "OPENAI_CHAT_CONFIG", "links": [3]}], "properties": {"Node name for S&R": "OpenAIChatConfig"}, "widgets_values": ["auto", 4096, "You are an AI specialized in image-to-text prompt generation. Analyze the given image carefully and describe it in a detailed, structured, and prompt-friendly format. Include elements such as subject, clothing, style, environment, lighting, composition, color palette, mood, camera angle, and any other relevant visual details. Your output should resemble a high-quality text-to-image prompt that can help recreate the image. Use concise, descriptive language. Do not include explanations, just output the prompt."]}, {"id": 3, "type": "OpenAIChatNode", "pos": [-5810, 2080], "size": [400, 580], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 4}, {"name": "files", "shape": 7, "type": "OPENAI_INPUT_FILES", "link": 7}, {"name": "advanced_options", "shape": 7, "type": "OPENAI_CHAT_CONFIG", "link": 3}], "outputs": [{"name": "STRING", "type": "STRING", "links": [2]}], "properties": {"Node name for S&R": "OpenAIChatNode"}, "widgets_values": ["Describe this image", false, "o1-pro"], "color": "#432", "bgcolor": "#653"}, {"id": 6, "type": "OpenAIInputFiles", "pos": [-6640, 2110], "size": [361.078125, 58], "flags": {}, "order": 1, "mode": 4, "inputs": [{"name": "OPENAI_INPUT_FILES", "shape": 7, "type": "OPENAI_INPUT_FILES", "link": null}], "outputs": [{"name": "OPENAI_INPUT_FILES", "type": "OPENAI_INPUT_FILES", "links": [6]}], "properties": {"Node name for S&R": "OpenAIInputFiles"}, "widgets_values": ["input.txt"]}, {"id": 7, "type": "OpenAIInputFiles", "pos": [-6640, 2220], "size": [361.078125, 58], "flags": {}, "order": 8, "mode": 4, "inputs": [{"name": "OPENAI_INPUT_FILES", "shape": 7, "type": "OPENAI_INPUT_FILES", "link": 6}], "outputs": [{"name": "OPENAI_INPUT_FILES", "type": "OPENAI_INPUT_FILES", "links": [7]}], "properties": {"Node name for S&R": "OpenAIInputFiles"}, "widgets_values": ["input.txt"]}, {"id": 13, "type": "LoadImage", "pos": [-6120, 2880], "size": [274.080078125, 314.0001220703125], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [14]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 11, "type": "LoadImage", "pos": [-6420, 2880], "size": [274.080078125, 314.0001220703125], "flags": {}, "order": 3, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [8]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 12, "type": "LoadImage", "pos": [-6720, 2880], "size": [274.080078125, 314.0001220703125], "flags": {}, "order": 4, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 10, "type": "ImageBatch", "pos": [-6290, 2760], "size": [140, 46], "flags": {}, "order": 9, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 8}, {"name": "image2", "type": "IMAGE", "link": 9}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [12]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 16, "type": "ImageBatch", "pos": [-5990, 2760], "size": [140, 46], "flags": {}, "order": 11, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 12}, {"name": "image2", "type": "IMAGE", "link": 14}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": null}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 9, "type": "<PERSON>downNote", "pos": [-6640, 2320], "size": [360, 150], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The corresponding files need to already exist in the **ComfyUI/input** folder. The supported formats are `.txt` and `.pdf`. However, we are currently improving this node, and in the future, we will use an uploadable node to replace it.  \n\n---\n\n需要对应的文件在 **ComfyUI/input** 文件夹下已经存在，支持 `.txt` 和 `.pdf` 格式，不过我们目前正在改进这一节点，后续将会使用可以上传的节点来替代"], "color": "#432", "bgcolor": "#653"}, {"id": 5, "type": "LoadImage", "pos": [-6230, 2050], "size": [370, 326], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 18, "type": "<PERSON>downNote", "pos": [-7060, 2060], "size": [390, 320], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 3, 0, 2, 0, "*"], [3, 4, 0, 3, 2, "OPENAI_CHAT_CONFIG"], [4, 5, 0, 3, 0, "IMAGE"], [6, 6, 0, 7, 0, "OPENAI_INPUT_FILES"], [7, 7, 0, 3, 1, "OPENAI_INPUT_FILES"], [8, 11, 0, 10, 0, "IMAGE"], [9, 12, 0, 10, 1, "IMAGE"], [12, 10, 0, 16, 0, "IMAGE"], [14, 13, 0, 16, 1, "IMAGE"]], "groups": [{"id": 1, "title": "Input Files", "bounding": [-6650, 2030, 390, 460], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Multiple images input example", "bounding": [-6730, 2670, 894.080078125, 537.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3138428376721386, "offset": [9247.864065025406, -1474.0947088085522]}, "frontendVersion": "1.21.0"}, "version": 0.4}