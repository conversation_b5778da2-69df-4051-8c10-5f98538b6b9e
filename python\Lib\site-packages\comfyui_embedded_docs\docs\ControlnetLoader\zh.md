该节点会检测位于 `ComfyUI/models/controlnet` 文件夹下的模型，同时也会读取你在 extra_model_paths.yaml 文件中配置的额外路径的模型，有时你可能需要 **刷新 ComfyUI 界面** 才能让它读取到对应文件夹下的模型文件

`ControlNetLoader`节点设计用于从指定路径加载一个ControlNet模型。它在初始化ControlNet模型中扮演着至关重要的角色，这些模型对于在生成内容或根据控制信号修改现有内容时应用控制机制是必不可少的。

## 输入

| 字段               | Comfy dtype        | 描述                                                                            |
|------------------|-------------------|-----------------------------------------------------------------------------------|
| `control_net_name`| `COMBO[STRING]`    | 指定要加载的ControlNet模型的名称，用于在预定义的目录结构中定位模型文件。 |

## 输出

| 字段            | Comfy dtype      | 描述                                                                   |
|---------------|-----------------|----------------------------------------------------------------------|
| `control_net` | `CONTROL_NET`   | 返回加载的ControlNet模型，准备用于控制或修改内容生成过程。 |
