{"id": "232828dd-a063-4f3c-8463-e1d361bdf7c4", "revision": 0, "last_node_id": 15, "last_link_id": 8, "nodes": [{"id": 14, "type": "SaveVideo", "pos": [-15.798379898071289, -632.7276000976562], "size": [456.74285888671875, 354.9178466796875], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 8}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"filename_prefix": true, "format": true, "codec": true}}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 13, "type": "LoadImage", "pos": [-782.451171875, -629.8445434570312], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [7]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"image": true, "upload": true}}, "widgets_values": ["nen<PERSON><PERSON><PERSON>_POV_of_a_man_reading_a_book._A_blue_glowing_butterf_27d1f7f0-2235-47a4-b561-4dfe8bb93dab_3.png", "image"]}, {"id": 15, "type": "<PERSON>downNote", "pos": [-1196.22265625, -622.950439453125], "size": [389.0563049316406, 249.69961547851562], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 12, "type": "PikaImageToVideoNode2_2", "pos": [-442.*************, -633.************], "size": [400, 238], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 7}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [8]}], "properties": {"Node name for S&R": "PikaImageToVideoNode2_2", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"prompt_text": true, "negative_prompt": true, "seed": true, "resolution": true, "duration": true}}, "widgets_values": ["Orbit left", "", 1875588813, "randomize", "1080p", 5], "color": "#322", "bgcolor": "#533"}], "links": [[7, 13, 0, 12, 0, "IMAGE"], [8, 12, 0, 14, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.0610764609500014, "offset": [1083.1381593220601, 896.6781296958575]}, "frontendVersion": "1.19.1", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}