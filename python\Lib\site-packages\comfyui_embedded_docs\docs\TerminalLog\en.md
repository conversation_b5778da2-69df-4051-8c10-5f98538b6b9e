Terminal Log (Manager) node is primarily used to display the running information of ComfyUI in the terminal within the ComfyUI interface. To use it, you need to set the `mode` to **logging** mode. This will allow it to record corresponding log information during the image generation task. If the `mode` is set to **stop** mode, it will not record log information.
When you access and use ComfyUI via remote connections or local area network connections, Terminal Log (Manager) node becomes particularly useful. It allows you to directly view error messages from the CMD within the ComfyUI interface, making it easier to understand the current status of ComfyUI's operation.
