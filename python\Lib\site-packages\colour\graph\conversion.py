"""
Automatic Colour Conversion Graph
=================================

Define the automatic colour conversion graph objects:

-   :func:`colour.describe_conversion_path`
-   :func:`colour.convert`
"""

from __future__ import annotations

import inspect
import re
import sys
import textwrap
from collections import namedtuple
from copy import copy
from functools import partial
from pprint import pformat

import numpy as np

import colour
import colour.models
from colour.appearance import (
    CAM16_to_XYZ,
    CAM_Specification_CAM16,
    CAM_Specification_CIECAM02,
    CAM_Specification_CIECAM16,
    CAM_Specification_Hellwig2022,
    CIECAM02_to_XYZ,
    CIECAM16_to_XYZ,
    Hellwig2022_to_XYZ,
    Kim2009_to_XYZ,
    XYZ_to_ATD95,
    XYZ_to_CAM16,
    XYZ_to_CIECAM02,
    XYZ_to_CIECAM16,
    XYZ_to_Hellwig2022,
    XYZ_to_<PERSON>,
    XYZ_to_Kim2009,
    XYZ_to_LLAB,
    XYZ_to_Nayatani95,
    XYZ_to_RLAB,
    XYZ_to_ZCAM,
    ZCAM_to_XYZ,
)
from colour.appearance.ciecam02 import CAM_KWARGS_CIECAM02_sRGB
from colour.colorimetry import (
    CCS_ILLUMINANTS,
    TVS_ILLUMINANTS_HUNTERLAB,
    colorimetric_purity,
    complementary_wavelength,
    dominant_wavelength,
    excitation_purity,
    lightness,
    luminance,
    luminous_efficacy,
    luminous_efficiency,
    luminous_flux,
    sd_to_XYZ,
    wavelength_to_XYZ,
    whiteness,
    yellowness,
)
from colour.hints import (
    Any,
    ArrayLike,
    Callable,
    List,
    Literal,
    NDArrayFloat,
    cast,
)
from colour.models import (
    COLOURSPACE_MODELS_POLAR_CONVERSIONS,
    CAM02LCD_to_JMh_CIECAM02,
    CAM02SCD_to_JMh_CIECAM02,
    CAM02UCS_to_JMh_CIECAM02,
    CAM16LCD_to_JMh_CAM16,
    CAM16SCD_to_JMh_CAM16,
    CAM16UCS_to_JMh_CAM16,
    CIE1960UCS_to_XYZ,
    CIE1976UCS_to_XYZ,
    CMY_to_CMYK,
    CMY_to_RGB,
    CMYK_to_CMY,
    DIN99_to_XYZ,
    HCL_to_RGB,
    HSL_to_RGB,
    HSV_to_RGB,
    Hunter_Lab_to_XYZ,
    Hunter_Rdab_to_XYZ,
    ICaCb_to_XYZ,
    ICtCp_to_XYZ,
    IgPgTg_to_XYZ,
    IHLS_to_RGB,
    IPT_Ragoo2021_to_XYZ,
    IPT_to_XYZ,
    JMh_CAM16_to_CAM16LCD,
    JMh_CAM16_to_CAM16SCD,
    JMh_CAM16_to_CAM16UCS,
    JMh_CIECAM02_to_CAM02LCD,
    JMh_CIECAM02_to_CAM02SCD,
    JMh_CIECAM02_to_CAM02UCS,
    Jzazbz_to_XYZ,
    Lab_to_XYZ,
    Luv_to_uv,
    Luv_to_XYZ,
    Luv_uv_to_xy,
    Oklab_to_XYZ,
    OSA_UCS_to_XYZ,
    Prismatic_to_RGB,
    ProLab_to_XYZ,
    RGB_Colourspace,
    RGB_COLOURSPACE_sRGB,
    RGB_luminance,
    RGB_to_CMY,
    RGB_to_HCL,
    RGB_to_HSL,
    RGB_to_HSV,
    RGB_to_IHLS,
    RGB_to_Prismatic,
    RGB_to_RGB,
    RGB_to_XYZ,
    RGB_to_YCbCr,
    RGB_to_YcCbcCrc,
    RGB_to_YCoCg,
    UCS_to_uv,
    UCS_to_XYZ,
    UCS_uv_to_xy,
    UVW_to_XYZ,
    XYZ_to_CIE1960UCS,
    XYZ_to_CIE1976UCS,
    XYZ_to_DIN99,
    XYZ_to_hdr_CIELab,
    XYZ_to_hdr_IPT,
    XYZ_to_Hunter_Lab,
    XYZ_to_Hunter_Rdab,
    XYZ_to_ICaCb,
    XYZ_to_ICtCp,
    XYZ_to_IgPgTg,
    XYZ_to_IPT,
    XYZ_to_IPT_Ragoo2021,
    XYZ_to_Jzazbz,
    XYZ_to_Lab,
    XYZ_to_Luv,
    XYZ_to_Oklab,
    XYZ_to_OSA_UCS,
    XYZ_to_ProLab,
    XYZ_to_RGB,
    XYZ_to_sRGB,
    XYZ_to_UCS,
    XYZ_to_UVW,
    XYZ_to_xy,
    XYZ_to_xyY,
    XYZ_to_Yrg,
    YCbCr_to_RGB,
    YcCbcCrc_to_RGB,
    YCoCg_to_RGB,
    Yrg_to_XYZ,
    cctf_decoding,
    cctf_encoding,
    hdr_CIELab_to_XYZ,
    hdr_IPT_to_XYZ,
    sRGB_to_XYZ,
    uv_to_Luv,
    uv_to_UCS,
    xy_to_Luv_uv,
    xy_to_UCS_uv,
    xy_to_xyY,
    xy_to_XYZ,
    xyY_to_xy,
    xyY_to_XYZ,
)
from colour.notation import (
    HEX_to_RGB,
    RGB_to_HEX,
    keyword_to_RGB_CSSColor3,
    munsell_colour_to_xyY,
    munsell_value,
    xyY_to_munsell_colour,
)
from colour.quality import colour_quality_scale, colour_rendering_index
from colour.recovery import XYZ_to_sd
from colour.temperature import CCT_to_mired, CCT_to_uv, mired_to_CCT, uv_to_CCT
from colour.utilities import (
    as_float_array,
    domain_range_scale,
    filter_kwargs,
    message_box,
    required,
    tsplit,
    tstack,
    validate_method,
    zeros,
)

__author__ = "Colour Developers"
__copyright__ = "Copyright 2013 Colour Developers"
__license__ = "BSD-3-Clause - https://opensource.org/licenses/BSD-3-Clause"
__maintainer__ = "Colour Developers"
__email__ = "<EMAIL>"
__status__ = "Production"

__all__ = [
    "Conversion_Specification",
    "CIECAM02_to_JMh_CIECAM02",
    "JMh_CIECAM02_to_CIECAM02",
    "CAM16_to_JMh_CAM16",
    "JMh_CAM16_to_CAM16",
    "Hellwig2022_to_JMh_Hellwig2022",
    "JMh_Hellwig2022_to_Hellwig2022",
    "XYZ_to_luminance",
    "RGB_luminance_to_RGB",
    "CCT_D_uv_to_mired",
    "mired_to_CCT_D_uv",
    "CONVERSION_SPECIFICATIONS_DATA",
    "CONVERSION_GRAPH_NODE_LABELS",
    "CONVERSION_SPECIFICATIONS",
    "CONVERSION_GRAPH",
    "describe_conversion_path",
    "convert",
]


class Conversion_Specification(
    namedtuple("Conversion_Specification", ("source", "target", "conversion_function"))
):
    """
    Conversion specification for *Colour* graph for automatic colour
    conversion describing two nodes and the edge in the graph.

    Parameters
    ----------
    source
        Source node in the graph.
    target
        Target node in the graph.
    conversion_function
        Callable converting from the ``source`` node to the ``target`` node.
    """

    def __new__(cls, source: str, target: str, conversion_function: Callable):
        """
        Return a new instance of the
        :class:`colour.graph.conversion.Conversion_Specification` class.
        """

        return super().__new__(cls, source.lower(), target.lower(), conversion_function)


def CIECAM02_to_JMh_CIECAM02(
    specification: CAM_Specification_CIECAM02,
) -> NDArrayFloat:
    """
    Convert from *CIECAM02* specification to *CIECAM02* :math:`JMh`
    correlates.

    Parameters
    ----------
    specification
        *CIECAM02* colour appearance model specification.

    Returns
    -------
    :class:`numpy.ndarray`
        *CIECAM02* :math:`JMh` correlates.

    Examples
    --------
    >>> specification = CAM_Specification_CIECAM02(
    ...     J=41.731091132513917, M=0.108842175669226, h=219.048432658311780
    ... )
    >>> CIECAM02_to_JMh_CIECAM02(specification)  # doctest: +ELLIPSIS
    array([  4.1731091...e+01,   1.0884217...e-01,   2.1904843...e+02])
    """

    return tstack(
        [
            cast(NDArrayFloat, specification.J),
            cast(NDArrayFloat, specification.M),
            cast(NDArrayFloat, specification.h),
        ]
    )


def JMh_CIECAM02_to_CIECAM02(JMh: ArrayLike) -> CAM_Specification_CIECAM02:
    """
    Convert from *CIECAM02* :math:`JMh` correlates to *CIECAM02*
    specification.

    Parameters
    ----------
    JMh
         *CIECAM02* :math:`JMh` correlates.

    Returns
    -------
    :class:`colour.CAM_Specification_CIECAM02`
        *CIECAM02* colour appearance model specification.

    Examples
    --------
    >>> import numpy as np
    >>> JMh = np.array([4.17310911e01, 1.08842176e-01, 2.19048433e02])
    >>> JMh_CIECAM02_to_CIECAM02(JMh)  # doctest: +ELLIPSIS
    CAM_Specification_CIECAM02(J=41.7310911..., C=None, h=219.0484329..., \
s=None, Q=None, M=0.1088421..., H=None, HC=None)
    """

    J, M, h = tsplit(JMh)

    return CAM_Specification_CIECAM02(J=J, M=M, h=h)


def CAM16_to_JMh_CAM16(specification) -> NDArrayFloat:
    """
    Convert from *CAM16* specification to *CAM16* :math:`JMh` correlates.

    Parameters
    ----------
    specification
        *CAM16* colour appearance model specification.

    Returns
    -------
    :class:`numpy.ndarray`
        *CAM16* :math:`JMh` correlates.

    Examples
    --------
    >>> specification = CAM_Specification_CAM16(
    ...     J=41.731207905126638, M=0.107436772335905, h=217.067959767393010
    ... )
    >>> CAM16_to_JMh_CAM16(specification)  # doctest: +ELLIPSIS
    array([  4.1731207...e+01,   1.0743677...e-01,   2.1706796...e+02])
    """

    return tstack([specification.J, specification.M, specification.h])


def JMh_CAM16_to_CAM16(JMh: ArrayLike) -> CAM_Specification_CAM16:
    """
    Convert from *CAM6* :math:`JMh` correlates to *CAM6* specification.

    Parameters
    ----------
    JMh
         *CAM6* :math:`JMh` correlates.

    Returns
    -------
    :class:`colour.CAM6_Specification`
        *CAM6* colour appearance model specification.

    Examples
    --------
    >>> import numpy as np
    >>> JMh = np.array([4.17312079e01, 1.07436772e-01, 2.17067960e02])
    >>> JMh_CAM16_to_CAM16(JMh)  # doctest: +ELLIPSIS
    CAM_Specification_CAM16(J=41.7312079..., C=None, h=217.06796..., s=None, \
Q=None, M=0.1074367..., H=None, HC=None)
    """

    J, M, h = tsplit(JMh)

    return CAM_Specification_CAM16(J=J, M=M, h=h)


def CIECAM16_to_JMh_CIECAM16(specification) -> NDArrayFloat:
    """
    Convert from *CIECAM16* specification to *CIECAM16* :math:`JMh` correlates.

    Parameters
    ----------
    specification
        *CIECAM16* colour appearance model specification.

    Returns
    -------
    :class:`numpy.ndarray`
        *CIECAM16* :math:`JMh` correlates.

    Examples
    --------
    >>> specification = CAM_Specification_CIECAM16(
    ...     J=41.731207905126638, M=0.107436772335905, h=217.067959767393010
    ... )
    >>> CIECAM16_to_JMh_CIECAM16(specification)  # doctest: +ELLIPSIS
    array([  4.1731207...e+01,   1.0743677...e-01,   2.1706796...e+02])
    """

    return tstack([specification.J, specification.M, specification.h])


def JMh_CIECAM16_to_CIECAM16(JMh: ArrayLike) -> CAM_Specification_CIECAM16:
    """
    Convert from *CAM6* :math:`JMh` correlates to *CAM6* specification.

    Parameters
    ----------
    JMh
         *CAM6* :math:`JMh` correlates.

    Returns
    -------
    :class:`colour.CAM6_Specification`
        *CAM6* colour appearance model specification.

    Examples
    --------
    >>> import numpy as np
    >>> JMh = np.array([4.17312079e01, 1.07436772e-01, 2.17067960e02])
    >>> JMh_CIECAM16_to_CIECAM16(JMh)  # doctest: +ELLIPSIS
    CAM_Specification_CIECAM16(J=41.7312079..., C=None, h=217.06796..., \
s=None, Q=None, M=0.1074367..., H=None, HC=None)
    """

    J, M, h = tsplit(JMh)

    return CAM_Specification_CIECAM16(J=J, M=M, h=h)


def Hellwig2022_to_JMh_Hellwig2022(specification) -> NDArrayFloat:
    """
    Convert from *Hellwig and Fairchild (2022)* specification to
    *Hellwig and Fairchild (2022)* :math:`JMh` correlates.

    Parameters
    ----------
    specification
        *Hellwig and Fairchild (2022)* colour appearance model specification.

    Returns
    -------
    :class:`numpy.ndarray`
        *Hellwig and Fairchild (2022)* :math:`JMh` correlates.

    Examples
    --------
    >>> specification = CAM_Specification_Hellwig2022(
    ...     J=41.731207905126638, M=0.029382869535427687, h=217.06795976739301
    ... )
    >>> Hellwig2022_to_JMh_Hellwig2022(specification)  # doctest: +ELLIPSIS
    array([  4.1731207...e+01,   2.9382869...e-02,   2.1706796...e+02])
    """

    return tstack([specification.J, specification.M, specification.h])


def JMh_Hellwig2022_to_Hellwig2022(
    JMh: ArrayLike,
) -> CAM_Specification_Hellwig2022:
    """
    Convert from *Hellwig and Fairchild (2022)* :math:`JMh` correlates to
    *Hellwig and Fairchild (2022)* specification.

    Parameters
    ----------
    JMh
         *Hellwig and Fairchild (2022)* :math:`JMh` correlates.

    Returns
    -------
    :class:`colour.CAM6_Specification`
        *Hellwig and Fairchild (2022)* colour appearance model specification.

    Examples
    --------
    >>> import numpy as np
    >>> JMh = np.array([4.17312079e01, 2.93828695e-02, 2.17067960e02])
    >>> JMh_Hellwig2022_to_Hellwig2022(JMh)  # doctest: +ELLIPSIS
    CAM_Specification_Hellwig2022(J=41.7312079..., C=None, h=217.06796, \
s=None, Q=None, M=0.0293828..., H=None, HC=None, J_HK=None, Q_HK=None)
    """

    J, M, h = tsplit(JMh)

    return CAM_Specification_Hellwig2022(J=J, M=M, h=h)


def XYZ_to_luminance(XYZ: ArrayLike) -> NDArrayFloat:
    """
    Convert from *CIE XYZ* tristimulus values to *luminance* :math:`Y`.

    Parameters
    ----------
    XYZ
        *CIE XYZ* tristimulus values.

    Returns
    -------
    :class:`numpy.ndarray`
        *Luminance* :math:`Y`.

    Examples
    --------
    >>> import numpy as np
    >>> XYZ = np.array([0.20654008, 0.12197225, 0.05136952])
    >>> XYZ_to_luminance(XYZ)  # doctest: +ELLIPSIS
    0.1219722...
    """

    _X, Y, _Z = tsplit(XYZ)

    return Y


def RGB_luminance_to_RGB(Y: ArrayLike) -> NDArrayFloat:
    """
    Convert from *luminance* :math:`Y` to *RGB*.

    Parameters
    ----------
    Y
        *Luminance* :math:`Y`.

    Returns
    -------
    :class:`numpy.ndarray`
        *RGB*.

    Examples
    --------
    >>> RGB_luminance_to_RGB(0.123014562384318)  # doctest: +ELLIPSIS
    array([ 0.1230145...,  0.1230145...,  0.1230145...])
    """

    Y = as_float_array(Y)

    return tstack([Y, Y, Y])


def CCT_D_uv_to_mired(CCT_D_uv: ArrayLike) -> NDArrayFloat:
    """
    Convert given correlated colour temperature :math:`T_{cp}` and
    :math:`\\Delta_{uv}` to micro reciprocal degree (mired).

    Parameters
    ----------
    CCT_D_uv
        Correlated colour temperature :math:`T_{cp}`, :math:`\\Delta_{uv}`.

    Returns
    -------
    :class:`numpy.ndarray`
        Micro reciprocal degree (mired).

    Examples
    --------
    >>> CCT_D_uv = np.array([6500.0081378199056, 0.008333331244225])
    >>> CCT_D_uv_to_mired(CCT_D_uv)  # doctest: +ELLIPSIS
    153.8459612...
    """

    CCT, _D_uv = tsplit(CCT_D_uv)

    return CCT_to_mired(CCT)


def mired_to_CCT_D_uv(mired: ArrayLike) -> NDArrayFloat:
    """
    Convert given micro reciprocal degree (mired) to correlated colour
    temperature :math:`T_{cp}` and :math:`\\Delta_{uv}`.

    Parameters
    ----------
    Micro reciprocal degree (mired).


    Returns
    -------
    :class:`numpy.ndarray`
        Correlated colour temperature :math:`T_{cp}`, :math:`\\Delta_{uv}`.

    Examples
    --------
    >>> mired = 153.84596123527297
    >>> mired_to_CCT_D_uv(mired)  # doctest: +ELLIPSIS
    array([ 6500.0081378...,     0.        ])
    """

    mired = as_float_array(mired)

    return tstack([mired_to_CCT(mired), zeros(mired.shape)])


_ILLUMINANT_DEFAULT: str = "D65"
"""Default automatic colour conversion graph illuminant name."""

_CCS_ILLUMINANT_DEFAULT: NDArrayFloat = CCS_ILLUMINANTS[
    "CIE 1931 2 Degree Standard Observer"
][_ILLUMINANT_DEFAULT]
"""
Default automatic colour conversion graph illuminant *CIE xy* chromaticity
coordinates.
"""

_TVS_ILLUMINANT_DEFAULT: NDArrayFloat = xy_to_XYZ(_CCS_ILLUMINANT_DEFAULT)
"""
Default automatic colour conversion graph illuminant *CIE XYZ* tristimulus
values.
"""

_RGB_COLOURSPACE_DEFAULT: RGB_Colourspace = RGB_COLOURSPACE_sRGB
"""Default automatic colour conversion graph *RGB* colourspace."""

_CAM_KWARGS_CIECAM02_sRGB: dict = CAM_KWARGS_CIECAM02_sRGB.copy()
"""
Default parameter values for the *CIECAM02* colour appearance model usage in
the context of *sRGB*.

Warnings
--------
The *CIE XYZ* tristimulus values of reference white :math:`XYZ_w` is adjusted
for the domain-range scale **'1'**.
"""

_CAM_KWARGS_CIECAM02_sRGB["XYZ_w"] = _CAM_KWARGS_CIECAM02_sRGB["XYZ_w"] / 100

CONVERSION_SPECIFICATIONS_DATA: List[tuple] = [
    # Colorimetry
    ("Spectral Distribution", "CIE XYZ", sd_to_XYZ),
    ("CIE XYZ", "Spectral Distribution", XYZ_to_sd),
    ("Spectral Distribution", "Luminous Flux", luminous_flux),
    ("Spectral Distribution", "Luminous Efficiency", luminous_efficiency),
    ("Spectral Distribution", "Luminous Efficacy", luminous_efficacy),
    ("CIE XYZ", "Luminance", XYZ_to_luminance),
    ("Luminance", "Lightness", lightness),
    ("Lightness", "Luminance", luminance),
    (
        "CIE XYZ",
        "Whiteness",
        partial(whiteness, XYZ_0=_TVS_ILLUMINANT_DEFAULT),
    ),
    ("CIE XYZ", "Yellowness", yellowness),
    (
        "CIE xy",
        "Colorimetric Purity",
        partial(colorimetric_purity, xy_n=_CCS_ILLUMINANT_DEFAULT),
    ),
    (
        "CIE xy",
        "Complementary Wavelength",
        partial(complementary_wavelength, xy_n=_CCS_ILLUMINANT_DEFAULT),
    ),
    (
        "CIE xy",
        "Dominant Wavelength",
        partial(dominant_wavelength, xy_n=_CCS_ILLUMINANT_DEFAULT),
    ),
    (
        "CIE xy",
        "Excitation Purity",
        partial(excitation_purity, xy_n=_CCS_ILLUMINANT_DEFAULT),
    ),
    ("Wavelength", "CIE XYZ", wavelength_to_XYZ),
    # Colour Models
    ("CIE XYZ", "CIE xyY", XYZ_to_xyY),
    ("CIE xyY", "CIE XYZ", xyY_to_XYZ),
    ("CIE xyY", "CIE xy", xyY_to_xy),
    ("CIE xy", "CIE xyY", xy_to_xyY),
    ("CIE XYZ", "CIE xy", XYZ_to_xy),
    ("CIE xy", "CIE XYZ", xy_to_XYZ),
    ("CIE XYZ", "CIE Lab", XYZ_to_Lab),
    ("CIE Lab", "CIE XYZ", Lab_to_XYZ),
    ("CIE XYZ", "CIE Luv", XYZ_to_Luv),
    ("CIE Luv", "CIE XYZ", Luv_to_XYZ),
    ("CIE Luv", "CIE Luv uv", Luv_to_uv),
    ("CIE Luv uv", "CIE Luv", uv_to_Luv),
    ("CIE Luv uv", "CIE xy", Luv_uv_to_xy),
    ("CIE xy", "CIE Luv uv", xy_to_Luv_uv),
    ("CIE XYZ", "CIE UCS", XYZ_to_UCS),
    ("CIE UCS", "CIE XYZ", UCS_to_XYZ),
    ("CIE UCS", "CIE UCS uv", UCS_to_uv),
    ("CIE UCS uv", "CIE UCS", uv_to_UCS),
    ("CIE UCS uv", "CIE xy", UCS_uv_to_xy),
    ("CIE xy", "CIE UCS uv", xy_to_UCS_uv),
    ("CIE XYZ", "CIE UVW", XYZ_to_UVW),
    ("CIE UVW", "CIE XYZ", UVW_to_XYZ),
    ("CIE XYZ", "DIN99", XYZ_to_DIN99),
    ("DIN99", "CIE XYZ", DIN99_to_XYZ),
    ("CIE XYZ", "hdr-CIELAB", XYZ_to_hdr_CIELab),
    ("hdr-CIELAB", "CIE XYZ", hdr_CIELab_to_XYZ),
    (
        "CIE XYZ",
        "Hunter Lab",
        partial(
            XYZ_to_Hunter_Lab,
            XYZ_n=TVS_ILLUMINANTS_HUNTERLAB["CIE 1931 2 Degree Standard Observer"][
                "D65"
            ].XYZ_n
            / 100,
        ),
    ),
    (
        "Hunter Lab",
        "CIE XYZ",
        partial(
            Hunter_Lab_to_XYZ,
            XYZ_n=TVS_ILLUMINANTS_HUNTERLAB["CIE 1931 2 Degree Standard Observer"][
                "D65"
            ].XYZ_n
            / 100,
        ),
    ),
    (
        "CIE XYZ",
        "Hunter Rdab",
        partial(
            XYZ_to_Hunter_Rdab,
            XYZ_n=TVS_ILLUMINANTS_HUNTERLAB["CIE 1931 2 Degree Standard Observer"][
                "D65"
            ].XYZ_n
            / 100,
        ),
    ),
    (
        "Hunter Rdab",
        "CIE XYZ",
        partial(
            Hunter_Rdab_to_XYZ,
            XYZ_n=TVS_ILLUMINANTS_HUNTERLAB["CIE 1931 2 Degree Standard Observer"][
                "D65"
            ].XYZ_n
            / 100,
        ),
    ),
    ("CIE XYZ", "ICaCb", XYZ_to_ICaCb),
    ("ICaCb", "CIE XYZ", ICaCb_to_XYZ),
    ("CIE XYZ", "ICtCp", XYZ_to_ICtCp),
    ("ICtCp", "CIE XYZ", ICtCp_to_XYZ),
    ("CIE XYZ", "IgPgTg", XYZ_to_IgPgTg),
    ("IgPgTg", "CIE XYZ", IgPgTg_to_XYZ),
    ("CIE XYZ", "IPT", XYZ_to_IPT),
    ("IPT", "CIE XYZ", IPT_to_XYZ),
    ("CIE XYZ", "IPT Ragoo 2021", XYZ_to_IPT_Ragoo2021),
    ("IPT Ragoo 2021", "CIE XYZ", IPT_Ragoo2021_to_XYZ),
    ("CIE XYZ", "Jzazbz", XYZ_to_Jzazbz),
    ("Jzazbz", "CIE XYZ", Jzazbz_to_XYZ),
    ("CIE XYZ", "hdr-IPT", XYZ_to_hdr_IPT),
    ("hdr-IPT", "CIE XYZ", hdr_IPT_to_XYZ),
    ("CIE XYZ", "OSA UCS", XYZ_to_OSA_UCS),
    ("OSA UCS", "CIE XYZ", OSA_UCS_to_XYZ),
    ("CIE XYZ", "Oklab", XYZ_to_Oklab),
    ("Oklab", "CIE XYZ", Oklab_to_XYZ),
    ("CIE XYZ", "ProLab", XYZ_to_ProLab),
    ("ProLab", "CIE XYZ", ProLab_to_XYZ),
    ("CIE XYZ", "Yrg", XYZ_to_Yrg),
    ("Yrg", "CIE XYZ", Yrg_to_XYZ),
    ("CIE 1931", "CIE XYZ", xyY_to_XYZ),
    ("CIE XYZ", "CIE 1931", XYZ_to_xyY),
    ("CIE 1960 UCS", "CIE XYZ", CIE1960UCS_to_XYZ),
    ("CIE XYZ", "CIE 1960 UCS", XYZ_to_CIE1960UCS),
    ("CIE 1976 UCS", "CIE XYZ", CIE1976UCS_to_XYZ),
    ("CIE XYZ", "CIE 1976 UCS", XYZ_to_CIE1976UCS),
    # RGB Colour Models
    (
        "CIE XYZ",
        "RGB",
        partial(XYZ_to_RGB, colourspace=_RGB_COLOURSPACE_DEFAULT),
    ),
    (
        "RGB",
        "CIE XYZ",
        partial(RGB_to_XYZ, colourspace=_RGB_COLOURSPACE_DEFAULT),
    ),
    (
        "RGB",
        "Scene-Referred RGB",
        partial(
            RGB_to_RGB,
            input_colourspace=_RGB_COLOURSPACE_DEFAULT,
            output_colourspace=_RGB_COLOURSPACE_DEFAULT,
        ),
    ),
    (
        "Scene-Referred RGB",
        "RGB",
        partial(
            RGB_to_RGB,
            input_colourspace=_RGB_COLOURSPACE_DEFAULT,
            output_colourspace=_RGB_COLOURSPACE_DEFAULT,
        ),
    ),
    ("RGB", "HSV", RGB_to_HSV),
    ("HSV", "RGB", HSV_to_RGB),
    ("RGB", "HSL", RGB_to_HSL),
    ("HSL", "RGB", HSL_to_RGB),
    ("RGB", "HCL", RGB_to_HCL),
    ("HCL", "RGB", HCL_to_RGB),
    ("RGB", "IHLS", RGB_to_IHLS),
    ("IHLS", "RGB", IHLS_to_RGB),
    ("CMY", "RGB", CMY_to_RGB),
    ("RGB", "CMY", RGB_to_CMY),
    ("CMY", "CMYK", CMY_to_CMYK),
    ("CMYK", "CMY", CMYK_to_CMY),
    (
        "RGB",
        "RGB Luminance",
        partial(
            RGB_luminance,
            primaries=_RGB_COLOURSPACE_DEFAULT.primaries,
            whitepoint=_RGB_COLOURSPACE_DEFAULT.whitepoint,
        ),
    ),
    ("RGB Luminance", "RGB", RGB_luminance_to_RGB),
    ("RGB", "Prismatic", RGB_to_Prismatic),
    ("Prismatic", "RGB", Prismatic_to_RGB),
    ("Output-Referred RGB", "YCbCr", RGB_to_YCbCr),
    ("YCbCr", "Output-Referred RGB", YCbCr_to_RGB),
    ("RGB", "YcCbcCrc", RGB_to_YcCbcCrc),
    ("YcCbcCrc", "RGB", YcCbcCrc_to_RGB),
    ("Output-Referred RGB", "YCoCg", RGB_to_YCoCg),
    ("YCoCg", "Output-Referred RGB", YCoCg_to_RGB),
    ("RGB", "Output-Referred RGB", cctf_encoding),
    ("Output-Referred RGB", "RGB", cctf_decoding),
    ("Scene-Referred RGB", "Output-Referred RGB", cctf_encoding),
    ("Output-Referred RGB", "Scene-Referred RGB", cctf_decoding),
    ("CIE XYZ", "sRGB", XYZ_to_sRGB),
    ("sRGB", "CIE XYZ", sRGB_to_XYZ),
    # Colour Notation Systems
    ("Output-Referred RGB", "Hexadecimal", RGB_to_HEX),
    ("Hexadecimal", "Output-Referred RGB", HEX_to_RGB),
    ("CSS Color 3", "Output-Referred RGB", keyword_to_RGB_CSSColor3),
    ("CIE xyY", "Munsell Colour", xyY_to_munsell_colour),
    ("Munsell Colour", "CIE xyY", munsell_colour_to_xyY),
    ("Luminance", "Munsell Value", munsell_value),
    ("Munsell Value", "Luminance", partial(luminance, method="ASTM D1535")),
    # Colour Quality
    ("Spectral Distribution", "CRI", colour_rendering_index),
    ("Spectral Distribution", "CQS", colour_quality_scale),
    # Colour Temperature
    ("CCT", "CIE UCS uv", CCT_to_uv),
    ("CIE UCS uv", "CCT", uv_to_CCT),
    ("CCT", "Mired", CCT_D_uv_to_mired),
    ("Mired", "CCT", mired_to_CCT_D_uv),
    # Advanced Colorimetry
    (
        "CIE XYZ",
        "ATD95",
        partial(
            XYZ_to_ATD95,
            XYZ_0=_TVS_ILLUMINANT_DEFAULT,
            Y_0=80 * 0.2,
            k_1=0,
            k_2=(15 + 50) / 2,
        ),
    ),
    (
        "CIE XYZ",
        "CIECAM02",
        partial(XYZ_to_CIECAM02, **_CAM_KWARGS_CIECAM02_sRGB),
    ),
    (
        "CIECAM02",
        "CIE XYZ",
        partial(CIECAM02_to_XYZ, **_CAM_KWARGS_CIECAM02_sRGB),
    ),
    ("CIECAM02", "CIECAM02 JMh", CIECAM02_to_JMh_CIECAM02),
    ("CIECAM02 JMh", "CIECAM02", JMh_CIECAM02_to_CIECAM02),
    ("CIE XYZ", "CAM16", partial(XYZ_to_CAM16, **_CAM_KWARGS_CIECAM02_sRGB)),
    ("CAM16", "CIE XYZ", partial(CAM16_to_XYZ, **_CAM_KWARGS_CIECAM02_sRGB)),
    ("CAM16", "CAM16 JMh", CAM16_to_JMh_CAM16),
    ("CAM16 JMh", "CAM16", JMh_CAM16_to_CAM16),
    (
        "CIE XYZ",
        "CIECAM16",
        partial(XYZ_to_CIECAM16, **_CAM_KWARGS_CIECAM02_sRGB),
    ),
    (
        "CIECAM16",
        "CIE XYZ",
        partial(CIECAM16_to_XYZ, **_CAM_KWARGS_CIECAM02_sRGB),
    ),
    ("CIECAM16", "CIECAM16 JMh", CIECAM16_to_JMh_CIECAM16),
    ("CIECAM16 JMh", "CIECAM16", JMh_CIECAM16_to_CIECAM16),
    (
        "CIE XYZ",
        "Hellwig 2022",
        partial(XYZ_to_Hellwig2022, **_CAM_KWARGS_CIECAM02_sRGB),
    ),
    (
        "Hellwig 2022",
        "CIE XYZ",
        partial(Hellwig2022_to_XYZ, **_CAM_KWARGS_CIECAM02_sRGB),
    ),
    ("Hellwig 2022", "Hellwig 2022 JMh", Hellwig2022_to_JMh_Hellwig2022),
    ("Hellwig 2022 JMh", "Hellwig 2022", JMh_Hellwig2022_to_Hellwig2022),
    (
        "CIE XYZ",
        "Kim 2009",
        partial(XYZ_to_Kim2009, XYZ_w=_TVS_ILLUMINANT_DEFAULT, L_A=80 * 0.2),
    ),
    (
        "Kim 2009",
        "CIE XYZ",
        partial(Kim2009_to_XYZ, XYZ_w=_TVS_ILLUMINANT_DEFAULT, L_A=80 * 0.2),
    ),
    (
        "CIE XYZ",
        "Hunt",
        partial(
            XYZ_to_Hunt,
            XYZ_w=_TVS_ILLUMINANT_DEFAULT,
            XYZ_b=_TVS_ILLUMINANT_DEFAULT,
            L_A=80 * 0.2,
            CCT_w=6504,
        ),
    ),
    (
        "CIE XYZ",
        "LLAB",
        partial(XYZ_to_LLAB, XYZ_0=_TVS_ILLUMINANT_DEFAULT, Y_b=80 * 0.2, L=80),
    ),
    (
        "CIE XYZ",
        "Nayatani95",
        partial(
            XYZ_to_Nayatani95,
            XYZ_n=_TVS_ILLUMINANT_DEFAULT,
            Y_o=0.2,
            E_o=1000,
            E_or=1000,
        ),
    ),
    (
        "CIE XYZ",
        "RLAB",
        partial(XYZ_to_RLAB, XYZ_n=_TVS_ILLUMINANT_DEFAULT, Y_n=20),
    ),
    (
        "CIE XYZ",
        "ZCAM",
        partial(
            XYZ_to_ZCAM,
            XYZ_w=_TVS_ILLUMINANT_DEFAULT,
            L_A=64 / np.pi * 0.2,
            Y_b=20,
        ),
    ),
    (
        "ZCAM",
        "CIE XYZ",
        partial(
            ZCAM_to_XYZ,
            XYZ_w=_TVS_ILLUMINANT_DEFAULT,
            L_A=64 / np.pi * 0.2,
            Y_b=20,
        ),
    ),
    ("CIECAM02 JMh", "CAM02LCD", JMh_CIECAM02_to_CAM02LCD),
    ("CAM02LCD", "CIECAM02 JMh", CAM02LCD_to_JMh_CIECAM02),
    ("CIECAM02 JMh", "CAM02SCD", JMh_CIECAM02_to_CAM02SCD),
    ("CAM02SCD", "CIECAM02 JMh", CAM02SCD_to_JMh_CIECAM02),
    ("CIECAM02 JMh", "CAM02UCS", JMh_CIECAM02_to_CAM02UCS),
    ("CAM02UCS", "CIECAM02 JMh", CAM02UCS_to_JMh_CIECAM02),
    ("CAM16 JMh", "CAM16LCD", JMh_CAM16_to_CAM16LCD),
    ("CAM16LCD", "CAM16 JMh", CAM16LCD_to_JMh_CAM16),
    ("CAM16 JMh", "CAM16SCD", JMh_CAM16_to_CAM16SCD),
    ("CAM16SCD", "CAM16 JMh", CAM16SCD_to_JMh_CAM16),
    ("CAM16 JMh", "CAM16UCS", JMh_CAM16_to_CAM16UCS),
    ("CAM16UCS", "CAM16 JMh", CAM16UCS_to_JMh_CAM16),
]
"""
Automatic colour conversion graph specifications data describing two nodes and
the edge in the graph.
"""


# Programmatically defining the colourspace models polar conversions.


def _format_node_name(name):
    """Format given name by applying a series of substitutions."""

    for pattern, substitution in [
        ("hdr_", "hdr-"),
        ("-CIELab", "-CIELAB"),
        ("_", " "),
        ("^Lab", "CIE Lab"),
        ("^LCHab", "CIE LCHab"),
        ("^Luv", "CIE Luv"),
        ("^LCHuv", "CIE LCHuv"),
        ("Ragoo2021", "Ragoo 2021"),
    ]:
        name = re.sub(pattern, substitution, name)

    return name


for _Jab, _JCh in COLOURSPACE_MODELS_POLAR_CONVERSIONS:
    _module = sys.modules["colour.models"]
    _Jab_name = _format_node_name(_Jab)
    _JCh_name = _format_node_name(_JCh)
    CONVERSION_SPECIFICATIONS_DATA.append(
        (_Jab_name, _JCh_name, getattr(_module, f"{_Jab}_to_{_JCh}"))
    )
    CONVERSION_SPECIFICATIONS_DATA.append(
        (_JCh_name, _Jab_name, getattr(_module, f"{_JCh}_to_{_Jab}"))
    )

del _format_node_name, _JCh, _Jab, _module, _Jab_name, _JCh_name

CONVERSION_SPECIFICATIONS: list = [
    Conversion_Specification(*specification)
    for specification in CONVERSION_SPECIFICATIONS_DATA
]
"""
Automatic colour conversion graph specifications describing two nodes and
the edge in the graph.
"""

CONVERSION_GRAPH_NODE_LABELS: dict = {
    specification[0].lower(): specification[0]
    for specification in CONVERSION_SPECIFICATIONS_DATA
}
"""Automatic colour conversion graph node labels."""

CONVERSION_GRAPH_NODE_LABELS.update(
    {
        specification[1].lower(): specification[1]
        for specification in CONVERSION_SPECIFICATIONS_DATA
    }
)


@required("NetworkX")
def _build_graph() -> networkx.DiGraph:  # pyright: ignore  # noqa: F821
    """
    Build the automatic colour conversion graph.

    Returns
    -------
    :class:`networkx.DiGraph`
         Automatic colour conversion graph.
    """

    import networkx as nx

    graph = nx.DiGraph()

    for specification in CONVERSION_SPECIFICATIONS:
        graph.add_edge(
            specification.source,
            specification.target,
            conversion_function=specification.conversion_function,
        )

    return graph


CONVERSION_GRAPH: nx.DiGraph | None = None  # pyright: ignore # noqa: F821
"""Automatic colour conversion graph."""


@required("NetworkX")
def _conversion_path(source: str, target: str) -> List[Callable]:
    """
    Return the conversion path from the source node to the target node in the
    automatic colour conversion graph.

    Parameters
    ----------
    source
        Source node.
    target
        Target node.

    Returns
    -------
    :class:`list`
        Conversion path from the source node to the target node, i.e., a list of
        conversion function callables.

    Examples
    --------
    >>> _conversion_path("cie lab", "cct")
    ... # doctest: +ELLIPSIS
    [<function Lab_to_XYZ at 0x...>, <function XYZ_to_UCS at 0x...>, \
<function UCS_to_uv at 0x...>, <function uv_to_CCT at 0x...>]
    """

    import networkx as nx

    global CONVERSION_GRAPH  # noqa: PLW0603

    if CONVERSION_GRAPH is None:
        # Updating the :attr:`CONVERSION_GRAPH` attributes.
        colour.graph.CONVERSION_GRAPH = CONVERSION_GRAPH = _build_graph()

    path = nx.shortest_path(cast(nx.DiGraph, CONVERSION_GRAPH), source, target)

    return [
        CONVERSION_GRAPH.get_edge_data(a, b)["conversion_function"]  # pyright: ignore
        for a, b in zip(path[:-1], path[1:])
    ]


def _lower_order_function(callable_: Callable) -> Callable:
    """
    Return the lower order function associated with given callable, i.e.,
    the function wrapped by a partial object.

    Parameters
    ----------
    callable_
        Callable to return the lower order function.

    Returns
    -------
    Callable
        Lower order function or given callable if no lower order function
        exists.
    """

    return callable_.func if isinstance(callable_, partial) else callable_


def describe_conversion_path(
    source: str,
    target: str,
    mode: Literal["Short", "Long", "Extended"] | str = "Short",
    width: int = 79,
    padding: int = 3,
    print_callable: Callable = print,
    **kwargs: Any,
):
    """
    Describe the conversion path from source colour representation to target
    colour representation using the automatic colour conversion graph.

    Parameters
    ----------
    source
        Source colour representation, i.e., the source node in the automatic
        colour conversion graph.
    target
        Target colour representation, i.e., the target node in the automatic
        colour conversion graph.
    mode
        Verbose mode: *Short* describes the conversion path, *Long* provides
        details about the arguments, definitions signatures and output values,
        *Extended* appends the definitions' documentation.
    width
        Message box width.
    padding
        Padding on each side of the message.
    print_callable
        Callable used to print the message box.

    Other Parameters
    ----------------
    kwargs
        {:func:`colour.convert`},
        See the documentation of the previously listed definition.

    Examples
    --------
    >>> describe_conversion_path("Spectral Distribution", "sRGB", width=75)
    ===========================================================================
    *                                                                         *
    *   [ Conversion Path ]                                                   *
    *                                                                         *
    *   "sd_to_XYZ" --> "XYZ_to_sRGB"                                         *
    *                                                                         *
    ===========================================================================
    """

    try:  # pragma: no cover
        signature_inspection = inspect.signature
    except AttributeError:  # pragma: no cover
        signature_inspection = inspect.getfullargspec

    source, target = source.lower(), target.lower()
    mode = validate_method(
        mode,
        ("Short", "Long", "Extended"),
        '"{0}" mode is invalid, it must be one of {1}!',
    )

    width = (79 + 2 + 2 * 3 - 4) if mode == "extended" else width

    conversion_path = _conversion_path(source, target)

    joined_conversion_path = " --> ".join(
        [
            f'"{_lower_order_function(conversion_function).__name__}"'
            for conversion_function in conversion_path
        ]
    )

    message_box(
        f"[ Conversion Path ]\n\n{joined_conversion_path}",
        width,
        padding,
        print_callable,
    )

    for conversion_function in conversion_path:
        conversion_function_name = _lower_order_function(conversion_function).__name__

        # Filtering compatible keyword arguments passed directly and
        # irrespective of any conversion function name.
        filtered_kwargs = filter_kwargs(conversion_function, **kwargs)

        # Filtering keyword arguments passed as dictionary with the
        # conversion function name.
        filtered_kwargs.update(kwargs.get(conversion_function_name, {}))

        return_value = filtered_kwargs.pop("return", None)

        if mode in ("long", "extended"):
            signature = pformat(
                signature_inspection(_lower_order_function(conversion_function))
            )
            message = (
                f'[ "{_lower_order_function(conversion_function).__name__}" ]\n\n'
                f"[ Signature ]\n\n"
                f"{signature}"
            )

            if filtered_kwargs:
                message += f"\n\n[ Filtered Arguments ]\n\n{pformat(filtered_kwargs)}"

            if mode in ("extended",):
                docstring = textwrap.dedent(
                    str(_lower_order_function(conversion_function).__doc__)
                ).strip()
                message += f"\n\n[ Documentation ]\n\n {docstring}"

            if return_value is not None:
                message += f"\n\n[ Conversion Output ]\n\n{return_value}"

            message_box(message, width, padding, print_callable)


def convert(a: Any, source: str, target: str, **kwargs: Any) -> Any:
    """
    Convert given object :math:`a` from source colour representation to target
    colour representation using the automatic colour conversion graph.

    The conversion is performed by finding the shortest path in a
    `NetworkX <https://networkx.github.io>`__ :class:`DiGraph` class instance.

    The conversion path adopts the **'1'** domain-range scale and the object
    :math:`a` is expected to be *soft* normalised accordingly. For example,
    *CIE XYZ* tristimulus values arguments for use with the *CAM16* colour
    appearance model should be in domain `[0, 1]` instead of the domain
    `[0, 100]` used with the **'Reference'** domain-range scale. The arguments
    are typically converted as follows:

    -   *Scalars* in domain-range `[0, 10]`, e.g *Munsell Value* are
        scaled by *10*.
    -   *Percentages* in domain-range `[0, 100]` are scaled by *100*.
    -   *Degrees* in domain-range `[0, 360]` are scaled by *360*.
    -   *Integers* in domain-range `[0, 2**n -1]` where `n` is the bit
        depth are scaled by *2**n -1*.

    See the `Domain-Range Scales <../basics.html#domain-range-scales>`__ page
    for more information.

    Parameters
    ----------
    a
        Object :math:`a` to convert. If :math:`a` represents a reflectance,
        transmittance or absorptance value, the expectation is that it is
        viewed under *CIE Standard Illuminant D Series* *D65*. The illuminant
        can be changed on a per-definition basis along the conversion path.
    source
        Source colour representation, i.e., the source node in the automatic
        colour conversion graph.
    target
        Target colour representation, i.e., the target node in the automatic
        colour conversion graph.

    Other Parameters
    ----------------
    kwargs
        See the documentation of the supported conversion
        definitions.

        Arguments for the conversion definitions are passed as keyword
        arguments whose names is those of the conversion definitions and values
        set as dictionaries. For example, in the conversion from spectral
        distribution to *sRGB* colourspace, passing arguments to the
        :func:`colour.sd_to_XYZ` definition is done as follows::

            convert(sd, "Spectral Distribution", "sRGB", sd_to_XYZ={\
"illuminant": SDS_ILLUMINANTS["FL2"]})

        It is also possible to pass keyword arguments directly to the various
        conversion definitions irrespective of their name. This is
        ``dangerous`` and could cause unexpected behaviour, consider the
        following conversion::

             convert(sd, "Spectral Distribution", "sRGB", "illuminant": \
SDS_ILLUMINANTS["FL2"])

        Because both the :func:`colour.sd_to_XYZ` and
        :func:`colour.XYZ_to_sRGB` definitions have an *illuminant* argument,
        `SDS_ILLUMINANTS["FL2"]` will be passed to both of them and will raise
        an exception in the :func:`colour.XYZ_to_sRGB` definition. This will
        be addressed in the future by either catching the exception and trying
        a new time without the keyword argument or more elegantly via type
        checking.

        With that in mind, this mechanism offers some good benefits: For
        example, it allows defining a conversion from *CIE XYZ* colourspace to
        *n* different colour models while passing an illuminant argument but
        without having to explicitly define all the explicit conversion
        definition arguments::

            a = np.array([0.20654008, 0.12197225, 0.05136952])
            illuminant = CCS_ILLUMINANTS[\
"CIE 1931 2 Degree Standard Observer"]["D65"]
            for model in ("CIE xyY", "CIE Lab"):
                convert(a, "CIE XYZ", model, illuminant=illuminant)

        Instead of::

            for model in ("CIE xyY", "CIE Lab"):
                convert(a, "CIE XYZ", model, XYZ_to_xyY={"illuminant": \
illuminant}, XYZ_to_Lab={"illuminant": illuminant})

        Mixing both approaches is possible for the brevity benefits. It is made
        possible because the keyword arguments directly passed are filtered
        first and then the resulting dict is updated with the explicit
        conversion definition arguments::

            illuminant = CCS_ILLUMINANTS[\
"CIE 1931 2 Degree Standard Observer"]["D65"]
             convert(sd, "Spectral Distribution", "sRGB", "illuminant": \
SDS_ILLUMINANTS["FL2"], XYZ_to_sRGB={"illuminant": illuminant})

        For inspection purposes, verbose is enabled by passing arguments to the
        :func:`colour.describe_conversion_path` definition via the ``verbose``
        keyword argument as follows::

            convert(sd, "Spectral Distribution", "sRGB", \
verbose={"mode": "Long"})

    Returns
    -------
    Any
        Converted object :math:`a`.

    Warnings
    --------
    The domain-range scale is **'1'** and cannot be changed.

    Notes
    -----
    -   The **RGB** colour representation is assumed to be linear and
        representing *scene-referred* imagery, i.e., **Scene-Referred RGB**
        representation. To encode such *RGB* values as *output-referred*
        (*display-referred*) imagery, i.e., encode the *RGB* values using an
        encoding colour component transfer function (Encoding CCTF) /
        opto-electronic transfer function (OETF), the
        **Output-Referred RGB** representation must be used::

             convert(RGB, "Scene-Referred RGB", "Output-Referred RGB")

        Likewise, encoded *output-referred* *RGB* values can be decoded with
        the **Scene-Referred RGB** representation::

            convert(RGB, "Output-Referred RGB", "Scene-Referred RGB")

    -   The following defaults have been adopted:

        -   The default illuminant for the computation is
            *CIE Standard Illuminant D Series* *D65*. It can be changed on a
            per-definition basis along the conversion path. Note that the
            conversion from spectral to *CIE XYZ* tristimulus values remains
            unchanged.
        -   The default *RGB* colourspace primaries and whitepoint are that of
            the *BT.709*/*sRGB* colourspace. They can be changed on a
            per-definition basis along the conversion path.
        -   When using **sRGB** as a source or target colour representation,
            the convenient :func:`colour.sRGB_to_XYZ` and
            :func:`colour.XYZ_to_sRGB` definitions are used, respectively.
            Thus, decoding and encoding using the sRGB electro-optical transfer
            function (EOTF) and its inverse will be applied by default.
        -   Most of the colour appearance models have defaults set according to
            *IEC 61966-2-1:1999* viewing conditions, i.e., *sRGB* 64 Lux ambient
            illumination, 80 :math:`cd/m^2`, adapting field luminance about
            20% of a white object in the scene.

    Examples
    --------
    >>> import numpy as np
    >>> from colour import SDS_COLOURCHECKERS, SDS_ILLUMINANTS
    >>> sd = SDS_COLOURCHECKERS["ColorChecker N Ohta"]["dark skin"]
    >>> convert(
    ...     sd,
    ...     "Spectral Distribution",
    ...     "sRGB",
    ...     verbose={"mode": "Short", "width": 75},
    ... )
    ... # doctest: +ELLIPSIS
    ===========================================================================
    *                                                                         *
    *   [ Conversion Path ]                                                   *
    *                                                                         *
    *   "sd_to_XYZ" --> "XYZ_to_sRGB"                                         *
    *                                                                         *
    ===========================================================================
    array([ 0.4903477...,  0.3018587...,  0.2358768...])
    >>> illuminant = SDS_ILLUMINANTS["FL2"]
    >>> convert(
    ...     sd,
    ...     "Spectral Distribution",
    ...     "sRGB",
    ...     sd_to_XYZ={"illuminant": illuminant},
    ... )
    ... # doctest: +ELLIPSIS
    array([ 0.4792457...,  0.3167696...,  0.1736272...])
    >>> a = np.array([0.45675795, 0.30986982, 0.24861924])
    >>> convert(a, "Output-Referred RGB", "CAM16UCS")
    ... # doctest: +ELLIPSIS
    array([ 0.3999481...,  0.0920655...,  0.0812752...])
    >>> a = np.array([0.39994811, 0.09206558, 0.08127526])
    >>> convert(a, "CAM16UCS", "sRGB", verbose={"mode": "Short", "width": 75})
    ... # doctest: +ELLIPSIS
    ===========================================================================
    *                                                                         *
    *   [ Conversion Path ]                                                   *
    *                                                                         *
    *   "UCS_Li2017_to_JMh_CAM16" --> "JMh_CAM16_to_CAM16" -->                *
    *   "CAM16_to_XYZ" --> "XYZ_to_sRGB"                                      *
    *                                                                         *
    ===========================================================================
    array([ 0.4567576...,  0.3098826...,  0.2486222...])
    """

    source, target = source.lower(), target.lower()

    conversion_path = _conversion_path(source, target)

    verbose_kwargs = copy(kwargs)
    for conversion_function in conversion_path:
        conversion_function_name = _lower_order_function(conversion_function).__name__

        # Filtering compatible keyword arguments passed directly and
        # irrespective of any conversion function name.
        filtered_kwargs = filter_kwargs(conversion_function, **kwargs)

        # Filtering keyword arguments passed as dictionary with the
        # conversion function name.
        filtered_kwargs.update(kwargs.get(conversion_function_name, {}))

        with domain_range_scale("1"):
            a = conversion_function(a, **filtered_kwargs)

        if conversion_function_name in verbose_kwargs:
            verbose_kwargs[conversion_function_name]["return"] = a
        else:
            verbose_kwargs[conversion_function_name] = {"return": a}

    if "verbose" in verbose_kwargs:
        verbose_kwargs.update(verbose_kwargs.pop("verbose"))
        describe_conversion_path(source, target, **verbose_kwargs)

    return a
