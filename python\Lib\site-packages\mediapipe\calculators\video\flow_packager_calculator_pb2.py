# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/video/flow_packager_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.util.tracking import flow_packager_pb2 as mediapipe_dot_util_dot_tracking_dot_flow__packager__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n:mediapipe/calculators/video/flow_packager_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\x1a+mediapipe/util/tracking/flow_packager.proto\"\x85\x02\n\x1d\x46lowPackagerCalculatorOptions\x12=\n\x15\x66low_packager_options\x18\x01 \x01(\x0b\x32\x1e.mediapipe.FlowPackagerOptions\x12%\n\x17\x63\x61\x63hing_chunk_size_msec\x18\x02 \x01(\x05:\x04\x32\x35\x30\x30\x12%\n\x11\x63\x61\x63he_file_format\x18\x03 \x01(\t:\nchunk_%04d2W\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xb3\xf8\xaa\x81\x01 \x01(\x0b\x32(.mediapipe.FlowPackagerCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.video.flow_packager_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_FLOWPACKAGERCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_FLOWPACKAGERCALCULATOROPTIONS']._serialized_start=157
  _globals['_FLOWPACKAGERCALCULATOROPTIONS']._serialized_end=418
# @@protoc_insertion_point(module_scope)
