此节点旨在对一组潜在样本应用噪波遮罩。它通过整合指定的遮罩来修改输入样本，从而改变它们的噪声特性。

## 输入

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `samples` | `LATENT` | 将应用噪波遮罩的潜在样本。此参数对于确定将被修改的基础内容至关重要。 |
| `mask`    | `MASK`   | 要应用于潜在样本的遮罩。它定义了样本内噪声变化的区域和强度。       |

## 输出

|
该节点主要用于对图像编码后的latent图像在遮罩区域添加额外的噪声来进行局部的重绘，根据采样器设置的```denoise降噪```的值的不同，当设置的值越小时，生成的图像会保持和原图越高的相似度。

不同```denoise降噪```值设置下使用该节点输出图像效果如下
**Input-输入**

|参数名称 | 作用|
|-----------------------|---------------------|
|samples | 需要进行重绘的图像的Latent输入|
|mask-遮罩  | 加载图像中需要进行重绘的区域|

**Output-输出**

|参数名称 | 作用|
-----------------------|---------------------|
|LATENT  | 转换后的潜空间图像   |
