CropMask 노드는 주어진 마스크에서 특정 영역을 잘라내기 위해 설계되었습니다. 사용자는 좌표와 크기를 지정하여 관심 영역을 정의할 수 있으며, 이를 통해 마스크의 일부를 효과적으로 추출하여 추가 처리나 분석에 사용할 수 있습니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                   |
| -------- | ----------- | ---------------------------------------------------------------------------------------------------------------------- |
| `mask`   | MASK        | 마스크 입력은 잘라낼 마스크 이미지를 나타냅니다. 지정된 좌표와 크기를 기반으로 추출할 영역을 정의하는 데 필수적입니다. |
| `x`      | INT         | x 좌표는 수평 축에서 잘라내기를 시작할 시작 지점을 지정합니다.                                                         |
| `y`      | INT         | y 좌표는 수직 축에서 잘라내기 작업을 시작할 시작 지점을 결정합니다.                                                    |
| `width`  | INT         | 너비는 시작 지점에서 잘라낼 영역의 수평 범위를 정의합니다.                                                             |
| `height` | INT         | 높이는 시작 지점에서 잘라낼 영역의 수직 범위를 지정합니다.                                                             |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                         |
| -------- | ----------- | ---------------------------------------------------------------------------- |
| `mask`   | MASK        | 출력은 지정된 좌표와 크기로 정의된 원래 마스크의 일부인 잘라낸 마스크입니다. |
