
このノードは、StableZero123モデルで使用するためのデータを処理し、条件付けするように設計されています。これらのモデルに適合し、最適化された特定の形式で入力を準備することに重点を置いています。

## 入力

| パラメータ             | Comfy dtype        | 説明 |
|-----------------------|--------------------|-------------|
| `clip_vision`         | `CLIP_VISION`      | モデルの要件に合わせて視覚データを処理し、視覚的コンテキストの理解を向上させます。 |
| `init_image`          | `IMAGE`            | モデルの初期画像入力として機能し、さらなる画像ベースの操作の基準を設定します。 |
| `vae`                 | `VAE`              | 変分オートエンコーダーの出力を統合し、画像の生成や修正を可能にします。 |
| `width`               | `INT`              | 出力画像の幅を指定し、モデルのニーズに応じた動的なリサイズを可能にします。 |
| `height`              | `INT`              | 出力画像の高さを決定し、出力寸法のカスタマイズを可能にします。 |
| `batch_size`          | `INT`              | 単一のバッチで処理される画像の数を制御し、計算効率を最適化します。 |
| `elevation`           | `FLOAT`            | 3Dモデルレンダリングのための仰角を調整し、空間的理解を向上させます。 |
| `azimuth`             | `FLOAT`            | 3Dモデルの視覚化のための方位角を変更し、方向の認識を改善します。 |

## 出力

| パラメータ     | データ型 | 説明 |
|---------------|--------------|-------------|
| `positive`    | `CONDITIONING` | ポジティブな条件付けベクトルを生成し、モデルのポジティブな特徴強化を支援します。 |
| `negative`    | `CONDITIONING` | ネガティブな条件付けベクトルを生成し、特定の特徴の回避を支援します。 |
| `latent`      | `LATENT`     | 潜在表現を作成し、データに対するモデルの深い洞察を促進します。 |
