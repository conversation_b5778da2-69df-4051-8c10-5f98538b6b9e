Metadata-Version: 2.1
Name: matrix-client
Version: 0.4.0
Summary: Client-Server SDK for Matrix
Home-page: https://github.com/matrix-org/matrix-python-sdk
Author: The Matrix.org Team
Author-email: <EMAIL>
License: Apache License, Version 2.0
Keywords: chat sdk matrix matrix.org
Platform: UNKNOWN
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Communications :: Chat
Classifier: Topic :: Communications :: Conferencing
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: requests (~=2.22)
Requires-Dist: urllib3 (~=1.21)
Provides-Extra: doc
Requires-Dist: Sphinx (==1.*,>=1.7.6) ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme (==0.1.*,>=0.1.9) ; extra == 'doc'
Requires-Dist: sphinxcontrib-napoleon (==0.5.*,>=0.5.3) ; extra == 'doc'
Provides-Extra: e2e
Requires-Dist: python-olm (~=3.1) ; extra == 'e2e'
Requires-Dist: canonicaljson (~=1.1) ; extra == 'e2e'
Provides-Extra: test
Requires-Dist: pytest (<6.0.0,>=4.6) ; extra == 'test'
Requires-Dist: responses (==0.10.*,>=0.10.6) ; extra == 'test'

Matrix Client SDK for Python
============================

.. image:: https://img.shields.io/pypi/v/matrix-client.svg?maxAge=600
  :target: https://pypi.python.org/pypi/matrix-client
  :alt: Latest Version
.. image:: https://travis-ci.org/matrix-org/matrix-python-sdk.svg?branch=master
  :target: https://travis-ci.org/matrix-org/matrix-python-sdk
  :alt: Travis-CI Results
.. image:: https://coveralls.io/repos/github/matrix-org/matrix-python-sdk/badge.svg?branch=master
  :target: https://coveralls.io/github/matrix-org/matrix-python-sdk?branch=master
  :alt: coveralls.io Results
.. image:: https://img.shields.io/matrix/matrix-python-sdk:matrix.org
   :target: https://matrix.to/#/%23matrix-python-sdk:matrix.org
   :alt: Matrix chatroom
.. image:: https://img.shields.io/badge/docs-stable-blue
   :target: https://matrix-org.github.io/matrix-python-sdk/
   :alt: Documentation


Matrix client-server SDK for Python 2.7 and 3.4+

Project Status
--------------

We strongly recommend using the `matrix-nio`_ library rather than this
sdk. It is both more featureful and more actively maintained.

This sdk is currently lightly maintained without any person ultimately
responsible for the project. Pull-requests **may** be reviewed, but no
new-features or bug-fixes are being actively developed. For more info
or to volunteer to help, please see
https://github.com/matrix-org/matrix-python-sdk/issues/279 or come
chat in `#matrix-python-sdk:matrix.org`_.

.. _`matrix-nio`: https://github.com/poljar/matrix-nio
.. _`#matrix-python-sdk:matrix.org`: https://matrix.to/#/%23matrix-python-sdk:matrix.org

Installation
============
Stable release
--------------
Install with pip from pypi. This will install all necessary dependencies as well.

.. code:: shell

   pip install matrix_client

Development version
-------------------
Install using ``setup.py`` in root project directory. This will also install all
needed dependencies.

.. code:: shell

   git clone https://github.com/matrix-org/matrix-python-sdk.git
   cd matrix-python-sdk
   python setup.py install

Usage
=====
The SDK provides 2 layers of interaction. The low-level layer just wraps the
raw HTTP API calls. The high-level layer wraps the low-level layer and provides
an object model to perform actions on.

Client:

.. code:: python

    from matrix_client.client import MatrixClient

    client = MatrixClient("http://localhost:8008")

    # New user
    token = client.register_with_password(username="foobar", password="monkey")

    # Existing user
    token = client.login(username="foobar", password="monkey")

    room = client.create_room("my_room_alias")
    room.send_text("Hello!")


API:

.. code:: python

    from matrix_client.api import MatrixHttpApi

    matrix = MatrixHttpApi("https://matrix.org", token="some_token")
    response = matrix.send_message("!roomid:matrix.org", "Hello!")


Structure
=========
The SDK is split into two modules: ``api`` and ``client``.

API
---
This contains the raw HTTP API calls and has minimal business logic. You can
set the access token (``token``) to use for requests as well as set a custom
transaction ID (``txn_id``) which will be incremented for each request.

Client
------
This encapsulates the API module and provides object models such as ``Room``.

Samples
=======
A collection of samples are included, written in Python 3.

You can either install the SDK, or run the sample like this:

.. code:: shell

    PYTHONPATH=. python samples/samplename.py

Building the Documentation
==========================

The documentation can be built by installing ``sphinx`` and ``sphinx_rtd_theme``.

Simple run ``make`` inside ``docs`` which will list the avaliable output formats.


