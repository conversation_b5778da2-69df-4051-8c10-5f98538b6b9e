
SDTurboSchedulerは、指定されたノイズ除去レベルとステップ数に基づいて、画像サンプリングのためのシグマ値のシーケンスを生成するように設計されています。これは、特定のモデルのサンプリング機能を活用してこれらのシグマ値を生成し、画像生成中のノイズ除去プロセスを制御するために重要です。

## 入力

| パラメータ | データ型 | 説明 |
| --- | --- | --- |
| `model` | `MODEL` | モデルパラメータは、シグマ値生成に使用される生成モデルを指定します。これは、スケジューラーの特定のサンプリング動作と機能を決定するために重要です。 |
| `steps` | `INT` | ステップパラメータは、生成されるシグマシーケンスの長さを決定し、ノイズ除去プロセスの粒度に直接影響を与えます。 |
| `denoise` | `FLOAT` | デノイズパラメータは、シグマシーケンスの開始点を調整し、画像生成中に適用されるノイズ除去レベルをより細かく制御することを可能にします。 |

## 出力

| パラメータ | データ型 | 説明 |
| --- | --- | --- |
| `sigmas` | `SIGMAS` | 指定されたモデル、ステップ、およびデノイズレベルに基づいて生成されたシグマ値のシーケンス。これらの値は、画像生成におけるノイズ除去プロセスを制御するために不可欠です。 |
