{"id": "e1a07aa5-d763-48f2-aa3a-6c64ab0e371d", "revision": 0, "last_node_id": 34, "last_link_id": 65, "nodes": [{"id": 22, "type": "CLIPSetLastLayer", "pos": [0, 1030], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 27}], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [13, 14]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 7, "type": "CLIPTextEncode", "pos": [390, 300], "size": [410, 190], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["lowres, bad anatomy, bad hands, (text:1.1), blurry, mutated hands and fingers, mutation, deformed face, ugly, (logo:1.1), cropped, worst quality, jpeg, (jpeg artifacts), deleted, old, oldest, (censored), (bad aesthetic), (mosaic censoring, bar censor, blur censor) earphones"], "color": "#223", "bgcolor": "#335"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [830, 40], "size": [318.5, 474], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 54}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7, 64]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [749119555793879, "randomize", 14, 8, "dpmpp_sde", "simple", 1]}, {"id": 11, "type": "VAEDecode", "pos": [930, 1240], "size": [210, 46], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 60}, {"name": "vae", "type": "VAE", "link": 63}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 8, "type": "VAEDecode", "pos": [920, -60], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [10]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 33, "type": "<PERSON>downNote", "pos": [-370, 40], "size": [330, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [Latent Upscale - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/#more-examples) — Overview\n> \n> [ComfyUI Image Upscale - docs.comfy.org](https://docs.comfy.org/tutorials/basic/upscale) — Upscaling step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}, {"id": 34, "type": "LatentUpscaleBy", "pos": [400, 730], "size": [270, 82], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 64}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [65]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LatentUpscaleBy"}, "widgets_values": ["nearest-exact", 1.5], "color": "#322", "bgcolor": "#533"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [30, 40], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [54]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [3, 5]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [8]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "DreamShaper_8_pruned.safetensors", "url": "https://huggingface.co/Lykon/DreamShaper/resolve/main/DreamShaper_8_pruned.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["DreamShaper_8_pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 13, "type": "CheckpointLoaderSimple", "pos": [0, 880], "size": [315, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [56]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [27]}, {"name": "VAE", "type": "VAE", "links": [63]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "v1-5-pruned-emaonly-fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["v1-5-pruned-emaonly-fp16.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 5, "type": "EmptyLatentImage", "pos": [30, 240], "size": [315, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1], "color": "#323", "bgcolor": "#535"}, {"id": 14, "type": "CLIPTextEncode", "pos": [390, 1170], "size": [410, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 13}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [58]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), (text:1.1), letters, numbers, error, cropped, (jpeg artifacts:1.2), (signature:1.1), (watermark:1.1), username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.1), extra legs, (forehead mark)  (penis)"], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "CLIPTextEncode", "pos": [390, 40], "size": [410, 220], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) beautiful landscape, serene lake, pine forest, snow mountain, blue sky, soft clouds, morning light, peaceful, nature\n"], "color": "#232", "bgcolor": "#353"}, {"id": 15, "type": "CLIPTextEncode", "pos": [390, 880], "size": [410, 240], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 14}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) beautiful landscape, serene lake, pine forest, snow mountain, blue sky, soft clouds, morning light, peaceful, nature, ultra-detailed, photorealistic, high resolution, intricate textures\n"], "color": "#232", "bgcolor": "#353"}, {"id": 10, "type": "SaveImage", "pos": [1180, 40], "size": [600, 650], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 10}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}, {"id": 32, "type": "K<PERSON><PERSON><PERSON>", "pos": [830, 880], "size": [315, 474], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 56}, {"name": "positive", "type": "CONDITIONING", "link": 57}, {"name": "negative", "type": "CONDITIONING", "link": 58}, {"name": "latent_image", "type": "LATENT", "link": 65}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [60]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [874134555328346, "randomize", 8, 13, "dpmpp_sde", "simple", 0.3500000000000001]}, {"id": 12, "type": "SaveImage", "pos": [1180, 880], "size": [850, 920], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 12}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [10, 8, 0, 10, 0, "IMAGE"], [12, 11, 0, 12, 0, "IMAGE"], [13, 22, 0, 14, 0, "CLIP"], [14, 22, 0, 15, 0, "CLIP"], [27, 13, 1, 22, 0, "CLIP"], [54, 4, 0, 3, 0, "MODEL"], [56, 13, 0, 32, 0, "MODEL"], [57, 15, 0, 32, 1, "CONDITIONING"], [58, 14, 0, 32, 2, "CONDITIONING"], [60, 32, 0, 11, 0, "LATENT"], [63, 13, 2, 11, 1, "VAE"], [64, 3, 0, 34, 0, "LATENT"], [65, 34, 0, 32, 3, "LATENT"]], "groups": [{"id": 1, "title": "Step1-1: load model", "bounding": [20, -30, 335, 181.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step1-2: load model", "bounding": [-10, 810, 335, 181.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "First pass image size", "bounding": [20, 170, 335, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "First pass prompt", "bounding": [380, -30, 430, 533.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8360000000000127, "offset": [28.577896898123527, -691.4952052236044]}, "frontendVersion": "1.23.4"}, "version": 0.4}