
VAE内补编码器节点，主要用于将已有的需要修改的图像转换为潜空间图像，除了和**VAE编码器** 一样需要VAE和图像的输入信息之外，它需要额外提供遮罩提供给模型，让采样器知道该对哪个部分进行去噪（重新生成）在我的使用过程中，我感觉这个节点适合用于对于局部重绘替换成与原始图像完全不相关的内容。
>
    **注意**
    使用此节点时，请勿将采样器中的 ```denoise降噪```设置过低，否则将出现重绘后图像蒙版区域为灰白色块情况
不同```denoise降噪```值设置下使用该节点输出图像效果如下

## 输入

| 参数名称       | 数据类型 | 作用                                                         |
|----------------|----------|--------------------------------------------------------------|
| `pixels`       | `IMAGE`  | 要编码的输入图像。在编码之前，此图像将进行预处理和调整大小以匹配VAE模型的预期输入尺寸。 |
| `vae`          | `VAE`    | 用于将图像编码为其潜在表示的VAE模型。它在转换过程中起着关键作用，决定了输出潜在空间的质量和特性。 |
| `mask`         | `MASK`   | 一个遮罩，指示输入图像中要进行修复的区域。它用于在编码前修改图像，确保VAE专注于相关区域。       |
| `grow_mask_by` | `INT`    | 指定扩展修复遮罩的程度，以确保潜在空间中的无缝过渡。较大的值会增加修复影响的区域。       |

## 输出

| 参数名称 | 数据类型 | 作用                                       |
|----------|----------|--------------------------------------------|
| `latent` | `LATENT` | 输出包括图像的编码潜在表示和噪声遮罩，这两者对于后续的修复任务都至关重要。 |
