このノードは、`ComfyUI/models/vae` フォルダーにあるモデルを検出し、
また、extra_model_paths.yaml ファイルで設定された追加パスのモデルも読み込みます。
場合によっては、**ComfyUIのインターフェースを更新する**必要があるかもしれませんので、対応するフォルダー内のモデルファイルを読み込むことができます。

VAELoaderノードは、Variational Autoencoder（VAE）モデルをロードするために設計されており、標準および近似VAEの両方に対応しています。'taesd'や'taesdxl'モデルの特別な処理を含む、名前によるVAEのロードをサポートし、VAEの特定の設定に基づいて動的に調整します。

## Input types（入力タイプ）

| Field（フィールド）   | Comfy dtype       | Description（説明）                                                                                   |
|---------|-------------------|-----------------------------------------------------------------------------------------------|
| `vae_name` | `COMBO[STRING]`    | ロードするVAEの名前を指定し、どのVAEモデルが取得されロードされるかを決定します。'taesd'や'taesdxl'を含む、事前定義されたVAE名の範囲をサポートしています。 |

## Output types（出力タイプ）

| Field（フィールド） | Data Type | Description（説明）                                                              |
|-------|-------------|--------------------------------------------------------------------------|
| `vae`  | `VAE`       | ロードされたVAEモデルを返し、エンコードやデコードなどのさらなる操作の準備が整います。出力は、ロードされたモデルの状態をカプセル化したモデルオブジェクトです。 |
