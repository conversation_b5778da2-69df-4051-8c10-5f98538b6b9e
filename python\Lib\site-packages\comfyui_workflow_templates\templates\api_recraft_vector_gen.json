{"id": "23f22777-724e-432f-bd47-5edc7f13156e", "revision": 0, "last_node_id": 32, "last_link_id": 30, "nodes": [{"id": 4, "type": "SaveSVG", "pos": [601.1892700195312, 528.0665893554688], "size": [413.03875732421875, 250.30764770507812], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "svg", "type": "SVG", "link": 2}], "outputs": [], "properties": {"Node name for S&R": "SaveSVG"}, "widgets_values": ["svg/ComfyUI"]}, {"id": 27, "type": "RecraftColorRGB", "pos": [-441.6960754394531, 463.4327392578125], "size": [276.515380859375, 106], "flags": {}, "order": 3, "mode": 4, "inputs": [{"name": "recraft_color", "shape": 7, "type": "RECRAFT_COLOR", "link": 25}], "outputs": [{"name": "recraft_color", "type": "RECRAFT_COLOR", "links": [26]}], "properties": {"Node name for S&R": "RecraftColorRGB"}, "widgets_values": [241, 44, 208]}, {"id": 28, "type": "RecraftColorRGB", "pos": [-441.6960754394531, 613.4322509765625], "size": [276.515380859375, 106], "flags": {}, "order": 4, "mode": 4, "inputs": [{"name": "recraft_color", "shape": 7, "type": "RECRAFT_COLOR", "link": 26}], "outputs": [{"name": "recraft_color", "type": "RECRAFT_COLOR", "links": [27]}], "properties": {"Node name for S&R": "RecraftColorRGB"}, "widgets_values": [241, 255, 65]}, {"id": 26, "type": "RecraftColorRGB", "pos": [-441.6960754394531, 303.43304443359375], "size": [276.515380859375, 106], "flags": {}, "order": 0, "mode": 4, "inputs": [{"name": "recraft_color", "shape": 7, "type": "RECRAFT_COLOR", "link": null}], "outputs": [{"name": "recraft_color", "type": "RECRAFT_COLOR", "links": [25]}], "properties": {"Node name for S&R": "RecraftColorRGB"}, "widgets_values": [24, 44, 196]}, {"id": 30, "type": "RecraftColorRGB", "pos": [-441.78997802734375, 825.3909912109375], "size": [276.515380859375, 106], "flags": {}, "order": 1, "mode": 4, "inputs": [{"name": "recraft_color", "shape": 7, "type": "RECRAFT_COLOR", "link": null}], "outputs": [{"name": "recraft_color", "type": "RECRAFT_COLOR", "links": [30]}], "properties": {"Node name for S&R": "RecraftColorRGB"}, "widgets_values": [24, 44, 196]}, {"id": 31, "type": "<PERSON>downNote", "pos": [179.62075805664062, 232.27223205566406], "size": [389.0563049316406, 249.69961547851562], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 29, "type": "RecraftControls", "pos": [-124.**************, 544.*************], "size": [287.*************, 55.**************], "flags": {}, "order": 5, "mode": 4, "inputs": [{"name": "colors", "shape": 7, "type": "RECRAFT_COLOR", "link": 27}, {"name": "background_color", "shape": 7, "type": "RECRAFT_COLOR", "link": 30}], "outputs": [{"name": "recraft_controls", "type": "RECRAFT_CONTROLS", "links": [29]}], "properties": {"Node name for S&R": "RecraftControls"}, "widgets_values": []}, {"id": 3, "type": "RecraftTextToVectorNode", "pos": [179.61141967773438, 532.3265991210938], "size": [400, 300.3140563964844], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "negative_prompt", "shape": 7, "type": "STRING", "link": null}, {"name": "recraft_controls", "shape": 7, "type": "RECRAFT_CONTROLS", "link": 29}], "outputs": [{"name": "SVG", "type": "SVG", "links": [2]}], "properties": {"Node name for S&R": "RecraftTextToVectorNode"}, "widgets_values": ["A magical academy crest design, featuring a central emblem of a mystical creature, such as a dragon or phoenix, surrounded by arcane symbols, ancient runes, and magical elements. The design should be intricate and ornate, with multiple layers of detail. Incorporate a variety of vibrant colors like deep blues, purples, golds, and silvers, to represent different magical elements like fire, water, and air. The emblem should have a shield-like structure with flowing ribbons, stars, and enchanted accents. The overall style should be grand and regal, evoking a sense of mysticism and wisdom, suitable for a prestigious magical institution.", "None", "1024x1024", 1, 201681460468869, "randomize"]}], "links": [[2, 3, 0, 4, 0, "SVG"], [25, 26, 0, 27, 0, "RECRAFT_COLOR"], [26, 27, 0, 28, 0, "RECRAFT_COLOR"], [27, 28, 0, 29, 0, "RECRAFT_COLOR"], [29, 29, 0, 3, 1, "RECRAFT_CONTROLS"], [30, 30, 0, 29, 1, "RECRAFT_COLOR"]], "groups": [{"id": 1, "title": "Color", "bounding": [-451.69598388671875, 233.4331512451172, 296.515380859375, 499.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Background Color", "bounding": [-451.78997802734375, 751.791015625, 296.515380859375, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.18.5"}, "version": 0.4}