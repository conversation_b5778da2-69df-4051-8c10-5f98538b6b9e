{"id": "cbeeb8a3-8519-434e-a8c4-874227569ca0", "revision": 0, "last_node_id": 18, "last_link_id": 23, "nodes": [{"id": 10, "type": "LoadImage", "pos": [1540, 1430], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [17]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["first.jpg", "image"]}, {"id": 11, "type": "LoadImage", "pos": [1540, 1800], "size": [274.080078125, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [18]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["last.jpg", "image"]}, {"id": 12, "type": "SaveVideo", "pos": [2310, 1440], "size": [630, 530], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 10}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 9, "type": "RunwayFirstLastFrameNode", "pos": [1880, 1440], "size": [400, 360], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "start_frame", "type": "IMAGE", "link": 17}, {"name": "end_frame", "type": "IMAGE", "link": 18}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [10]}], "properties": {"Node name for S&R": "RunwayFirstLastFrameNode"}, "widgets_values": ["wide horizontal pan shot of a girl running across a reflective water surface, behind her vast starry sky with pastel-colored nebula waves swirling, stardust drifting and twinkling stars, cool-to-warm dreamy pastel gradient, soft backlighting by starlight, watercolor and oil painting blend texture, smooth frame-to-frame transitions maintaining continuous nebula motion", 5, "1280:768", 379330984, "randomize"], "color": "#432", "bgcolor": "#653"}, {"id": 1, "type": "<PERSON>downNote", "pos": [1110, 1430], "size": [390, 320], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[10, 9, 0, 12, 0, "VIDEO"], [17, 10, 0, 9, 0, "IMAGE"], [18, 11, 0, 9, 1, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6209213230591556, "offset": [-424.5876169669926, -1026.1062443956946]}, "frontendVersion": "1.21.0"}, "version": 0.4}