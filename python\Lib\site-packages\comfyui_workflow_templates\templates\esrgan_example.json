{"id": "a5a315c0-acbb-4e1e-b8ac-8491715b3383", "revision": 0, "last_node_id": 16, "last_link_id": 20, "nodes": [{"id": 14, "type": "ImageUpscaleWithModel", "pos": [1510, 50], "size": [221.98202514648438, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 14}, {"name": "image", "type": "IMAGE", "link": 15}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [16]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "ImageUpscaleWithModel"}, "widgets_values": []}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [10, 160], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [1]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [17, 18]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [19]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "DreamShaper_8_pruned.safetensors", "url": "https://huggingface.co/Lykon/DreamShaper/resolve/main/DreamShaper_8_pruned.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["DreamShaper_8_pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 5, "type": "EmptyLatentImage", "pos": [10, 370], "size": [310, 110], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1], "color": "#322", "bgcolor": "#533"}, {"id": 13, "type": "UpscaleModelLoader", "pos": [10, 60], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "slot_index": 0, "links": [14]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "UpscaleModelLoader", "models": [{"name": "RealESRGAN_x4plus.safetensors", "url": "https://huggingface.co/Comfy-Org/Real-ESRGAN_repackaged/resolve/main/RealESRGAN_x4plus.safetensors", "directory": "upscale_models"}]}, "widgets_values": ["RealESRGAN_x4plus.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 15, "type": "<PERSON>downNote", "pos": [-330, 10], "size": [312, 152], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "Tutorials", "properties": {}, "widgets_values": ["## Tutorials\n[Docs](https://docs.comfy.org/tutorials/basic/upscale) \n\n## Workflow Overview\n[Upscale Models - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/upscale_models/) "], "color": "#432", "bgcolor": "#653"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [850, 50], "size": [310, 480], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [479320868631054, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [370, 50], "size": [423.1099853515625, 201.5616455078125], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 17}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A rainy city street at night, neon signs illuminating the wet pavement, reflections of streetlights on puddles, pedestrians with umbrellas, hyper-realistic details, atmospheric, cinematic lighting, soft focus"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [370, 290], "size": [423.1099853515625, 180.61000061035156], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 18}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad hands,(worst quality),(low quality),(normal quality:),lowres,bad anatomy,(bad hands),((monochrome)),((grayscale)) watermark,moles,many fingers,(broken hands),nsfw, "], "color": "#223", "bgcolor": "#335"}, {"id": 8, "type": "VAEDecode", "pos": [1180, 50], "size": [210, 46], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 19}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [15, 20]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1510, 150], "size": [455.989990234375, 553.0900268554688], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 16}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": ["ComfyUI"]}, {"id": 16, "type": "PreviewImage", "pos": [1180, 150], "size": [290, 330], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 20}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "PreviewImage"}, "widgets_values": []}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [14, 13, 0, 14, 0, "UPSCALE_MODEL"], [15, 8, 0, 14, 1, "IMAGE"], [16, 14, 0, 9, 0, "IMAGE"], [17, 4, 1, 6, 0, "CLIP"], [18, 4, 1, 7, 0, "CLIP"], [19, 4, 2, 8, 1, "VAE"], [20, 8, 0, 16, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Step 1- Load models", "bounding": [0, -20, 330, 300], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step2-Set image size", "bounding": [0, 300, 330, 190], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - Prompt", "bounding": [350, -20, 460, 510], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Second - Upscale image", "bounding": [1500, -20, 475.989990234375, 736.6900024414062], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "First - Image generation", "bounding": [830, -20, 650, 560], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.0152559799477237, "offset": [873.9457952395927, 411.574657129133]}, "frontendVersion": "1.23.4"}, "version": 0.4}