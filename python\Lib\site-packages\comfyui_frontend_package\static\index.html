<!doctype html><html lang="en"><head><meta charset="UTF-8"><title>ComfyUI</title><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><link rel="stylesheet" href="materialdesignicons.min.css"/><link rel="stylesheet" href="user.css"/><link rel="stylesheet" href="api/userdata/user.css"/><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="black"><link rel="manifest" href="./assets/manifest-CebUEmtR.json"><script type="importmap">{
  "imports": {
    "primevue": "./assets/vendor-primevue-DRGUzLTK.js",
    "vue": "./assets/vendor-vue-H4UETSFK.js",
    "vue-i18n": "./assets/vendor-vue-i18n-DiivlA3w.js"
  }
}</script><script type="module" crossorigin src="./assets/index-CWzmkThr.js"></script><link rel="modulepreload" crossorigin href="./assets/vendor-vue-H4UETSFK.js"><link rel="modulepreload" crossorigin href="./assets/vendor-primevue-DRGUzLTK.js"><link rel="modulepreload" crossorigin href="./assets/vendor-vue-i18n-DiivlA3w.js"><link rel="stylesheet" crossorigin href="./assets/index-DXX-SSok.css"></head><body class="litegraph grid"><div id="vue-app"></div></body></html>