このノードは、特定のタイムステップ範囲を設定することで、条件付けの時間的側面を調整するように設計されています。これにより、条件付けプロセスの開始点と終了点を正確に制御し、よりターゲットを絞った効率的な生成を可能にします。

## 入力

| パラメータ | データ型 | 説明 |
| --- | --- | --- |
| `CONDITIONING` | CONDITIONING | 条件付け入力は、生成プロセスの現在の状態を表し、このノードは特定のタイムステップ範囲を設定することでそれを修正します。 |
| `start` | `FLOAT` | startパラメータは、生成プロセス全体のパーセンテージとしてタイムステップ範囲の開始を指定し、条件付け効果が始まるタイミングを微調整することができます。 |
| `end` | `FLOAT` | endパラメータは、タイムステップ範囲の終了点をパーセンテージで定義し、条件付け効果の持続時間と終了を正確に制御することができます。 |

## 出力

| パラメータ | データ型 | 説明 |
| --- | --- | --- |
| `CONDITIONING` | CONDITIONING | 出力は、指定されたタイムステップ範囲が適用された修正済みの条件付けであり、さらなる処理や生成の準備が整っています。 |
