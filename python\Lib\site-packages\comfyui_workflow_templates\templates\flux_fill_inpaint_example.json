{"last_node_id": 45, "last_link_id": 100, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [307, 282], "size": [425.28, 180.61], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 63}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [81], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 32, "type": "VAELoader", "pos": [1352, 422], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [60, 82], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"]}, {"id": 26, "type": "FluxGuidance", "pos": [593, 44], "size": [317.4, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 41}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "shape": 3, "links": [80], "slot_index": 0}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 34, "type": "DualCLIPLoader", "pos": [-237, 79], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [62, 63]}], "properties": {"Node name for S&R": "DualCLIPLoader", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true", "directory": "text_encoders"}, {"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 39, "type": "DifferentialDiffusion", "pos": [1001, -68], "size": [277.2, 26], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 85}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [86], "slot_index": 0}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 8, "type": "VAEDecode", "pos": [1620, 98], "size": [210, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 60}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [95], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 38, "type": "InpaintModelConditioning", "pos": [952, 78], "size": [302.4, 138], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 80}, {"name": "negative", "type": "CONDITIONING", "link": 81}, {"name": "vae", "type": "VAE", "link": 82}, {"name": "pixels", "type": "IMAGE", "link": 99}, {"name": "mask", "type": "MASK", "link": 100}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [77], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [78], "slot_index": 1}, {"name": "latent", "type": "LATENT", "links": [88], "slot_index": 2}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 9, "type": "SaveImage", "pos": [1877, 101], "size": [828.95, 893.85], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 95}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1280, 100], "size": [315, 262], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 86}, {"name": "positive", "type": "CONDITIONING", "link": 77}, {"name": "negative", "type": "CONDITIONING", "link": 78}, {"name": "latent_image", "type": "LATENT", "link": 88}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [656821733471329, "randomize", 20, 1, "euler", "normal", 1]}, {"id": 31, "type": "UNETLoader", "pos": [602, -120], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [85], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "flux1-fill-dev.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-Fill-dev/blob/main/flux1-fill-dev.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["flux1-fill-dev.safetensors", "default"]}, {"id": 17, "type": "LoadImage", "pos": [587, 312], "size": [315, 314.0], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "shape": 3, "links": [99], "slot_index": 0}, {"name": "MASK", "type": "MASK", "shape": 3, "links": [100], "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["yosemite_inpaint_example.png", "image"]}, {"id": 23, "type": "CLIPTextEncode", "pos": [144, -7], "size": [422.85, 164.31], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 62}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [41], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["anime girl with massive fennec ears blonde hair blue eyes wearing a pink shirt"], "color": "#232", "bgcolor": "#353"}, {"id": 45, "type": "<PERSON>downNote", "pos": [-225, 255], "size": [225, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/flux/#fill-inpainting-model)"], "color": "#432", "bgcolor": "#653"}], "links": [[7, 3, 0, 8, 0, "LATENT"], [41, 23, 0, 26, 0, "CONDITIONING"], [60, 32, 0, 8, 1, "VAE"], [62, 34, 0, 23, 0, "CLIP"], [63, 34, 0, 7, 0, "CLIP"], [77, 38, 0, 3, 1, "CONDITIONING"], [78, 38, 1, 3, 2, "CONDITIONING"], [80, 26, 0, 38, 0, "CONDITIONING"], [81, 7, 0, 38, 1, "CONDITIONING"], [82, 32, 0, 38, 2, "VAE"], [85, 31, 0, 39, 0, "MODEL"], [86, 39, 0, 3, 0, "MODEL"], [88, 38, 2, 3, 3, "LATENT"], [95, 8, 0, 9, 0, "IMAGE"], [99, 17, 0, 38, 3, "IMAGE"], [100, 17, 1, 38, 4, "MASK"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.21, "offset": [566.62, 207.73]}}, "version": 0.4}