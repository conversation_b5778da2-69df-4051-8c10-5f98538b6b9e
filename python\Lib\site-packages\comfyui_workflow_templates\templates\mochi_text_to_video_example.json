{"last_node_id": 40, "last_link_id": 79, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 187], "size": [315, 262], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 79}, {"name": "positive", "type": "CONDITIONING", "link": 46}, {"name": "negative", "type": "CONDITIONING", "link": 52}, {"name": "latent_image", "type": "LATENT", "link": 38}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [35], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [704883238463297, "randomize", 30, 4.5, "euler", "simple", 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": [422.85, 164.31], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [46], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a fox moving quickly in a beautiful winter scenery nature trees sunset tracking camera"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": [425.28, 180.61], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [52], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 8, "type": "VAEDecode", "pos": [1210, 190], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 35}, {"name": "vae", "type": "VAE", "link": 76}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [56], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 21, "type": "EmptyMochiLatentVideo", "pos": [520, 620], "size": [315, 130], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [38], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyMochiLatentVideo"}, "widgets_values": [848, 480, 37, 1]}, {"id": 28, "type": "SaveAnimatedWEBP", "pos": [1460, 190], "size": [847.3, 602.03], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 56}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", 24, false, 80, "default"]}, {"id": 37, "type": "UNETLoader", "pos": [420, 40], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [79], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "mochi_preview_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/mochi_preview_repackaged/resolve/main/split_files/diffusion_models/mochi_preview_bf16.safetensors?download=true", "directory": "diffusion_models"}]}, "widgets_values": ["mochi_preview_bf16.safetensors", "default"]}, {"id": 38, "type": "CLIPLoader", "pos": [40, 270], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [74, 75], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPLoader", "models": [{"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["t5xxl_fp16.safetensors", "mochi", "default"]}, {"id": 39, "type": "VAELoader", "pos": [890, 500], "size": [278.68, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [76]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "mochi_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/mochi_preview_repackaged/resolve/main/split_files/vae/mochi_vae.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["mochi_vae.safetensors"]}, {"id": 40, "type": "<PERSON>downNote", "pos": [45, 405], "size": [225, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/mochi/)"], "color": "#432", "bgcolor": "#653"}], "links": [[35, 3, 0, 8, 0, "LATENT"], [38, 21, 0, 3, 3, "LATENT"], [46, 6, 0, 3, 1, "CONDITIONING"], [52, 7, 0, 3, 2, "CONDITIONING"], [56, 8, 0, 28, 0, "IMAGE"], [74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [76, 39, 0, 8, 1, "VAE"], [79, 37, 0, 3, 0, "MODEL"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [35.42, 115.48]}}, "version": 0.4}