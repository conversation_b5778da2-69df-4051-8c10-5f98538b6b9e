{"id": "38d4a359-349a-444f-acc1-acba3173c650", "revision": 0, "last_node_id": 43, "last_link_id": 73, "nodes": [{"id": 28, "type": "SamplerCustomAdvanced", "pos": [1110, 100], "size": [202.53378295898438, 106], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 33}, {"name": "guider", "type": "GUIDER", "link": 34}, {"name": "sampler", "type": "SAMPLER", "link": 35}, {"name": "sigmas", "type": "SIGMAS", "link": 36}, {"name": "latent_image", "type": "LATENT", "link": 49}], "outputs": [{"name": "output", "type": "LATENT", "slot_index": 0, "links": [46]}, {"name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 15, "type": "ReferenceLatent", "pos": [290, 100], "size": [197.712890625, 46], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 16}, {"name": "latent", "shape": 7, "type": "LATENT", "link": 18}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [68]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "ReferenceLatent"}, "widgets_values": []}, {"id": 29, "type": "ReferenceLatent", "pos": [290, 260], "size": [197.712890625, 46], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 44}, {"name": "latent", "shape": 7, "type": "LATENT", "link": 42}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [69]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "ReferenceLatent"}, "widgets_values": []}, {"id": 38, "type": "ReferenceLatent", "pos": [530, 100], "size": [197.712890625, 46], "flags": {}, "order": 21, "mode": 4, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 68}, {"name": "latent", "shape": 7, "type": "LATENT", "link": 72}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [70]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "ReferenceLatent"}, "widgets_values": []}, {"id": 39, "type": "ReferenceLatent", "pos": [540, 250], "size": [197.712890625, 46], "flags": {}, "order": 22, "mode": 4, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 69}, {"name": "latent", "shape": 7, "type": "LATENT", "link": 73}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [71]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "ReferenceLatent"}, "widgets_values": []}, {"id": 11, "type": "EmptySD3LatentImage", "pos": [127.8342056274414, 691.1400756835938], "size": [270, 106], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 55}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 56}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [49]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 17, "type": "ImageScaleToTotalPixels", "pos": [-170.0668182373047, 520.1840209960938], "size": [270, 82], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 20}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [21, 54]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "ImageScaleToTotalPixels"}, "widgets_values": ["area", 1]}, {"id": 21, "type": "RandomNoise", "pos": [770, 110], "size": [310, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [33]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "RandomNoise"}, "widgets_values": [822957660815591, "randomize"]}, {"id": 27, "type": "DualCFGGuider", "pos": [770, 240], "size": [310, 142], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 53}, {"name": "cond1", "type": "CONDITIONING", "link": 70}, {"name": "cond2", "type": "CONDITIONING", "link": 71}, {"name": "negative", "type": "CONDITIONING", "link": 45}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [34]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "DualCFGGuider"}, "widgets_values": [5, 2]}, {"id": 14, "type": "VAEEncode", "pos": [120, 520], "size": [140, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 21}, {"name": "vae", "type": "VAE", "link": 15}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [18, 42]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 13, "type": "VAELoader", "pos": [-520, 360], "size": [290, 60], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [14, 15, 62]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 10, "type": "CLIPLoader", "pos": [-520, 210], "size": [290, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [10, 11]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "CLIPLoader", "models": [{"name": "qwen_2.5_vl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/text_encoders/qwen_2.5_vl_fp16.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["qwen_2.5_vl_fp16.safetensors", "omnigen2", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "UNETLoader", "pos": [-520, 90], "size": [290, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [52, 53]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "UNETLoader", "models": [{"name": "omnigen2_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/omnigen2_fp16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["omnigen2_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 16, "type": "LoadImage", "pos": [-510, 520], "size": [280, 310], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [20]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_256804_.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 33, "type": "LoadImage", "pos": [-510, 930], "size": [280, 310], "flags": {}, "order": 5, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [63]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_256804_.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 32, "type": "GetImageSize", "pos": [-170, 690], "size": [246, 136], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 54}], "outputs": [{"name": "width", "type": "INT", "links": [55]}, {"name": "height", "type": "INT", "links": [56]}, {"name": "batch_size", "type": "INT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "GetImageSize"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [520, 690], "size": [810, 580], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42"}, "widgets_values": ["ComfyUI"]}, {"id": 23, "type": "BasicScheduler", "pos": [770, 430], "size": [210, 106], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 52}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [36]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 20, "type": "KSamplerSelect", "pos": [770, 580], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [35]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 8, "type": "VAEDecode", "pos": [530, 600], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 46}, {"name": "vae", "type": "VAE", "link": 14}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 7, "type": "CLIPTextEncode", "pos": [-170, 280], "size": [410, 130], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 10}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [44, 45]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["deformed, blurry, over saturation, bad anatomy, disfigured, poorly drawn face, mutation, mutated, extra_limb, ugly, poorly drawn hands, fused fingers, messy drawing, broken legs censor, censored, censor_bar"], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "CLIPTextEncode", "pos": [-170, 100], "size": [410, 140], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 11}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [16]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Transform character into crystal material, transparent crystal texture, sparkling surface, prismatic light effects, magical appearance, elegant translucent look"], "color": "#232", "bgcolor": "#353"}, {"id": 40, "type": "<PERSON>downNote", "pos": [-890, 60], "size": [340, 400], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "Model links", "properties": {}, "widgets_values": ["## Docs\n[English](http://docs.comfy.org/tutorials/image/omnigen/omnigen2) | [中文](http://docs.comfy.org/zh-CN/tutorials/image/omnigen/omnigen2)\n\n## Model links\n\n**diffusion model**\n\n- [omnigen2_fp16.safetensors](https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/omnigen2_fp16.safetensors)\n\n**vae**\n\n- [ae.safetensors](https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/blob/main/split_files/vae/ae.safetensors)\n\n**text encoder**\n\n- [qwen_2.5_vl_fp16.safetensors](https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/text_encoders/qwen_2.5_vl_fp16.safetensors)\n\nFile save location\n```\n📂 ComfyUI/\n├── 📂 models/\n│   ├── 📂 diffusion_models/\n│   │   └── omnigen2_fp16.safetensors\n│   ├── 📂 vae/\n│   │   └── ae.safetensor\n│   └── 📂 text_encoders/\n│       └── qwen_2.5_vl_fp16.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 37, "type": "VAEEncode", "pos": [-50, 1030], "size": [140, 46], "flags": {}, "order": 17, "mode": 4, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 61}, {"name": "vae", "type": "VAE", "link": 62}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [72, 73]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 43, "type": "<PERSON>downNote", "pos": [-160, 870], "size": [240, 110], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "title": "Custom image size", "properties": {}, "widgets_values": ["To customize the image size, delete the **Get Image Size** node."], "color": "#432", "bgcolor": "#653"}, {"id": 34, "type": "ImageScaleToTotalPixels", "pos": [-170, 1130], "size": [270, 82], "flags": {}, "order": 14, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 63}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [61]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "ImageScaleToTotalPixels"}, "widgets_values": ["area", 1]}, {"id": 42, "type": "<PERSON>downNote", "pos": [-170, 1260], "size": [270, 90], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "title": "Enable the second input image", "properties": {}, "widgets_values": ["Use **Ctrl + B** to switch all nodes in Bypass mode to normal mode to enable the second image input."], "color": "#432", "bgcolor": "#653"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [10, 10, 0, 7, 0, "CLIP"], [11, 10, 0, 6, 0, "CLIP"], [14, 13, 0, 8, 1, "VAE"], [15, 13, 0, 14, 1, "VAE"], [16, 6, 0, 15, 0, "CONDITIONING"], [18, 14, 0, 15, 1, "LATENT"], [20, 16, 0, 17, 0, "IMAGE"], [21, 17, 0, 14, 0, "IMAGE"], [33, 21, 0, 28, 0, "NOISE"], [34, 27, 0, 28, 1, "GUIDER"], [35, 20, 0, 28, 2, "SAMPLER"], [36, 23, 0, 28, 3, "SIGMAS"], [42, 14, 0, 29, 1, "LATENT"], [44, 7, 0, 29, 0, "CONDITIONING"], [45, 7, 0, 27, 3, "CONDITIONING"], [46, 28, 0, 8, 0, "LATENT"], [49, 11, 0, 28, 4, "LATENT"], [52, 12, 0, 23, 0, "MODEL"], [53, 12, 0, 27, 0, "MODEL"], [54, 17, 0, 32, 0, "IMAGE"], [55, 32, 0, 11, 0, "INT"], [56, 32, 1, 11, 1, "INT"], [61, 34, 0, 37, 0, "IMAGE"], [62, 13, 0, 37, 1, "VAE"], [63, 33, 0, 34, 0, "IMAGE"], [68, 15, 0, 38, 0, "CONDITIONING"], [69, 29, 0, 39, 0, "CONDITIONING"], [70, 38, 0, 27, 1, "CONDITIONING"], [71, 39, 0, 27, 2, "CONDITIONING"], [72, 37, 0, 38, 1, "LATENT"], [73, 37, 0, 39, 1, "LATENT"]], "groups": [{"id": 1, "title": "Step 1 - Load models", "bounding": [-530, 20, 320, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step 3 - Prompt", "bounding": [-190, 20, 450, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step 2 - Load image", "bounding": [-530, 440, 320, 400], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Comditioning 1", "bounding": [280, 20, 220.05160522460938, 296.8192443847656], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Custom Sampling", "bounding": [760, 20, 561.83544921875, 630.7272338867188], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "(Optional) - 2nd image input", "bounding": [-530, 850, 322.1171875, 402.537109375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Comditioning 2", "bounding": [520, 20, 220.05160522460938, 296.8192443847656], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Output image size", "bounding": [120, 620, 290, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5644739300537773, "offset": [1461.1142852243397, 281.2289969838054]}, "frontendVersion": "1.23.4", "groupNodes": {}}, "version": 0.4}