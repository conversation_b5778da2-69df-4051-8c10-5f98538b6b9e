{"id": "242a6140-7341-49ca-876b-c01366b39b84", "revision": 0, "last_node_id": 33, "last_link_id": 46, "nodes": [{"id": 10, "type": "CLIPLoader", "pos": [100, 300], "size": [270, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [34, 35]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPLoader", "models": [{"name": "oldt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/resolve/main/text_encoders/oldt5_xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["oldt5_xxl_fp8_e4m3fn_scaled.safetensors", "cosmos", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 15, "type": "VAELoader", "pos": [100, 450], "size": [270, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [17]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [880, 150], "size": [315, 262], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 33}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 40}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [324950799589628, "randomize", 35, 4, "euler", "karras", 1]}, {"id": 8, "type": "VAEDecode", "pos": [880, 460], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [41]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 27, "type": "SaveImage", "pos": [1210, 150], "size": [640, 570], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 41}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": ["Cosmos_"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [420, 390], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 34}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#223", "bgcolor": "#335"}, {"id": 12, "type": "EmptySD3LatentImage", "pos": [101.16511535644531, 606.3223266601562], "size": [270, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [40]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [420, 180], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 35}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["On a rainy night, a girl holds an umbrella and looks at the camera. The rain keeps falling."], "color": "#232", "bgcolor": "#353"}, {"id": 13, "type": "UNETLoader", "pos": [100, 180], "size": [280, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [33]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "UNETLoader", "models": [{"name": "cosmos_predict2_2B_t2i.safetensors", "url": "https://huggingface.co/Comfy-Org/Cosmos_Predict2_repackaged/resolve/main/cosmos_predict2_2B_t2i.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["cosmos_predict2_2B_t2i.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 33, "type": "<PERSON>downNote", "pos": [-360, 140], "size": [429.9635009765625, 282.6522216796875], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "Model links", "properties": {}, "widgets_values": ["[tutorial](http://docs.comfy.org/tutorials/image/cosmos/cosmos-predict2-t2i)｜[教程](http://docs.comfy.org/zh-CN/tutorials/image/cosmos/cosmos-predict2-t2i)\n\n**Diffusion model**\n\n- [cosmos_predict2_2B_t2i.safetensors](https://huggingface.co/Comfy-Org/Cosmos_Predict2_repackaged/resolve/main/cosmos_predict2_2B_t2i.safetensors)\n- [more](https://huggingface.co/Comfy-Org/Cosmos_Predict2_repackaged/)\n\n**Text encoder**\n\n[oldt5_xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/resolve/main/text_encoders/oldt5_xxl_fp8_e4m3fn_scaled.safetensors)\n\n**VAE**\n\n[wan_2.1_vae.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors)\n\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   └─── cosmos_predict2_2B_t2i.pt \n│   ├── text_encoders/\n│   │   └─── oldt5_xxl_fp8_e4m3fn_scaled.safetensors\n│   └── vae/\n│       └──  wan_2.1_vae.safetensors\n```"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [17, 15, 0, 8, 1, "VAE"], [33, 13, 0, 3, 0, "MODEL"], [34, 10, 0, 7, 0, "CLIP"], [35, 10, 0, 6, 0, "CLIP"], [40, 12, 0, 3, 3, "LATENT"], [41, 8, 0, 27, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Step1 - load models here", "bounding": [90, 110, 300.74005126953125, 412.10009765625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step2 - image size", "bounding": [90, 530, 300, 190], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - prompt", "bounding": [410, 110, 450, 480], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7736437382254227, "offset": [507.67525592257886, 157.18896053924988]}, "frontendVersion": "1.21.7"}, "version": 0.4}