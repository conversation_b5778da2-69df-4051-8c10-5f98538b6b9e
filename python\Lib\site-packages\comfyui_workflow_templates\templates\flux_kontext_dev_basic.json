{"id": "7cbcec68-7fa6-47bb-a38a-da689949a001", "revision": 0, "last_node_id": 189, "last_link_id": 294, "nodes": [{"id": 39, "type": "VAELoader", "pos": [-400, 390], "size": [337.76861572265625, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [61, 223]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "DualCLIPLoader", "pos": [-400, 210], "size": [337.76861572265625, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [59]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "DualCLIPLoader", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 135, "type": "ConditioningZeroOut", "pos": [250, 200], "size": [240, 26], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 237}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [238]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 173, "type": "PreviewImage", "pos": [320, 860], "size": [420, 310], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 289}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 136, "type": "SaveImage", "pos": [760, 510], "size": [650, 660], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 240}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39"}, "widgets_values": ["ComfyUI"]}, {"id": 8, "type": "VAEDecode", "pos": [530, 350], "size": [190, 46], "flags": {"collapsed": false}, "order": 23, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 52}, {"name": "vae", "type": "VAE", "link": 61}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [240]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 142, "type": "LoadImageOutput", "pos": [-390, 770], "size": [320, 374], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [249]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImageOutput"}, "widgets_values": ["doll.webp [output]", false, "refresh", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 124, "type": "VAEEncode", "pos": [-20, 400], "size": [240, 50], "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 222}, {"name": "vae", "type": "VAE", "link": 223}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [291, 293]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 42, "type": "FluxKontextImageScale", "pos": [-50, 570], "size": [270, 30], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 251}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [222, 289]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale"}, "widgets_values": []}, {"id": 146, "type": "ImageStitch", "pos": [-390, 570], "size": [270, 150], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 249}, {"name": "image2", "shape": 7, "type": "IMAGE", "link": 250}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [251]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "ImageStitch"}, "widgets_values": ["right", true, 0, "white"]}, {"id": 35, "type": "FluxGuidance", "pos": [250, 90], "size": [240, 58], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 292}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxGuidance"}, "widgets_values": [2.5]}, {"id": 185, "type": "<PERSON>downNote", "pos": [-960, 490], "size": [510, 170], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "About VRAM", "properties": {}, "widgets_values": ["For reference:\n- **fp8_scaled**: Requires about 20GB of VRAM.\n- **Original**: Requires about 32GB of VRAM.\n\n---\n\n供参考：\n-  **fp8_scaled** :  大概需要 20GB 左右 VRAM \n- **原始权重**:  原始权重，大概需要 32GB 左右 VRAM \n"], "color": "#432", "bgcolor": "#653"}, {"id": 186, "type": "<PERSON>downNote", "pos": [-960, 710], "size": [510, 170], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "Flux Kontext Prompt Techniques", "properties": {}, "widgets_values": ["\n## Flux Kontext Prompt Techniques\n\n### 1. Basic Modifications\n- Simple and direct: `\"Change the car color to red\"`\n- Maintain style: `\"Change to daytime while maintaining the same style of the painting\"`\n\n### 2. Style Transfer\n**Principles:**\n- Clearly name style: `\"Transform to Bauhaus art style\"`\n- Describe characteristics: `\"Transform to oil painting with visible brushstrokes, thick paint texture\"`\n- Preserve composition: `\"Change to Bauhaus style while maintaining the original composition\"`\n\n### 3. Character Consistency\n**Framework:**\n- Specific description: `\"The woman with short black hair\"` instead of \"she\"\n- Preserve features: `\"while maintaining the same facial features, hairstyle, and expression\"`\n- Step-by-step modifications: Change background first, then actions\n\n### 4. Text Editing\n- Use quotes: `\"Replace 'joy' with 'BFL'\"`\n- Maintain format: `\"Replace text while maintaining the same font style\"`\n\n## Common Problem Solutions\n\n### Character Changes Too Much\n❌ Wrong: `\"Transform the person into a Viking\"`\n✅ Correct: `\"Change the clothes to be a viking warrior while preserving facial features\"`\n\n### Composition Position Changes\n❌ Wrong: `\"Put him on a beach\"`\n✅ Correct: `\"Change the background to a beach while keeping the person in the exact same position, scale, and pose\"`\n\n### Style Application Inaccuracy\n❌ Wrong: `\"Make it a sketch\"`\n✅ Correct: `\"Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture\"`\n\n## Core Principles\n\n1. **Be Specific and Clear** - Use precise descriptions, avoid vague terms\n2. **Step-by-step Editing** - Break complex modifications into multiple simple steps\n3. **Explicit Preservation** - State what should remain unchanged\n4. **Verb Selection** - Use \"change\", \"replace\" rather than \"transform\"\n\n## Best Practice Templates\n\n**Object Modification:**\n`\"Change [object] to [new state], keep [content to preserve] unchanged\"`\n\n**Style Transfer:**\n`\"Transform to [specific style], while maintaining [composition/character/other] unchanged\"`\n\n**Background Replacement:**\n`\"Change the background to [new background], keep the subject in the exact same position and pose\"`\n\n**Text Editing:**\n`\"Replace '[original text]' with '[new text]', maintain the same font style\"`\n\n> **Remember:** The more specific, the better. Kontext excels at understanding detailed instructions and maintaining consistency. "], "color": "#432", "bgcolor": "#653"}, {"id": 184, "type": "<PERSON>downNote", "pos": [-960, 40], "size": [510, 400], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "Model links", "properties": {}, "widgets_values": ["[tutorial](http://docs.comfy.org/tutorials/flux/flux-1-kontext-dev) | [教程](http://docs.comfy.org/zh-CN/tutorials/flux/flux-1-kontext-dev)\n\n**diffusion model**\n\n- [flux1-dev-kontext_fp8_scaled.safetensors](https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors)\n\n**vae**\n\n- [ae.safetensors](https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/blob/main/split_files/vae/ae.safetensors)\n\n**text encoder**\n\n- [clip_l.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/blob/main/clip_l.safetensors)\n- [t5xxl_fp16.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors) or [t5xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors)\n\nModel Storage Location\n\n```\n📂 ComfyUI/\n├── 📂 models/\n│   ├── 📂 diffusion_models/\n│   │   └── flux1-dev-kontext_fp8_scaled.safetensors\n│   ├── 📂 vae/\n│   │   └── ae.safetensor\n│   └── 📂 text_encoders/\n│       ├── clip_l.safetensors\n│       └── t5xxl_fp16.safetensors 或者 t5xxl_fp8_e4m3fn_scaled.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 37, "type": "UNETLoader", "pos": [-400, 80], "size": [337.76861572265625, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [58]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "UNETLoader", "models": [{"name": "flux1-dev-kontext_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["flux1-dev-kontext_fp8_scaled.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 177, "type": "ReferenceLatent", "pos": [10, 140], "size": [197.712890625, 46], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 294}, {"name": "latent", "shape": 7, "type": "LATENT", "link": 293}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [292]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ReferenceLatent"}, "widgets_values": []}, {"id": 178, "type": "<PERSON>downNote", "pos": [-30, -150], "size": [540, 150], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "About multiple images reference", "properties": {}, "widgets_values": ["[English] In addition to using **Image Stitch** to combine two images at a time, you can also encode individual images, then concatenate multiple latent conditions using the **ReferenceLatent** node, thus achieving the purpose of referencing multiple images. You can use the **EmptySD3LatentImage** node on the right to connect to **KSamper** and customize the size of the **latent_image**.\n\n[中文] 除了使用 **Image Stitch** 将两个两个图像拼合之外，你同样可以将单独的图像 encode 之后，将多个 latent 条件使用 **ReferenceLatent** 节点串联，从而实现多张图像参考的目的。可以使用右边的 **EmptySD3LatentImage** 节点连接到 **KSamper**来自定义 **latent_image** 的尺寸"], "color": "#432", "bgcolor": "#653"}, {"id": 188, "type": "EmptySD3LatentImage", "pos": [530, -140], "size": [310, 106], "flags": {}, "order": 8, "mode": 4, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [330, 560], "size": [400, 220], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 59}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [237, 294]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Using this elegant style, create a portrait of a swan wearing a pearl tiara and lace collar, maintaining the same refined quality and soft color tones."], "color": "#232", "bgcolor": "#353"}, {"id": 31, "type": "K<PERSON><PERSON><PERSON>", "pos": [530, 40], "size": [320, 262], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 58}, {"name": "positive", "type": "CONDITIONING", "link": 57}, {"name": "negative", "type": "CONDITIONING", "link": 238}, {"name": "latent_image", "type": "LATENT", "link": 291}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [784381637916598, "randomize", 20, 1, "euler", "simple", 1]}, {"id": 180, "type": "<PERSON>downNote", "pos": [-1430, 40], "size": [450, 450], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "title": "✨ New ComfyUI feature for Flux.1 Kontext Dev", "properties": {}, "widgets_values": ["[English]\nWe have added an **Edit** button to the **Selection Toolbox** of the node for **FLUX.1 Kontext Image Edit** support. When clicked, it quickly adds a **FLUX.1 Kontext Image Edit** group node to the Latent output of your current workflow. This enables an interactive editing experience where you can:\n\n- Create multiple editing iterations, each preserved as a separate node\n- Easily branch off from any previous edit point to explore different creative directions\n- Return to any earlier version and start a new editing branch\n- Modify parameters in earlier nodes and automatically update all downstream edits\n- Execute or re-execute any branch of edits at any time\n- When you want to maintain the effect of the corresponding branch, please set the seed of the corresponding group node to fixed.\n\n\nThis workflow mirrors the iterative nature of LLM conversations, but with the added advantage of visual editing and the ability to maintain multiple parallel editing paths.\n\n---\n\n[中文]\n我们为 **FLUX.1 Kontext Image Edit** 的相关支持在节点的**选择工具箱**上新增了一个**编辑**按钮。点击后，系统会在当前工作流的 Latent 输出上快速添加一个 **FLUX.1 Kontext Image Edit** 的组节点。这种设计带来了灵活的交互式编辑体验：\n\n- 创建多个编辑迭代，每次编辑都会保存为独立节点\n- 可以从任何之前的编辑点分支出新的创作方向\n- 随时返回到早期版本并开始新的编辑分支\n- 修改早期节点的参数，自动更新所有下游编辑\n- 可以随时执行或重新执行任何编辑分支\n- 想要固定对应分支效果时，请将对应的 seed 设置为 fixed\n\n这种工作流程类似于 LLM 对话的迭代特性，但增加了视觉编辑的优势，并能够维护多个并行的编辑路径。"], "color": "#322", "bgcolor": "#533"}, {"id": 187, "type": "<PERSON>downNote", "pos": [-960, 930], "size": [510, 180], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "title": "Flux Kontext 提示词技巧", "properties": {}, "widgets_values": ["\n## Flux Kontext 提示词技巧\n\n使用英文\n\n### 1. 基础修改\n- 简单直接：`\"Change the car color to red\"`\n- 保持风格：`\"Change to daytime while maintaining the same style of the painting\"`\n\n### 2. 风格转换\n**原则：**\n- 明确命名风格：`\"Transform to Bauhaus art style\"`\n- 描述特征：`\"Transform to oil painting with visible brushstrokes, thick paint texture\"`\n- 保留构图：`\"Change to Bauhaus style while maintaining the original composition\"`\n\n### 3. 角色一致性\n**框架：**\n- 具体描述：`\"The woman with short black hair\"`而非`\"她\"`\n- 保留特征：`\"while maintaining the same facial features, hairstyle, and expression\"`\n- 分步修改：先改背景，再改动作\n\n### 4. 文本编辑\n- 使用引号：`\"Replace 'joy' with 'BFL'\"`\n- 保持格式：`\"Replace text while maintaining the same font style\"`\n\n## 常见问题解决\n\n### 角色变化过大\n❌ 错误：`\"Transform the person into a Viking\"`\n✅ 正确：`\"Change the clothes to be a viking warrior while preserving facial features\"`\n\n### 构图位置改变\n❌ 错误：`\"Put him on a beach\"`\n✅ 正确：`\"Change the background to a beach while keeping the person in the exact same position, scale, and pose\"`\n\n### 风格应用不准确\n❌ 错误：`\"Make it a sketch\"`\n✅ 正确：`\"Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture\"`\n\n## 核心原则\n\n1. **具体明确** - 使用精确描述，避免模糊词汇\n2. **分步编辑** - 复杂修改分为多个简单步骤\n3. **明确保留** - 说明哪些要保持不变\n4. **动词选择** - 用\"更改\"、\"替换\"而非\"转换\"\n\n## 最佳实践模板\n\n**对象修改：**\n`\"Change [object] to [new state], keep [content to preserve] unchanged\"`\n\n**风格转换：**\n`\"Transform to [specific style], while maintaining [composition/character/other] unchanged\"`\n\n**背景替换：**\n`\"Change the background to [new background], keep the subject in the exact same position and pose\"`\n\n**文本编辑：**\n`\"Replace '[original text]' with '[new text]', maintain the same font style\"`\n\n> **记住：** 越具体越好，Kontext 擅长理解详细指令并保持一致性。"], "color": "#432", "bgcolor": "#653"}, {"id": 175, "type": "<PERSON>downNote", "pos": [-50, 640], "size": [320, 88], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "title": "How to enable multiple image input", "properties": {}, "widgets_values": ["Click on the **Load image (from output)** node and use **Ctrl + B** to enable multiple image input support."], "color": "#432", "bgcolor": "#653"}, {"id": 189, "type": "<PERSON>downNote", "pos": [-400, 1200], "size": [340, 90], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "title": "About multi-round edit", "properties": {}, "widgets_values": ["The **Load Image (from Outputs)** node allows you to fetch the last image from the output. Click on the **refresh** button to fetch the latest image or manually select an image from the dropdown list."], "color": "#432", "bgcolor": "#653"}, {"id": 147, "type": "LoadImageOutput", "pos": [-50, 770], "size": [320, 374], "flags": {}, "order": 13, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [250]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImageOutput"}, "widgets_values": ["doll.webp [output]", false, "refresh", "image"], "color": "#322", "bgcolor": "#533"}], "links": [[52, 31, 0, 8, 0, "LATENT"], [57, 35, 0, 31, 1, "CONDITIONING"], [58, 37, 0, 31, 0, "MODEL"], [59, 38, 0, 6, 0, "CLIP"], [61, 39, 0, 8, 1, "VAE"], [222, 42, 0, 124, 0, "IMAGE"], [223, 39, 0, 124, 1, "VAE"], [237, 6, 0, 135, 0, "CONDITIONING"], [238, 135, 0, 31, 2, "CONDITIONING"], [240, 8, 0, 136, 0, "IMAGE"], [249, 142, 0, 146, 0, "IMAGE"], [250, 147, 0, 146, 1, "IMAGE"], [251, 146, 0, 42, 0, "IMAGE"], [289, 42, 0, 173, 0, "IMAGE"], [291, 124, 0, 31, 3, "LATENT"], [292, 177, 0, 35, 0, "CONDITIONING"], [293, 124, 0, 177, 1, "LATENT"], [294, 6, 0, 177, 0, "CONDITIONING"]], "groups": [{"id": 1, "title": "Step 1- Load models", "bounding": [-410, 10, 360, 450], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step 2 - Upload images", "bounding": [-410, 480, 700, 680], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Step 3 - Prompt", "bounding": [310, 480, 430, 330], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Conditioning", "bounding": [-30, 10, 540, 250], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5559917313492586, "offset": [1740.2336172409775, 16.235369583159628]}, "frontendVersion": "1.23.4", "groupNodes": {}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}