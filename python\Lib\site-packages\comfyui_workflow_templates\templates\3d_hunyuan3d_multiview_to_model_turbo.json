{"id": "b22dbb77-aac1-4849-b05a-1d3c7f8b9d95", "revision": 0, "last_node_id": 91, "last_link_id": 178, "nodes": [{"id": 51, "type": "CLIPVisionEncode", "pos": [-1180, 250], "size": [253.60000610351562, 78], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 111}, {"label": "image", "name": "image", "type": "IMAGE", "link": 145}], "outputs": [{"label": "CLIP_VISION_OUTPUT", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [144]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 82, "type": "LoadImage", "pos": [-880, 370], "size": [401.6362609863281, 345.33544921875], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [164]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "LoadImage"}, "widgets_values": ["<EMAIL>", "image"]}, {"id": 81, "type": "CLIPVisionEncode", "pos": [-740, 250], "size": [253.60000610351562, 78], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 167}, {"label": "image", "name": "image", "type": "IMAGE", "link": 164}], "outputs": [{"label": "CLIP_VISION_OUTPUT", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [170]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 87, "type": "LoadImage", "pos": [-880, 950], "size": [401.6362609863281, 345.33544921875], "flags": {}, "order": 1, "mode": 4, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [176]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "LoadImage"}, "widgets_values": ["back.jpg", "image"]}, {"id": 85, "type": "LoadImage", "pos": [-1320, 950], "size": [401.6362609863281, 345.33544921875], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [172]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "LoadImage"}, "widgets_values": ["back.jpg", "image"]}, {"id": 86, "type": "CLIPVisionEncode", "pos": [-1180, 820], "size": [253.60000610351562, 78], "flags": {}, "order": 11, "mode": 4, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 173}, {"label": "image", "name": "image", "type": "IMAGE", "link": 172}], "outputs": [{"label": "CLIP_VISION_OUTPUT", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [177]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 88, "type": "CLIPVisionEncode", "pos": [-740, 820], "size": [253.60000610351562, 78], "flags": {}, "order": 12, "mode": 4, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 175}, {"label": "image", "name": "image", "type": "IMAGE", "link": 176}], "outputs": [{"label": "CLIP_VISION_OUTPUT", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [178]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 78, "type": "FluxGuidance", "pos": [-430, 333], "size": [270, 60], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 162}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [163]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 66, "type": "EmptyLatentHunyuan3Dv2", "pos": [-430, 440], "size": [270, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [143]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "EmptyLatentHunyuan3Dv2"}, "widgets_values": [3072, 1]}, {"id": 65, "type": "Hunyuan3Dv2ConditioningMultiView", "pos": [-430, 200], "size": [270, 86], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "front", "name": "front", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 144}, {"label": "left", "name": "left", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 177}, {"label": "back", "name": "back", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 170}, {"label": "right", "name": "right", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 178}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [162]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [151]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "Hunyuan3Dv2ConditioningMultiView"}, "widgets_values": []}, {"id": 61, "type": "VAEDecodeHunyuan3D", "pos": [190, 200], "size": [315, 102], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 130}, {"label": "vae", "name": "vae", "type": "VAE", "link": 158}], "outputs": [{"label": "VOXEL", "name": "VOXEL", "type": "VOXEL", "slot_index": 0, "links": [168]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "VAEDecodeHunyuan3D"}, "widgets_values": [8000, 256]}, {"id": 83, "type": "VoxelToMesh", "pos": [200, 350], "size": [270, 82], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "voxel", "type": "VOXEL", "link": 168}], "outputs": [{"name": "MESH", "type": "MESH", "links": [169]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "VoxelToMesh"}, "widgets_values": ["surface net", 0.6]}, {"id": 67, "type": "SaveGLB", "pos": [-430, 570], "size": [920, 720], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "mesh", "name": "mesh", "type": "MESH", "link": 169}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "SaveGLB", "Camera Info": {"position": {"x": 10.00565840268339, "y": 9.988673578489918, "z": 10.005658402683391}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["mesh/ComfyUI", ""]}, {"id": 56, "type": "LoadImage", "pos": [-1320, 370], "size": [401.6362609863281, 345.33544921875], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [145]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "LoadImage"}, "widgets_values": ["<EMAIL>", "image"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [-140, 200], "size": [315, 262], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 156}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 163}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 151}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 143}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [130]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [528364197559477, "randomize", 20, 4, "euler", "normal", 1]}, {"id": 91, "type": "<PERSON>downNote", "pos": [-140, 60], "size": [310, 88], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "CFG setting", "properties": {}, "widgets_values": ["Try adjusting the cfg settings to get the best results. It should be between 4 and 8. Thanks for the feedback from <PERSON><PERSON><PERSON>. "], "color": "#432", "bgcolor": "#653"}, {"id": 54, "type": "ImageOnlyCheckpointLoader", "pos": [-1330, 10], "size": [369.6000061035156, 98], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [155]}, {"label": "CLIP_VISION", "name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 1, "links": [111, 167, 173, 175]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [158]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "ImageOnlyCheckpointLoader", "models": [{"name": "hunyuan3d-dit-v2-mv-turbo_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/hunyuan3D_2.0_repackaged/resolve/main/split_files/hunyuan3d-dit-v2-mv-turbo_fp16.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["hunyuan3d-dit-v2-mv-turbo_fp16.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 70, "type": "ModelSamplingAuraFlow", "pos": [-870, 10], "size": [310, 60], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 155}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [156]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "ModelSamplingAuraFlow"}, "widgets_values": [1.0000000000000002]}, {"id": 77, "type": "<PERSON>downNote", "pos": [-1720, -30], "size": [360, 160], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorials](https://docs.comfy.org/tutorials/3d/hunyuan3D-2) | [教程](https://docs.comfy.org/zh-CN/tutorials/3d/hunyuan3D-2)\n\n## Model\n\nDownload [hunyuan3d-dit-v2_fp16.safetensors](https://huggingface.co/Comfy-Org/hunyuan3D_2.0_repackaged/resolve/main/split_files/hunyuan3d-dit-v2_fp16.safetensors) \n\nPut it under the **ComfyUI/models/checkpoints** folder"], "color": "#432", "bgcolor": "#653"}], "links": [[111, 54, 1, 51, 0, "CLIP_VISION"], [130, 3, 0, 61, 0, "LATENT"], [143, 66, 0, 3, 3, "LATENT"], [144, 51, 0, 65, 0, "CLIP_VISION_OUTPUT"], [145, 56, 0, 51, 1, "IMAGE"], [151, 65, 1, 3, 2, "CONDITIONING"], [155, 54, 0, 70, 0, "MODEL"], [156, 70, 0, 3, 0, "MODEL"], [158, 54, 2, 61, 1, "VAE"], [162, 65, 0, 78, 0, "CONDITIONING"], [163, 78, 0, 3, 1, "CONDITIONING"], [164, 82, 0, 81, 1, "IMAGE"], [167, 54, 1, 81, 0, "CLIP_VISION"], [168, 61, 0, 83, 0, "VOXEL"], [169, 83, 0, 67, 0, "MESH"], [170, 81, 0, 65, 2, "CLIP_VISION_OUTPUT"], [172, 85, 0, 86, 1, "IMAGE"], [173, 54, 1, 86, 0, "CLIP_VISION"], [175, 54, 1, 88, 0, "CLIP_VISION"], [176, 87, 0, 88, 1, "IMAGE"], [177, 86, 0, 65, 1, "CLIP_VISION_OUTPUT"], [178, 88, 0, 65, 3, "CLIP_VISION_OUTPUT"]], "groups": [{"id": 1, "title": "Front", "bounding": [-1330, 180, 421.63623046875, 548.9354248046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Back", "bounding": [-890, 180, 421.63623046875, 548.9354248046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Left", "bounding": [-1330, 750, 421.63623046875, 558.9354248046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Right", "bounding": [-890, 750, 421.6362609863281, 558.9354248046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Step1 - Load model", "bounding": [-1340, -60, 389.5999755859375, 181.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Step2 - Upload multiview images", "bounding": [-1340, 140, 881.63623046875, 1182.535400390625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6951137299943303, "offset": [1760.340347682937, 271.4947908534884]}, "frontendVersion": "1.23.4", "node_versions": {"comfy-core": "0.3.35"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}