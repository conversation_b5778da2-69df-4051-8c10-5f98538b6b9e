このノードは、`ComfyUI/models/controlnet` フォルダーにあるモデルを検出し、また、extra_model_paths.yaml ファイルで設定された追加パスのモデルも読み込みます。場合によっては、**ComfyUIのインターフェースを更新する**必要があるかもしれませんので、対応するフォルダー内のモデルファイルを読み込むことができます。

ControlNetLoaderノードは、指定されたパスからControlNetモデルをロードするために設計されています。これは、生成されたコンテンツに対する制御メカニズムを適用したり、制御信号に基づいて既存のコンテンツを修正したりするために不可欠な役割を果たします。

## Input types（入力タイプ）

| Field（フィールド）             | Comfy dtype       | Description（説明）                                                                       |
|-------------------|-------------------|-----------------------------------------------------------------------------------|
| `control_net_name`| `COMBO[STRING]`    | ロードするControlNetモデルの名前を指定し、事前定義されたディレクトリ構造内でモデルファイルを特定するために使用されます。 |

## Output types（出力タイプ）

| Field（フィールド）          | Comfy dtype   | Description（説明）                                                              |
|----------------|---------------|--------------------------------------------------------------------------|
| `control_net`  | `CONTROL_NET` | ロードされたControlNetモデルを返し、コンテンツ生成プロセスの制御や修正に使用する準備が整っています。 |
