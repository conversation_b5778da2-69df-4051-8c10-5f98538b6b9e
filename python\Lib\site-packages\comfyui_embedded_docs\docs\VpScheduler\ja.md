
VPSchedulerノードは、Variance Preserving (VP) スケジューリング手法に基づいてノイズレベル（シグマ）のシーケンスを生成するように設計されています。このシーケンスは、拡散モデルにおけるノイズ除去プロセスを導くために重要であり、画像や他のデータタイプの制御された生成を可能にします。

## 入力

| パラメータ   | Data Type | 説明                                                                                                                                      |
|-------------|-------------|--------------------------------------------------------------------------------------------------------------------------------------------------|
| `steps`     | INT         | 拡散プロセスにおけるステップ数を指定し、生成されるノイズレベルの粒度に影響を与えます。                              |
| `beta_d`    | FLOAT       | 全体的なノイズレベルの分布を決定し、生成されるノイズレベルの分散に影響を与えます。                                 |
| `beta_min`  | FLOAT       | ノイズレベルの最小境界を設定し、ノイズが特定の閾値を下回らないようにします。                              |
| `eps_s`     | FLOAT       | 開始時のイプシロン値を調整し、拡散プロセスにおける初期ノイズレベルを微調整します。                                    |

## 出力

| パラメータ   | Data Type | 説明                                                                                   |
|-------------|-------------|-----------------------------------------------------------------------------------------------|
| `sigmas`    | SIGMAS      | VPスケジューリング手法に基づいて生成されたノイズレベル（シグマ）のシーケンスで、拡散モデルにおけるノイズ除去プロセスを導くために使用されます。 |
