## ComfyUI-Manager: installing dependencies done.
[2025-07-28 20:04:49.321] ** ComfyUI startup time: 2025-07-28 20:04:49.321
[2025-07-28 20:04:49.321] ** Platform: Windows
[2025-07-28 20:04:49.322] ** Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-28 20:04:49.322] ** Python executable: D:\AI\koutu\python\python.exe
[2025-07-28 20:04:49.322] ** ComfyUI Path: D:\AI\koutu\ComfyUI
[2025-07-28 20:04:49.322] ** ComfyUI Base Folder Path: D:\AI\koutu\ComfyUI
[2025-07-28 20:04:49.322] ** User directory: D:\AI\koutu\ComfyUI\user
[2025-07-28 20:04:49.322] ** ComfyUI-Manager config path: D:\AI\koutu\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-28 20:04:49.322] ** Log path: D:\AI\koutu\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-28 20:04:52.993]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
[2025-07-28 20:04:52.993]    7.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager
[2025-07-28 20:04:52.993] 
[2025-07-28 20:05:06.373] Checkpoint files will always be loaded safely.
[2025-07-28 20:05:06.534] Total VRAM 24564 MB, total RAM 65149 MB
[2025-07-28 20:05:06.534] pytorch version: 2.5.1+cu124
[2025-07-28 20:05:09.239] xformers version: 0.0.28.post3
[2025-07-28 20:05:09.239] Set vram state to: NORMAL_VRAM
[2025-07-28 20:05:09.239] Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
[2025-07-28 20:05:09.851] Using xformers attention
[2025-07-28 20:05:11.279] Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-28 20:05:11.279] ComfyUI version: 0.3.45
[2025-07-28 20:05:11.340] ComfyUI frontend version: 1.23.4
[2025-07-28 20:05:11.341] [Prompt Server] web root: D:\AI\koutu\python\Lib\site-packages\comfyui_frontend_package\static
[2025-07-28 20:05:12.238] [AnimateDiffEvo] - [0;31mERROR[0m - No motion models found. Please download one and place in: ['D:\\AI\\koutu\\ComfyUI\\custom_nodes\\comfyui-animatediff-evolved\\models', 'D:\\AI\\koutu\\ComfyUI\\models\\animatediff_models']
[2025-07-28 20:05:12.549] [Crystools [0;32mINFO[0m] Crystools version: 1.22.1
[2025-07-28 20:05:12.565] [Crystools [0;32mINFO[0m] CPU: Intel(R) Core(TM) Ultra 9 285K - Arch: AMD64 - OS: Windows 10
[2025-07-28 20:05:12.585] [Crystools [0;32mINFO[0m] Pynvml (Nvidia) initialized.
[2025-07-28 20:05:12.585] [Crystools [0;32mINFO[0m] GPU/s:
[2025-07-28 20:05:12.600] [Crystools [0;32mINFO[0m] 0) NVIDIA GeForce RTX 4090 D
[2025-07-28 20:05:12.600] [Crystools [0;32mINFO[0m] NVIDIA Driver: 576.02
[2025-07-28 20:05:13.323] ### Loading: ComfyUI-Impact-Pack (V8.8.5)
[2025-07-28 20:05:13.721] [Impact Pack] Wildcards loading done.
[2025-07-28 20:05:13.726] ### Loading: ComfyUI-Inspire-Pack (V1.14.1)
[2025-07-28 20:05:13.773] Total VRAM 24564 MB, total RAM 65149 MB
[2025-07-28 20:05:13.773] pytorch version: 2.5.1+cu124
[2025-07-28 20:05:13.773] xformers version: 0.0.28.post3
[2025-07-28 20:05:13.773] Set vram state to: NORMAL_VRAM
[2025-07-28 20:05:13.773] Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
[2025-07-28 20:05:13.913] ### Loading: ComfyUI-Manager (V3.31.1)
[2025-07-28 20:05:13.914] [ComfyUI-Manager] network_mode: public
[2025-07-28 20:05:14.368] ### ComfyUI Version: v0.3.45-25-ge6d9f627 | Released on '2025-07-27'
[2025-07-28 20:05:14.921] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-28 20:05:14.949] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-28 20:05:14.977] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-28 20:05:15.009] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-28 20:05:15.034] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-28 20:05:15.076] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: TensorrtExecutionProvider, CUDAExecutionProvider, CPUExecutionProvider
[2025-07-28 20:05:15.076] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-07-28 20:05:15.080] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-28 20:05:15.080] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-28 20:05:15.080] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-28 20:05:15.532] DWPose: Onnxruntime with acceleration providers detected
[2025-07-28 20:05:15.611] 
[36mEfficiency Nodes:[0m Attempting to add Control Net options to the 'HiRes-Fix Script' Node (comfyui_controlnet_aux add-on)...[92mSuccess![0m
[2025-07-28 20:05:18.035] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-28 20:05:18.035] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-28 20:05:19.302] FETCH ComfyRegistry Data: 5/92
[2025-07-28 20:05:19.811] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-28 20:05:19.811] 
	[3m[93m"Art is not about making something perfect, it's about making something meaningful."[0m[3m - Unknown[0m
[2025-07-28 20:05:19.811] 
[2025-07-28 20:05:19.835] 
[2025-07-28 20:05:19.835] [92m[rgthree-comfy] Loaded 42 extraordinary nodes. 🎉[00m
[2025-07-28 20:05:19.835] 
[2025-07-28 20:05:19.840] 
Import times for custom nodes:
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\sd-dynamic-thresholding
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-WD14-Tagger
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\AIGODLIKE-COMFYUI-TRANSLATION
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-layerdiffuse
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_essentials
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_UltimateSDUpscale
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-advanced-controlnet
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-animatediff-evolved
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Inspire-Pack
[2025-07-28 20:05:19.840]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-07-28 20:05:19.840]    0.1 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-KJNodes
[2025-07-28 20:05:19.840]    0.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-crystools
[2025-07-28 20:05:19.840]    0.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Impact-Pack
[2025-07-28 20:05:19.840]    0.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-VideoHelperSuite
[2025-07-28 20:05:19.840]    0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-28 20:05:19.840]    0.7 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper
[2025-07-28 20:05:19.840]    0.7 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager
[2025-07-28 20:05:19.840]    4.2 seconds: D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894
[2025-07-28 20:05:19.840] 
[2025-07-28 20:05:20.072] Context impl SQLiteImpl.
[2025-07-28 20:05:20.072] Will assume non-transactional DDL.
[2025-07-28 20:05:20.073] No target revision found.
[2025-07-28 20:05:20.096] Starting server

[2025-07-28 20:05:20.096] To see the GUI go to: http://127.0.0.1:8188
[2025-07-28 20:05:23.615] FETCH ComfyRegistry Data: 10/92
[2025-07-28 20:05:26.541] got prompt
[2025-07-28 20:05:26.907] [RMBG ERROR] Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
[2025-07-28 20:05:26.907] [RMBG ERROR] Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
[2025-07-28 20:05:27.083] !!! Exception during processing !!! Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
[2025-07-28 20:05:27.086] Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 195, in process_image
    self.load_model(model_name)
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 183, in load_model
    raise e
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 164, in load_model
    self.model = AutoModelForImageSegmentation.from_pretrained(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\auto_factory.py", line 547, in from_pretrained
    config, kwargs = AutoConfig.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\configuration_auto.py", line 1262, in from_pretrained
    return config_class.from_pretrained(pretrained_model_name_or_path, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 627, in from_pretrained
    return cls.from_dict(config_dict, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 789, in from_dict
    config = cls(**config_dict)
             ^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\.cache\huggingface\modules\transformers_modules\RMBG-2.0\BiRefNet_config.py", line 13, in __init__
    super().__init__(**kwargs)
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 284, in __init__
    self.tie_word_embeddings = tie_word_embeddings
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 206, in __setattr__
    super().__setattr__(key, value)
AttributeError: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 534, in process_image
    mask = model_instance.process_image(img, model, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 238, in process_image
    handle_model_error(f"Error in batch processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 89, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "D:\AI\koutu\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 602, in process_image
    handle_model_error(f"Error in image processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 89, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

[2025-07-28 20:05:27.087] Prompt executed in 0.54 seconds
[2025-07-28 20:05:27.536] FETCH ComfyRegistry Data: 15/92
[2025-07-28 20:05:31.914] FETCH ComfyRegistry Data: 20/92
[2025-07-28 20:05:35.977] FETCH ComfyRegistry Data: 25/92
[2025-07-28 20:05:40.166] FETCH ComfyRegistry Data: 30/92
[2025-07-28 20:05:44.577] FETCH ComfyRegistry Data: 35/92
[2025-07-28 20:05:48.361] FETCH ComfyRegistry Data: 40/92
[2025-07-28 20:05:52.195] FETCH ComfyRegistry Data: 45/92
[2025-07-28 20:05:56.345] FETCH ComfyRegistry Data: 50/92
[2025-07-28 20:06:00.583] FETCH ComfyRegistry Data: 55/92
[2025-07-28 20:06:05.293] FETCH ComfyRegistry Data: 60/92
[2025-07-28 20:06:09.420] FETCH ComfyRegistry Data: 65/92
[2025-07-28 20:06:13.585] FETCH ComfyRegistry Data: 70/92
[2025-07-28 20:06:18.055] FETCH ComfyRegistry Data: 75/92
[2025-07-28 20:06:22.810] FETCH ComfyRegistry Data: 80/92
[2025-07-28 20:06:26.889] FETCH ComfyRegistry Data: 85/92
[2025-07-28 20:06:30.813] FETCH ComfyRegistry Data: 90/92
[2025-07-28 20:06:32.835] FETCH ComfyRegistry Data [DONE]
[2025-07-28 20:06:32.922] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-28 20:06:32.949] nightly_channel: 
[2025-07-28 20:06:32.949] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-07-28 20:06:32.949] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-28 20:06:33.201] [ComfyUI-Manager] All startup tasks have been completed.
