{"id": "ba9df292-1ee9-4b7c-af08-690441990d87", "revision": 0, "last_node_id": 30, "last_link_id": 32, "nodes": [{"id": 28, "type": "<PERSON>downNote", "pos": [206.04347229003906, 603.7212524414062], "size": [394.96832275390625, 297.83856201171875], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["## Moonvalley Template Docs\n\n[Moonvalley video generation](http://docs.comfy.org/tutorials/api-nodes/moonvalley/moonvalley-video-generation)\n\n## API Node Docs\n[API Node](https://docs.comfy.org/tutorials/api-nodes/overview)\n## FAQ about login issues\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Ensure normal connectivity to our API services (VPN may be needed in some regions).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key)"], "color": "#322", "bgcolor": "#533"}, {"id": 27, "type": "SaveVideo", "pos": [1030, 610], "size": [710, 808], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 32}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 30, "type": "MoonvalleyTxt2VideoNode", "pos": [620, 610], "size": [390, 304], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "video", "type": "VIDEO", "links": [32]}], "properties": {"Node name for S&R": "MoonvalleyTxt2VideoNode"}, "widgets_values": ["A light, glowing, orange jellyish is floating in blue water.", "gopro, bright, contrast, static, overexposed, bright, vignette, artifacts, still, noise, texture, scanlines, videogame, 360 camera, VR, transition, flare, saturation, distorted, warped, wide angle, contrast, saturated, vibrant, glowing, cross dissolve, texture, videogame, saturation, cheesy, ugly hands, mutated hands, mutant, disfigured, extra fingers, blown out, horrible, blurry, worst quality, bad, transition, dissolve, cross-dissolve, melt, fade in, fade out, wobbly, weird, low quality, plastic, stock footage, video camera, boring, static", "16:9 (1920 x 1080)", 7, 2023097664, "randomize", 100], "color": "#432", "bgcolor": "#653"}], "links": [[32, 30, 0, 27, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8349250140851217, "offset": [-284.238917258848, -248.05691299440753]}, "frontendVersion": "1.24.0-1"}, "version": 0.4}