{"last_node_id": 13, "last_link_id": 18, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 186], "size": [315, 262], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 12}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 4}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 6}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [736724629443131, "randomize", 30, 7, "dpmpp_2m", "karras", 1]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-784, 280], "size": [315, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [17]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [18]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [8]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "dreamshaper_8.safetensors", "url": "https://civitai.com/api/download/models/128713?type=Model&format=SafeTensor&size=pruned&fp=fp16", "directory": "checkpoints"}]}, "widgets_values": ["dreamshaper_8.safetensors"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [1024, 1024, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [410.99298095703125, 177.98597717285156], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 13}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["masterpiece, best quality, ultra-detailed, 8K, RAW photo, intricate details, stunning visuals,upper-body, highly detailed face, smooth skin, white colthes, realistic lighting, beautiful Chinese girl, solo, traditional Chinese dress, golden embroidery, elegant, black hair, delicate hair ornament, cinematic lighting, soft focus,(white background:1.05)"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 14}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["(low quality, worst quality:1.4), (blurry:1.2), (bad anatomy:1.3), extra limbs, deformed, watermark, text, signature, bareness"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 7}, {"label": "vae", "name": "vae", "type": "VAE", "link": 8}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1451, 189], "size": [210, 270], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["2loras_test_"]}, {"id": 10, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-27, 160], "size": [315, 126], "flags": {}, "order": 4, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 15}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 16}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [12]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [13, 14]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "MoXinV1.safetensors", "url": "https://civitai.com/api/download/models/14856?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "loras"}]}, "widgets_values": ["MoXinV1.safetensors", 0.9, 1]}, {"id": 11, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-379, 160], "size": [315, 126], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 17}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 18}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [15]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [16]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "blindbox_v1_mix.safetensors", "url": "https://civitai.com/api/download/models/32988?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "loras"}]}, "widgets_values": ["blindbox_v1_mix.safetensors", 0.9, 1]}, {"id": 13, "type": "<PERSON>downNote", "pos": [-784, 432], "size": [312, 136], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [LoRA - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/lora/) — Overview\n> \n> [Multiple LoRAs - docs.comfy.org](https://docs.comfy.org/tutorials/basic/multiple-loras) — Detailed guide to using multiple LoRAs"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [9, 8, 0, 9, 0, "IMAGE"], [12, 10, 0, 3, 0, "MODEL"], [13, 10, 1, 6, 0, "CLIP"], [14, 10, 1, 7, 0, "CLIP"], [15, 11, 0, 10, 0, "MODEL"], [16, 11, 1, 10, 1, "CLIP"], [17, 4, 0, 11, 0, "MODEL"], [18, 4, 1, 11, 1, "CLIP"]], "groups": [], "config": {}, "extra": {"node_versions": {"comfy-core": "v0.3.9"}}, "version": 0.4}