
이 노드는 지정된 VAE 모델을 사용하여 이미지를 잠재 공간 표현으로 인코딩하도록 설계되었습니다. 인코딩 과정의 복잡성을 추상화하여 이미지를 잠재 표현으로 변환하는 간단한 방법을 제공합니다.

## 입력

| 매개변수 | 데이터 유형 | 설명                                                                                                                                                                              |
| -------- | ----------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `pixels` | `IMAGE`     | 'pixels' 매개변수는 잠재 공간으로 인코딩될 이미지 데이터를 나타냅니다. 이는 인코딩 과정에서 출력 잠재 표현을 결정하는 데 중요한 역할을 합니다.                                    |
| `vae`    | VAE         | 'vae' 매개변수는 이미지 데이터를 잠재 공간으로 인코딩하는 데 사용될 변분 오토인코더 모델을 지정합니다. 이는 생성된 잠재 표현의 인코딩 메커니즘과 특성을 정의하는 데 필수적입니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                    |
| -------- | ----------- | --------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 출력은 입력 이미지의 잠재 공간 표현으로, 그 본질적인 특징을 압축된 형태로 캡슐화합니다. |
