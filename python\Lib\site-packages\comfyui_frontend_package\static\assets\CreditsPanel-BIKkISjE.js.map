{"version": 3, "file": "CreditsPanel-BIKkISjE.js", "sources": ["../../src/components/dialog/content/setting/CreditsPanel.vue"], "sourcesContent": ["<template>\n  <TabPanel value=\"Credits\" class=\"credits-container h-full\">\n    <div class=\"flex flex-col h-full\">\n      <h2 class=\"text-2xl font-bold mb-2\">\n        {{ $t('credits.credits') }}\n      </h2>\n\n      <Divider />\n\n      <div class=\"flex flex-col gap-2\">\n        <h3 class=\"text-sm font-medium text-muted\">\n          {{ $t('credits.yourCreditBalance') }}\n        </h3>\n        <div class=\"flex justify-between items-center\">\n          <UserCredit text-class=\"text-3xl font-bold\" />\n          <Skeleton v-if=\"loading\" width=\"2rem\" height=\"2rem\" />\n          <Button\n            v-else\n            :label=\"$t('credits.purchaseCredits')\"\n            :loading=\"loading\"\n            @click=\"handlePurchaseCreditsClick\"\n          />\n        </div>\n        <div class=\"flex flex-row items-center\">\n          <Skeleton\n            v-if=\"balanceLoading\"\n            width=\"12rem\"\n            height=\"1rem\"\n            class=\"text-xs\"\n          />\n          <div v-else-if=\"formattedLastUpdateTime\" class=\"text-xs text-muted\">\n            {{ $t('credits.lastUpdated') }}: {{ formattedLastUpdateTime }}\n          </div>\n          <Button\n            icon=\"pi pi-refresh\"\n            text\n            size=\"small\"\n            severity=\"secondary\"\n            @click=\"() => authActions.fetchBalance()\"\n          />\n        </div>\n      </div>\n\n      <div class=\"flex justify-between items-center mt-8\">\n        <Button\n          :label=\"$t('credits.invoiceHistory')\"\n          text\n          severity=\"secondary\"\n          icon=\"pi pi-arrow-up-right\"\n          :loading=\"loading\"\n          @click=\"handleCreditsHistoryClick\"\n        />\n      </div>\n\n      <template v-if=\"creditHistory.length > 0\">\n        <div class=\"flex-grow\">\n          <DataTable :value=\"creditHistory\" :show-headers=\"false\">\n            <Column field=\"title\" :header=\"$t('g.name')\">\n              <template #body=\"{ data }\">\n                <div class=\"text-sm font-medium\">{{ data.title }}</div>\n                <div class=\"text-xs text-muted\">{{ data.timestamp }}</div>\n              </template>\n            </Column>\n            <Column field=\"amount\" :header=\"$t('g.amount')\">\n              <template #body=\"{ data }\">\n                <div\n                  :class=\"[\n                    'text-base font-medium text-center',\n                    data.isPositive ? 'text-sky-500' : 'text-red-400'\n                  ]\"\n                >\n                  {{ data.isPositive ? '+' : '-' }}${{\n                    formatMetronomeCurrency(data.amount, 'usd')\n                  }}\n                </div>\n              </template>\n            </Column>\n          </DataTable>\n        </div>\n      </template>\n\n      <Divider />\n\n      <div class=\"flex flex-row gap-2\">\n        <Button\n          :label=\"$t('credits.faqs')\"\n          text\n          severity=\"secondary\"\n          icon=\"pi pi-question-circle\"\n          @click=\"handleFaqClick\"\n        />\n        <Button\n          :label=\"$t('credits.messageSupport')\"\n          text\n          severity=\"secondary\"\n          icon=\"pi pi-comments\"\n          @click=\"handleMessageSupport\"\n        />\n      </div>\n    </div>\n  </TabPanel>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Column from 'primevue/column'\nimport DataTable from 'primevue/datatable'\nimport Divider from 'primevue/divider'\nimport Skeleton from 'primevue/skeleton'\nimport TabPanel from 'primevue/tabpanel'\nimport { computed, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport UserCredit from '@/components/common/UserCredit.vue'\nimport { useFirebaseAuthActions } from '@/composables/auth/useFirebaseAuthActions'\nimport { useDialogService } from '@/services/dialogService'\nimport { useFirebaseAuthStore } from '@/stores/firebaseAuthStore'\nimport { formatMetronomeCurrency } from '@/utils/formatUtil'\n\ninterface CreditHistoryItemData {\n  title: string\n  timestamp: string\n  amount: number\n  isPositive: boolean\n}\n\nconst { t } = useI18n()\nconst dialogService = useDialogService()\nconst authStore = useFirebaseAuthStore()\nconst authActions = useFirebaseAuthActions()\nconst loading = computed(() => authStore.loading)\nconst balanceLoading = computed(() => authStore.isFetchingBalance)\n\nconst formattedLastUpdateTime = computed(() =>\n  authStore.lastBalanceUpdateTime\n    ? authStore.lastBalanceUpdateTime.toLocaleString()\n    : ''\n)\n\nconst handlePurchaseCreditsClick = () => {\n  dialogService.showTopUpCreditsDialog()\n}\n\nconst handleCreditsHistoryClick = async () => {\n  await authActions.accessBillingPortal()\n}\n\nconst handleMessageSupport = () => {\n  dialogService.showIssueReportDialog({\n    title: t('issueReport.contactSupportTitle'),\n    subtitle: t('issueReport.contactSupportDescription'),\n    panelProps: {\n      errorType: 'BillingSupport',\n      defaultFields: ['Workflow', 'Logs', 'SystemStats', 'Settings']\n    }\n  })\n}\n\nconst handleFaqClick = () => {\n  window.open('https://docs.comfy.org/tutorials/api-nodes/faq', '_blank')\n}\n\nconst creditHistory = ref<CreditHistoryItemData[]>([])\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA8HM,UAAA,EAAE,MAAM;AACd,UAAM,gBAAgB;AACtB,UAAM,YAAY;AAClB,UAAM,cAAc;AACpB,UAAM,UAAU,SAAS,MAAM,UAAU,OAAO;AAChD,UAAM,iBAAiB,SAAS,MAAM,UAAU,iBAAiB;AAEjE,UAAM,0BAA0B;AAAA,MAAS,MACvC,UAAU,wBACN,UAAU,sBAAsB,eAChC,IAAA;AAAA,IAAA;AAGN,UAAM,6BAA6B,6BAAM;AACvC,oBAAc,uBAAuB;AAAA,IAAA,GADJ;AAInC,UAAM,4BAA4B,mCAAY;AAC5C,YAAM,YAAY;IAAoB,GADN;AAIlC,UAAM,uBAAuB,6BAAM;AACjC,oBAAc,sBAAsB;AAAA,QAClC,OAAO,EAAE,iCAAiC;AAAA,QAC1C,UAAU,EAAE,uCAAuC;AAAA,QACnD,YAAY;AAAA,UACV,WAAW;AAAA,UACX,eAAe,CAAC,YAAY,QAAQ,eAAe,UAAU;AAAA,QAC/D;AAAA,MAAA,CACD;AAAA,IAAA,GAR0B;AAW7B,UAAM,iBAAiB,6BAAM;AACpB,aAAA,KAAK,kDAAkD,QAAQ;AAAA,IAAA,GADjD;AAIjB,UAAA,gBAAgB,IAA6B,CAAA,CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}