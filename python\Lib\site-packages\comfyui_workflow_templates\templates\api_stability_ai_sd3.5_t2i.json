{"id": "e62e2c49-db9e-49bc-a71b-3871b67cc7da", "revision": 0, "last_node_id": 6, "last_link_id": 6, "nodes": [{"id": 3, "type": "<PERSON>downNote", "pos": [1005.7901611328125, 1342.15771484375], "size": [315.338134765625, 273.75128173828125], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 5, "type": "PrimitiveStringMultiline", "pos": [1348.************, 1712.************], "size": [274.*************, 184.*************], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [4]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.31", "Node name for S&R": "PrimitiveStringMultiline"}, "widgets_values": [""]}, {"id": 4, "type": "LoadImage", "pos": [1346.583740234375, 1338.996337890625], "size": [274.080078125, 314], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.31", "Node name for S&R": "LoadImage"}, "widgets_values": ["002.jpg", "image"]}, {"id": 6, "type": "StabilityStableImageSD_3_5Node", "pos": [1690.658935546875, 1343.3895263671875], "size": [406.4006042480469, 337.3551330566406], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 5}, {"name": "negative_prompt", "shape": 7, "type": "STRING", "link": 4}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [6]}], "properties": {"Node name for S&R": "StabilityStableImageSD_3_5Node"}, "widgets_values": ["a serene and peaceful scene of a glass cup filled with water and white daisies, the glass cup is positioned in the center of the image, with the flowers scattered around it, to the left of the cup, the camera is positioned directly, capturing the delicate petals and golden centers of the flowers, the background is blurred, with soft sunlight filtering through the leaves, creating a warm and inviting atmosphere, the overall effect is one of tranquility and natural beauty", "sd3.5-large", "1:1", "None", 4, 0, "randomize", 0.5], "color": "#432", "bgcolor": "#653"}, {"id": 2, "type": "SaveImage", "pos": [2130.786865234375, 1336.6605224609375], "size": [315, 365], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 6}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"filename_prefix": true}}, "widgets_values": ["ComfyUI"]}], "links": [[4, 5, 0, 6, 1, "STRING"], [5, 4, 0, 6, 0, "IMAGE"], [6, 6, 0, 2, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [-964.6499178931574, -1087.7462744920151]}, "frontendVersion": "1.23.4", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}