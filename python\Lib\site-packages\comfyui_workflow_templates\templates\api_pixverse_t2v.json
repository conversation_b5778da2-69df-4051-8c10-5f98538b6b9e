{"id": "40bd43e5-bb58-442a-9976-e5710e0041a0", "revision": 0, "last_node_id": 8, "last_link_id": 4, "nodes": [{"id": 6, "type": "SaveVideo", "pos": [830, 470], "size": [270, 249.875], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 3}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 4, "type": "PixverseTextToVideoNode", "pos": [390, 470], "size": [400, 252], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "negative_prompt", "shape": 7, "type": "STRING", "link": null}, {"name": "pixverse_template", "shape": 7, "type": "PIXVERSE_TEMPLATE", "link": 4}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [3]}], "properties": {"Node name for S&R": "PixverseTextToVideoNode"}, "widgets_values": ["A band of adorable toy rabbits rocks out on the moon's surface. They play a variety of instruments like guitars, drums, and keyboards under a star - filled sky with the Earth and moon in the background. Concert lights add a vibrant touch to this intergalactic musical performance. ", "16:9", "720p", 5, "normal", 1233550256, "randomize"]}, {"id": 8, "type": "PixverseTemplateNode", "pos": [92.2316665649414, 489.9173583984375], "size": [270, 58], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "pixverse_template", "type": "PIXVERSE_TEMPLATE", "links": [4]}], "properties": {"Node name for S&R": "PixverseTemplateNode"}, "widgets_values": ["Microwave"]}, {"id": 7, "type": "<PERSON>downNote", "pos": [-299.1886291503906, 488.6665344238281], "size": [370.8778991699219, 293.9327697753906], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[3, 4, 0, 6, 0, "VIDEO"], [4, 8, 0, 4, 1, "PIXVERSE_TEMPLATE"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.18.5"}, "version": 0.4}