ConditioningSetAreaPercentage 노드는 조건 요소의 영향 영역을 백분율 값에 따라 조정하는 데 특화되어 있습니다. 전체 이미지 크기의 백분율로 영역의 크기와 위치를 지정할 수 있으며, 조건 효과의 강도를 조절하는 강도 매개변수도 포함되어 있습니다.

## 입력

| 매개변수       | 데이터 유형  | 설명                                                                                                            |
| -------------- | ------------ | --------------------------------------------------------------------------------------------------------------- |
| `CONDITIONING` | CONDITIONING | 수정될 조건 요소를 나타내며, 영역 및 강도 조정을 적용하는 기초가 됩니다.                                        |
| `width`        | `FLOAT`      | 전체 이미지 너비의 백분율로 영역의 너비를 지정하여, 조건이 수평으로 이미지에 얼마나 영향을 미치는지 결정합니다. |
| `height`       | `FLOAT`      | 전체 이미지 높이의 백분율로 영역의 높이를 결정하여, 조건의 영향이 수직으로 얼마나 미치는지 결정합니다.          |
| `x`            | `FLOAT`      | 전체 이미지 너비의 백분율로 영역의 수평 시작점을 나타내어, 조건 효과의 위치를 지정합니다.                       |
| `y`            | `FLOAT`      | 전체 이미지 높이의 백분율로 영역의 수직 시작점을 지정하여, 조건 효과의 위치를 지정합니다.                       |
| `strength`     | `FLOAT`      | 지정된 영역 내에서 조건 효과의 강도를 조절하여, 그 영향을 세밀하게 조정할 수 있습니다.                          |

## 출력

| 매개변수       | 데이터 유형  | 설명                                                                                       |
| -------------- | ------------ | ------------------------------------------------------------------------------------------ |
| `CONDITIONING` | CONDITIONING | 수정된 영역 및 강도 매개변수가 적용된 조건 요소를 반환하여, 추가 처리나 적용을 준비합니다. |
