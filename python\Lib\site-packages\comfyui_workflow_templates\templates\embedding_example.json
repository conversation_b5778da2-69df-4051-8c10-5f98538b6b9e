{"last_node_id": 10, "last_link_id": 9, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": [425.28, 180.61], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad hands"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": [422.85, 164.31], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["photograph in the style of embedding:SDA768.pt girl with blonde hair\nlandscape scenery view"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": [210, 46], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [26, 474], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [8], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "v2-1_768-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-1/resolve/main/v2-1_768-ema-pruned.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["v2-1_768-ema-pruned.safetensors"]}, {"id": 9, "type": "SaveImage", "pos": [1451, 189], "size": [469, 528], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 768, 1]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 186], "size": [315, 262], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [193694018275622, "randomize", 20, 8, "uni_pc_bh2", "normal", 1]}, {"id": 10, "type": "<PERSON>downNote", "pos": [30, 630], "size": [225, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/textual_inversion_embeddings/)"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [9, 8, 0, 9, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.84, "offset": [498.31, 149.5]}}, "version": 0.4}