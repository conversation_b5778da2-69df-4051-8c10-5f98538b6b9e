{"version": 3, "file": "BaseViewTemplate-CpCvKU12.js", "sources": ["../../src/views/templates/BaseViewTemplate.vue"], "sourcesContent": ["<template>\n  <div\n    class=\"font-sans w-screen h-screen flex flex-col\"\n    :class=\"[\n      dark\n        ? 'text-neutral-300 bg-neutral-900 dark-theme'\n        : 'text-neutral-900 bg-neutral-300'\n    ]\"\n  >\n    <!-- Virtual top menu for native window (drag handle) -->\n    <div\n      v-show=\"isNativeWindow()\"\n      ref=\"topMenuRef\"\n      class=\"app-drag w-full h-[var(--comfy-topbar-height)]\"\n    />\n    <div\n      class=\"flex-grow w-full flex items-center justify-center overflow-auto\"\n    >\n      <slot />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { nextTick, onMounted, ref } from 'vue'\n\nimport { electronAPI, isElectron, isNativeWindow } from '@/utils/envUtil'\n\nconst { dark = false } = defineProps<{\n  dark?: boolean\n}>()\n\nconst darkTheme = {\n  color: 'rgba(0, 0, 0, 0)',\n  symbolColor: '#d4d4d4'\n}\n\nconst lightTheme = {\n  color: 'rgba(0, 0, 0, 0)',\n  symbolColor: '#171717'\n}\n\nconst topMenuRef = ref<HTMLDivElement | null>(null)\nonMounted(async () => {\n  if (isElectron()) {\n    await nextTick()\n\n    electronAPI().changeTheme({\n      ...(dark ? darkTheme : lightTheme),\n      height: topMenuRef.value?.getBoundingClientRect().height ?? 0\n    })\n  }\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;AAgCA,UAAM,YAAY;AAAA,MAChB,OAAO;AAAA,MACP,aAAa;AAAA,IAAA;AAGf,UAAM,aAAa;AAAA,MACjB,OAAO;AAAA,MACP,aAAa;AAAA,IAAA;AAGT,UAAA,aAAa,IAA2B,IAAI;AAClD,cAAU,YAAY;AACpB,UAAI,cAAc;AAChB,cAAM,SAAS;AAEf,oBAAA,EAAc,YAAY;AAAA,UACxB,GAAI,eAAO,YAAY;AAAA,UACvB,QAAQ,WAAW,OAAO,wBAAwB,UAAU;AAAA,QAAA,CAC7D;AAAA,MACH;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;"}