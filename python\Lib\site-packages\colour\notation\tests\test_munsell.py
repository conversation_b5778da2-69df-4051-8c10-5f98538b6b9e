"""Define the unit tests for the :mod:`colour.notation.munsell` module."""

from __future__ import annotations

import contextlib
from itertools import product

import numpy as np
import pytest

from colour.constants import TOLERANCE_ABSOLUTE_TESTS
from colour.hints import NDArrayFloat
from colour.notation import (
    munsell_value_ASTMD1535,
    munsell_value_Ladd1955,
    munsell_value_McCamy1987,
    munsell_value_Moon1943,
    munsell_value_Munsell1933,
    munsell_value_Priest1920,
    munsell_value_Saunderson1944,
)
from colour.notation.munsell import (
    CCS_ILLUMINANT_MUNSELL,
    LCHab_to_munsell_specification,
    bounding_hues_from_renotation,
    hue_angle_to_hue,
    hue_to_ASTM_hue,
    hue_to_hue_angle,
    interpolation_method_from_renotation_ovoid,
    is_grey_munsell_colour,
    is_specification_in_renotation,
    maximum_chroma_from_renotation,
    munsell_colour_to_munsell_specification,
    munsell_colour_to_xyY,
    munsell_specification_to_munsell_colour,
    munsell_specification_to_xy,
    munsell_specification_to_xyY,
    normalise_munsell_specification,
    parse_munsell_colour,
    xy_from_renotation_ovoid,
    xyY_from_renotation,
    xyY_to_munsell_colour,
    xyY_to_munsell_specification,
)
from colour.utilities import (
    as_array,
    as_float_array,
    domain_range_scale,
    ignore_numpy_errors,
    tstack,
)

__author__ = "Colour Developers"
__copyright__ = "Copyright 2013 Colour Developers"
__license__ = "BSD-3-Clause - https://opensource.org/licenses/BSD-3-Clause"
__maintainer__ = "Colour Developers"
__email__ = "<EMAIL>"
__status__ = "Production"

__all__ = [
    "MUNSELL_SPECIFICATIONS",
    "MUNSELL_GREYS_SPECIFICATIONS",
    "MUNSELL_EVEN_SPECIFICATIONS",
    "MUNSELL_BOUNDING_HUES",
    "MUNSELL_HUE_TO_ANGLE",
    "MUNSELL_HUE_TO_ASTM_HUE",
    "MUNSELL_INTERPOLATION_METHODS",
    "MUNSELL_XY_FROM_RENOTATION_OVOID",
    "TestMunsellValuePriest1920",
    "TestMunsellValueMunsell1933",
    "TestMunsellValueMoon1943",
    "TestMunsellValueSaunderson1944",
    "TestMunsellValueLadd1955",
    "TestMunsellValueMcCamy1992",
    "TestMunsellValueASTMD1535",
    "TestMunsellSpecification_to_xyY",
    "TestMunsellColour_to_xyY",
    "TestxyY_to_munsell_specification",
    "TestxyY_to_munsell_colour",
    "TestParseMunsellColour",
    "TestIsGreyMunsellColour",
    "TestNormaliseMunsellSpecification",
    "TestMunsellColourToMunsellSpecification",
    "TestMunsellSpecificationToMunsellColour",
    "Test_xyY_fromRenotation",
    "TestIsSpecificationInRenotation",
    "TestBoundingHuesFromRenotation",
    "TestHueToHueAngle",
    "TestHueAngleToHue",
    "TestHueTo_ASTM_hue",
    "TestInterpolationMethodFromRenotationOvoid",
    "Test_xy_fromRenotationOvoid",
    "TestLCHabToMunsellSpecification",
    "TestMaximumChromaFromRenotation",
    "TestMunsellSpecification_to_xy",
]


def _generate_unit_tests_specifications() -> tuple:  # pragma: no cover
    """
    Generate the unit tests specifications.

    Returns
    -------
    :class:`tuple`
        Tuples of unit tests specifications. The first tuple represents even
        specifications and their corresponding *CIE xyY* colourspace values.
        The tuple represents random specifications and their corresponding
        *CIE xyY* colourspace values.
    """

    from colour.notation import MUNSELL_COLOURS

    np.random.seed(16)

    indexes = np.arange(len(MUNSELL_COLOURS["real"]))
    np.random.shuffle(indexes)

    specifications, specifications_r = [], []
    for i in indexes:
        munsell_colour = "{} {}/{}".format(*MUNSELL_COLOURS["real"][i][0])

        try:
            specification = munsell_colour_to_munsell_specification(munsell_colour)
            specification_r = specification + np.hstack(
                [np.random.uniform(-1, 1, 3), [0]]
            )

            xyY = munsell_specification_to_xyY(specification)
            xyY_r = munsell_specification_to_xyY(specification_r)

            _munsell_colour = xyY_to_munsell_colour(xyY)
            _munsell_colour_r = xyY_to_munsell_colour(xyY_r)

            specifications.append([specification, xyY])
            specifications_r.append([specification_r, xyY_r])

            if len(specifications) == 100:
                break
        except Exception as error:
            print(specification)  # noqa: T201
            print(error)  # noqa: T201

    return specifications, specifications_r


MUNSELL_SPECIFICATIONS: NDArrayFloat = as_array(
    [
        [
            [7.18927191, 5.34025196, 16.05861170, 3.00000000],
            [0.16623068, 0.45684550, 0.22399519],
        ],
        [
            [6.75749691, 9.44255422, 11.79641069, 3.00000000],
            [0.27297006, 0.36631948, 0.86427673],
        ],
        [
            [8.44964118, 7.42750072, 3.22576700, 7.00000000],
            [0.35016714, 0.32764928, 0.48285006],
        ],
        [
            [2.27432787, 1.59581362, 8.78434161, 2.00000000],
            [0.08494989, 0.35448667, 0.02152067],
        ],
        [
            [9.82364034, 5.10755230, 3.23527358, 6.00000000],
            [0.38135277, 0.37024591, 0.20229405],
        ],
        [
            [9.43158071, 6.93268985, 23.81322134, 9.00000000],
            [0.33551832, 0.17578313, 0.41043153],
        ],
        [
            [7.48132776, 2.89467904, 8.47029950, 7.00000000],
            [0.53871366, 0.32437859, 0.05953010],
        ],
        [
            [7.19717582, 1.79579794, 3.60295782, 2.00000000],
            [0.21606607, 0.29770875, 0.02567493],
        ],
        [
            [7.77779715, 1.83619425, 6.42105231, 1.00000000],
            [0.15191376, 0.18640506, 0.02657869],
        ],
        [
            [7.05646374, 8.15113870, 2.63767438, 10.00000000],
            [0.29508705, 0.29769267, 0.60266436],
        ],
        [
            [3.03969874, 8.41779328, 5.82455544, 10.00000000],
            [0.25900977, 0.27198272, 0.65133417],
        ],
        [
            [7.12616461, 4.61877951, 3.25823453, 9.00000000],
            [0.30806367, 0.27715003, 0.16105305],
        ],
        [
            [9.75994064, 5.00164995, 13.80804118, 3.00000000],
            [0.16988405, 0.41104446, 0.19286318],
        ],
        [
            [6.97677165, 8.25846010, 6.75337147, 2.00000000],
            [0.24767890, 0.32314418, 0.62194498],
        ],
        [
            [3.30599914, 6.13663591, 6.17907561, 1.00000000],
            [0.23048205, 0.28607980, 0.30872546],
        ],
        [
            [9.18340797, 7.81066836, 4.15020349, 7.00000000],
            [0.36349464, 0.33324537, 0.54413876],
        ],
        [
            [8.10631072, 3.26448077, 9.90156782, 1.00000000],
            [0.14334034, 0.18086971, 0.07589385],
        ],
        [
            [5.69603430, 5.94554581, 26.45021730, 3.00000000],
            [0.10047143, 0.54485967, 0.28688700],
        ],
        [
            [7.52941160, 3.10193862, 2.03837398, 2.00000000],
            [0.26959492, 0.31200708, 0.06836215],
        ],
        [
            [9.01080996, 8.59514291, 5.06552338, 2.00000000],
            [0.26018550, 0.31561977, 0.68515300],
        ],
        [
            [9.93350418, 6.92619502, 2.76022400, 3.00000000],
            [0.28894781, 0.33443098, 0.40952934],
        ],
        [
            [9.96785141, 1.11835600, 3.99853550, 9.00000000],
            [0.31352711, 0.20634513, 0.01346611],
        ],
        [
            [9.99840409, 3.19309711, 5.82065904, 10.00000000],
            [0.25414765, 0.20939962, 0.07251951],
        ],
        [
            [9.27080064, 2.36096768, 2.35873176, 9.00000000],
            [0.31412335, 0.26537265, 0.04053697],
        ],
        [
            [9.85478063, 4.04717472, 2.09829596, 5.00000000],
            [0.34970053, 0.37571825, 0.12005323],
        ],
        [
            [8.02558980, 6.36819633, 4.60141462, 5.00000000],
            [0.37965729, 0.40977154, 0.33650122],
        ],
        [
            [7.19056185, 7.66646120, 10.23200084, 3.00000000],
            [0.24865695, 0.39205285, 0.52051894],
        ],
        [
            [9.27459121, 8.93329764, 2.82639874, 3.00000000],
            [0.29214864, 0.33503056, 0.75302151],
        ],
        [
            [6.56514645, 4.20156404, 5.64991156, 9.00000000],
            [0.30195484, 0.24513718, 0.13037695],
        ],
        [
            [9.90928732, 9.28999046, 17.40567009, 4.00000000],
            [0.30741363, 0.49906135, 0.82974391],
        ],
        [
            [6.66033139, 7.82125559, 2.83656023, 10.00000000],
            [0.29234640, 0.29513484, 0.54589974],
        ],
        [
            [7.09563810, 9.27179818, 3.41469395, 6.00000000],
            [0.34662788, 0.34241664, 0.82569659],
        ],
        [
            [8.06308980, 7.35943482, 4.42642439, 8.00000000],
            [0.34290892, 0.30832480, 0.47244825],
        ],
        [
            [6.73937163, 4.45035611, 17.29157077, 2.00000000],
            [0.09315578, 0.28811552, 0.14817605],
        ],
        [
            [4.46091279, 4.97623184, 8.27347386, 3.00000000],
            [0.25257308, 0.42043736, 0.19064074],
        ],
        [
            [9.59823267, 4.43380834, 4.49782459, 3.00000000],
            [0.26156547, 0.35357051, 0.14694714],
        ],
        [
            [5.68236594, 5.20647917, 7.98231240, 4.00000000],
            [0.36926047, 0.50258483, 0.21135463],
        ],
        [
            [6.75749120, 9.13825880, 5.79055270, 3.00000000],
            [0.28340044, 0.35597801, 0.79643638],
        ],
        [
            [3.45818284, 2.10857125, 4.85791418, 8.00000000],
            [0.34999194, 0.24490799, 0.03328770],
        ],
        [
            [2.88591190, 3.36970167, 10.21423066, 7.00000000],
            [0.51380217, 0.28835519, 0.08106070],
        ],
        [
            [1.71270774, 1.53669828, 3.39403462, 9.00000000],
            [0.26997438, 0.21369276, 0.02038916],
        ],
        [
            [4.90655199, 3.23724997, 18.85327042, 2.00000000],
            [0.05681430, 0.29884718, 0.07459423],
        ],
        [
            [2.60942297, 4.07393682, 4.85686834, 8.00000000],
            [0.33889111, 0.27070427, 0.12180360],
        ],
        [
            [7.80566706, 4.07902945, 30.88921341, 9.00000000],
            [0.30271734, 0.09713576, 0.12213854],
        ],
        [
            [9.83519352, 8.12251250, 10.73221172, 5.00000000],
            [0.42441612, 0.48582983, 0.59759029],
        ],
        [
            [7.35317886, 7.08150026, 4.62737592, 5.00000000],
            [0.37586606, 0.40232896, 0.43144075],
        ],
        [
            [7.77389803, 6.90019200, 8.10157277, 9.00000000],
            [0.31242681, 0.25756495, 0.40592957],
        ],
        [
            [4.87250279, 4.19552668, 18.70870067, 9.00000000],
            [0.26755998, 0.13995403, 0.12996294],
        ],
        [
            [7.38366474, 3.35764668, 12.11239914, 9.00000000],
            [0.30031035, 0.16953394, 0.08045698],
        ],
        [
            [5.11434827, 6.25840324, 18.46717586, 9.00000000],
            [0.27423716, 0.17360892, 0.32315057],
        ],
        [
            [6.50234711, 7.39248581, 8.42947132, 8.00000000],
            [0.36895615, 0.29211972, 0.47748118],
        ],
        [
            [5.12057785, 6.74131875, 9.90906687, 9.00000000],
            [0.28824634, 0.23317117, 0.38435902],
        ],
        [
            [7.97767039, 3.80398969, 4.31555212, 7.00000000],
            [0.41347359, 0.33271080, 0.10489086],
        ],
        [
            [8.28257058, 4.99288145, 4.13426077, 10.00000000],
            [0.27534267, 0.26445867, 0.19209471],
        ],
        [
            [2.27833490, 3.13652250, 12.74644014, 9.00000000],
            [0.24604475, 0.14443079, 0.06991948],
        ],
        [
            [4.17788614, 4.56875266, 4.69980113, 8.00000000],
            [0.34608761, 0.28369563, 0.15715775],
        ],
        [
            [5.52166738, 2.72767471, 10.73842907, 8.00000000],
            [0.41659713, 0.21166956, 0.05302211],
        ],
        [
            [1.96964226, 5.83362103, 28.30224843, 3.00000000],
            [0.11730240, 0.75674218, 0.27454364],
        ],
        [
            [4.22034979, 7.29875716, 8.00238058, 7.00000000],
            [0.40163259, 0.32283516, 0.46329585],
        ],
        [
            [8.10122939, 7.84099254, 13.76355873, 4.00000000],
            [0.34218201, 0.54357248, 0.54919251],
        ],
        [
            [6.93175327, 3.88633728, 4.19698393, 4.00000000],
            [0.33428973, 0.42675447, 0.10987617],
        ],
        [
            [7.17972997, 7.76394588, 5.52955464, 5.00000000],
            [0.38311308, 0.41154624, 0.53641155],
        ],
        [
            [7.44539380, 2.45826571, 18.17553552, 8.00000000],
            [0.49593109, 0.16155048, 0.04361652],
        ],
        [
            [6.57942671, 5.70954742, 4.21109407, 7.00000000],
            [0.37391946, 0.32682537, 0.26124266],
        ],
        [
            [7.07918723, 3.60945090, 5.18867792, 10.00000000],
            [0.24565114, 0.22732867, 0.09371006],
        ],
        [
            [9.19704045, 7.64270856, 20.12511878, 4.00000000],
            [0.31036980, 0.63853638, 0.51669329],
        ],
        [
            [7.38744649, 2.69345468, 6.96087944, 8.00000000],
            [0.40651600, 0.25385677, 0.05175378],
        ],
        [
            [5.71814887, 1.35726940, 4.25987210, 6.00000000],
            [0.55344553, 0.38963438, 0.01720090],
        ],
        [
            [7.65193815, 5.15801515, 17.63637606, 8.00000000],
            [0.47344711, 0.24662331, 0.20688549],
        ],
        [
            [5.80035357, 8.46843486, 3.91395266, 7.00000000],
            [0.35101966, 0.32394467, 0.66087042],
        ],
        [
            [2.64017362, 6.58022725, 25.27474395, 3.00000000],
            [0.15857410, 0.65244571, 0.36322040],
        ],
        [
            [6.81904597, 7.52735613, 14.14454379, 5.00000000],
            [0.46547138, 0.50598542, 0.49837113],
        ],
        [
            [1.72002655, 2.00166767, 15.17756223, 3.00000000],
            [0.06593801, 0.78772140, 0.03052292],
        ],
        [
            [7.17387426, 7.40686142, 4.67300544, 6.00000000],
            [0.38508722, 0.36570863, 0.47968081],
        ],
        [
            [5.66354474, 8.29189799, 7.88289254, 2.00000000],
            [0.24153734, 0.33167097, 0.62803620],
        ],
        [
            [4.96369167, 6.11709577, 2.83010340, 9.00000000],
            [0.30299188, 0.28933160, 0.30644773],
        ],
        [
            [9.86666880, 3.53860824, 3.76283439, 4.00000000],
            [0.30878925, 0.40194660, 0.08984394],
        ],
        [
            [2.54469570, 1.94205063, 6.51472847, 8.00000000],
            [0.34838418, 0.21610764, 0.02905603],
        ],
        [
            [7.41841874, 7.28061720, 7.52688380, 5.00000000],
            [0.41055673, 0.44673738, 0.46058155],
        ],
        [
            [1.75980807, 3.96245086, 15.45788733, 3.00000000],
            [0.19389711, 0.63957903, 0.11461936],
        ],
        [
            [4.71729705, 3.34696841, 7.66988506, 6.00000000],
            [0.52645405, 0.39761412, 0.07992475],
        ],
        [
            [2.08610570, 6.73042407, 7.05471426, 1.00000000],
            [0.22763195, 0.29145327, 0.38290628],
        ],
        [
            [2.27268990, 3.26447649, 7.69468499, 6.00000000],
            [0.53002779, 0.37341819, 0.07589364],
        ],
        [
            [7.23049017, 4.12309620, 15.10943786, 8.00000000],
            [0.46585026, 0.23748776, 0.12506155],
        ],
        [
            [6.64140481, 2.17555713, 5.41523047, 7.00000000],
            [0.46318547, 0.31027036, 0.03511077],
        ],
        [
            [7.37490620, 1.75480377, 16.97212717, 9.00000000],
            [0.28825940, 0.09568892, 0.02478061],
        ],
        [
            [4.33290089, 2.10328054, 2.19891461, 10.00000000],
            [0.25933572, 0.26024656, 0.03314673],
        ],
        [
            [9.12491352, 3.86980063, 15.33312818, 3.00000000],
            [0.12639463, 0.43809589, 0.10886288],
        ],
        [
            [3.39633194, 4.88689265, 23.54167471, 3.00000000],
            [0.10976610, 0.64434470, 0.18295497],
        ],
        [
            [2.61214646, 1.14977763, 7.81503339, 2.00000000],
            [0.06586815, 0.34294740, 0.01392758],
        ],
        [
            [3.29714039, 2.54520254, 13.61325521, 8.00000000],
            [0.39022459, 0.16803475, 0.04650827],
        ],
        [
            [2.78196233, 4.33925419, 16.17790238, 3.00000000],
            [0.17272546, 0.59343082, 0.14004876],
        ],
        [
            [7.51260170, 3.39633095, 9.42584908, 2.00000000],
            [0.14825319, 0.28502763, 0.08240516],
        ],
        [
            [4.46597913, 8.02986601, 3.85801792, 1.00000000],
            [0.26863443, 0.30193859, 0.58136398],
        ],
        [
            [2.29263540, 4.86650662, 7.08151101, 8.00000000],
            [0.34377887, 0.26102802, 0.18122849],
        ],
        [
            [6.82015249, 5.14066765, 21.13348572, 9.00000000],
            [0.29448492, 0.15338239, 0.20529997],
        ],
        [
            [4.31245640, 4.85614027, 11.50803662, 6.00000000],
            [0.54231929, 0.40629376, 0.18035446],
        ],
        [
            [8.22108412, 5.80818951, 3.94579416, 8.00000000],
            [0.34702328, 0.30674052, 0.27178470],
        ],
        [
            [8.31388115, 4.66785495, 3.69434623, 5.00000000],
            [0.38078540, 0.41116296, 0.16493242],
        ],
        [
            [3.40490668, 6.59689139, 9.20874115, 7.00000000],
            [0.41967010, 0.31821655, 0.36537324],
        ],
    ],
    dtype=object,  # pyright: ignore
)

MUNSELL_GREYS_SPECIFICATIONS: NDArrayFloat = as_array(
    list(
        zip(
            np.linspace(0, 10, 25)[:, None],
            (
                [0.31006, 0.31616, 0.00000000],
                [0.31006, 0.31616, 0.00473582],
                [0.31006, 0.31616, 0.00961944],
                [0.31006, 0.31616, 0.01545756],
                [0.31006, 0.31616, 0.02293343],
                [0.31006, 0.31616, 0.03261914],
                [0.31006, 0.31616, 0.04498800],
                [0.31006, 0.31616, 0.06042690],
                [0.31006, 0.31616, 0.07924864],
                [0.31006, 0.31616, 0.10170428],
                [0.31006, 0.31616, 0.12799549],
                [0.31006, 0.31616, 0.15828689],
                [0.31006, 0.31616, 0.19271844],
                [0.31006, 0.31616, 0.23141772],
                [0.31006, 0.31616, 0.27451233],
                [0.31006, 0.31616, 0.32214224],
                [0.31006, 0.31616, 0.37447210],
                [0.31006, 0.31616, 0.43170362],
                [0.31006, 0.31616, 0.49408790],
                [0.31006, 0.31616, 0.56193781],
                [0.31006, 0.31616, 0.63564030],
                [0.31006, 0.31616, 0.71566876],
                [0.31006, 0.31616, 0.80259539],
                [0.31006, 0.31616, 0.89710353],
                [0.31006, 0.31616, 1.00000000],
            ),
        )
    ),
    dtype=object,  # pyright: ignore
)

MUNSELL_EVEN_SPECIFICATIONS: NDArrayFloat = as_array(
    [
        [(7.5, 6.0, 16.0, 3), [0.18320000, 0.44140000, 0.29301153]],
        [(7.5, 9.0, 12.0, 3), [0.24190000, 0.39850000, 0.76695586]],
        [(7.5, 8.0, 4.0, 7), [0.35640000, 0.32790000, 0.57619628]],
        [(2.5, 2.0, 8.0, 2), [0.15570000, 0.35170000, 0.03048116]],
        [(10.0, 6.0, 4.0, 6), [0.38610000, 0.37670000, 0.29301153]],
        [(10.0, 6.0, 24.0, 9), [0.34410000, 0.16980000, 0.29301153]],
        [(7.5, 2.0, 8.0, 7), [0.54330000, 0.30270000, 0.03048116]],
        [(7.5, 1.0, 4.0, 2), [0.17020000, 0.27680000, 0.01179925]],
        [(7.5, 1.0, 6.0, 1), [0.13030000, 0.16390000, 0.01179925]],
        [(7.5, 9.0, 2.0, 10), [0.30150000, 0.30520000, 0.76695586]],
        [(2.5, 8.0, 6.0, 10), [0.25620000, 0.27090000, 0.57619628]],
        [(7.5, 5.0, 4.0, 9), [0.31000000, 0.27500000, 0.19271844]],
        [(10.0, 5.0, 14.0, 3), [0.16710000, 0.40890000, 0.19271844]],
        [(7.5, 9.0, 6.0, 2), [0.25430000, 0.32200000, 0.76695586]],
        [(2.5, 6.0, 6.0, 1), [0.23120000, 0.28990000, 0.29301153]],
        [(10.0, 8.0, 4.0, 7), [0.36210000, 0.33490000, 0.57619628]],
        [(7.5, 4.0, 10.0, 1), [0.16010000, 0.20280000, 0.11700751]],
        [(5.0, 5.0, 26.0, 3), [0.07840000, 0.57610000, 0.19271844]],
        [(7.5, 3.0, 2.0, 2), [0.26990000, 0.31200000, 0.06391178]],
        [(10.0, 9.0, 6.0, 2), [0.25010000, 0.31180000, 0.76695586]],
        [(10.0, 6.0, 2.0, 3), [0.29290000, 0.33030000, 0.29301153]],
        [(10.0, 1.0, 4.0, 9), [0.31320000, 0.20320000, 0.01179925]],
        [(10.0, 3.0, 6.0, 10), [0.25110000, 0.20310000, 0.06391178]],
        [(10.0, 2.0, 2.0, 9), [0.31610000, 0.26910000, 0.03048116]],
        [(10.0, 5.0, 2.0, 5), [0.34220000, 0.36480000, 0.19271844]],
        [(7.5, 6.0, 4.0, 5), [0.37450000, 0.40040000, 0.29301153]],
        [(7.5, 7.0, 10.0, 3), [0.24450000, 0.39140000, 0.41985394]],
        [(10.0, 9.0, 2.0, 3), [0.29650000, 0.32930000, 0.76695586]],
        [(7.5, 4.0, 6.0, 9), [0.30760000, 0.24160000, 0.11700751]],
        [(10.0, 9.0, 18.0, 4), [0.30320000, 0.57480000, 0.76695586]],
        [(7.5, 7.0, 2.0, 10), [0.29820000, 0.30030000, 0.41985394]],
        [(7.5, 9.0, 4.0, 6), [0.36790000, 0.35850000, 0.76695586]],
        [(7.5, 7.0, 4.0, 8), [0.33890000, 0.30790000, 0.41985394]],
        [(7.5, 5.0, 18.0, 2), [0.09820000, 0.28280000, 0.19271844]],
        [(5.0, 5.0, 8.0, 3), [0.25110000, 0.41070000, 0.19271844]],
        [(10.0, 5.0, 4.0, 3), [0.27110000, 0.34550000, 0.19271844]],
        [(5.0, 5.0, 8.0, 4), [0.38150000, 0.50930000, 0.19271844]],
        [(7.5, 9.0, 6.0, 3), [0.27630000, 0.36070000, 0.76695586]],
        [(2.5, 2.0, 4.0, 8), [0.33820000, 0.24960000, 0.03048116]],
        [(2.5, 4.0, 10.0, 7), [0.47740000, 0.29690000, 0.11700751]],
        [(2.5, 2.0, 4.0, 9), [0.27580000, 0.22080000, 0.03048116]],
        [(5.0, 4.0, 18.0, 2), [0.08280000, 0.31080000, 0.11700751]],
        [(2.5, 5.0, 4.0, 8), [0.32980000, 0.28690000, 0.19271844]],
        [(7.5, 4.0, 30.0, 9), [0.29690000, 0.09790000, 0.11700751]],
        [(10.0, 8.0, 10.0, 5), [0.41900000, 0.47910000, 0.57619628]],
        [(7.5, 8.0, 4.0, 5), [0.36220000, 0.38610000, 0.57619628]],
        [(7.5, 6.0, 8.0, 9), [0.30990000, 0.25020000, 0.29301153]],
        [(5.0, 5.0, 18.0, 9), [0.27180000, 0.16040000, 0.19271844]],
        [(7.5, 4.0, 12.0, 9), [0.30450000, 0.19050000, 0.11700751]],
        [(5.0, 6.0, 18.0, 9), [0.27310000, 0.17380000, 0.29301153]],
        [(7.5, 8.0, 8.0, 8), [0.36820000, 0.29830000, 0.57619628]],
        [(5.0, 6.0, 10.0, 9), [0.28620000, 0.22600000, 0.29301153]],
        [(7.5, 3.0, 4.0, 7), [0.42400000, 0.33020000, 0.06391178]],
        [(7.5, 4.0, 4.0, 10), [0.26570000, 0.25280000, 0.11700751]],
        [(2.5, 4.0, 12.0, 9), [0.25590000, 0.17300000, 0.11700751]],
        [(5.0, 4.0, 4.0, 8), [0.34910000, 0.28720000, 0.11700751]],
        [(5.0, 3.0, 10.0, 8), [0.40730000, 0.22350000, 0.06391178]],
        [(2.5, 5.0, 28.0, 3), [0.07940000, 0.73850000, 0.19271844]],
        [(5.0, 8.0, 8.0, 7), [0.40010000, 0.32630000, 0.57619628]],
        [(7.5, 8.0, 14.0, 4), [0.35460000, 0.54900000, 0.57619628]],
        [(7.5, 3.0, 4.0, 4), [0.32700000, 0.42880000, 0.06391178]],
        [(7.5, 7.0, 6.0, 5), [0.39430000, 0.42640000, 0.41985394]],
        [(7.5, 3.0, 18.0, 8), [0.51300000, 0.18930000, 0.06391178]],
        [(7.5, 5.0, 4.0, 7), [0.38060000, 0.32940000, 0.19271844]],
        [(7.5, 3.0, 6.0, 10), [0.23110000, 0.20100000, 0.06391178]],
        [(10.0, 7.0, 20.0, 4), [0.28160000, 0.65630000, 0.41985394]],
        [(7.5, 3.0, 6.0, 8), [0.39900000, 0.27080000, 0.06391178]],
        [(5.0, 1.0, 4.0, 6), [0.56600000, 0.37950000, 0.01179925]],
        [(7.5, 6.0, 18.0, 8), [0.45810000, 0.25490000, 0.29301153]],
        [(5.0, 9.0, 4.0, 7), [0.34950000, 0.32260000, 0.76695586]],
        [(2.5, 6.0, 26.0, 3), [0.13400000, 0.68710000, 0.29301153]],
        [(7.5, 8.0, 14.0, 5), [0.45740000, 0.50620000, 0.57619628]],
        [(2.5, 2.0, 16.0, 3), [0.03290000, 0.73580000, 0.03048116]],
        [(7.5, 8.0, 4.0, 6), [0.36990000, 0.35860000, 0.57619628]],
        [(5.0, 8.0, 8.0, 2), [0.24190000, 0.33520000, 0.57619628]],
        [(5.0, 6.0, 2.0, 9), [0.30500000, 0.29670000, 0.29301153]],
        [(10.0, 4.0, 4.0, 4), [0.31000000, 0.40180000, 0.11700751]],
        [(2.5, 2.0, 6.0, 8), [0.34700000, 0.22590000, 0.03048116]],
        [(7.5, 7.0, 8.0, 5), [0.41840000, 0.45680000, 0.41985394]],
        [(2.5, 3.0, 16.0, 3), [0.13410000, 0.64200000, 0.06391178]],
        [(5.0, 3.0, 8.0, 6), [0.54560000, 0.40400000, 0.06391178]],
        [(2.5, 6.0, 8.0, 1), [0.20800000, 0.27890000, 0.29301153]],
        [(2.5, 4.0, 8.0, 6), [0.50710000, 0.37770000, 0.11700751]],
        [(7.5, 5.0, 16.0, 8), [0.46170000, 0.25060000, 0.19271844]],
        [(7.5, 2.0, 6.0, 7), [0.48750000, 0.31230000, 0.03048116]],
        [(7.5, 2.0, 16.0, 9), [0.29220000, 0.11060000, 0.03048116]],
        [(5.0, 2.0, 2.0, 10), [0.26380000, 0.26240000, 0.03048116]],
        [(10.0, 3.0, 16.0, 3), [0.09250000, 0.42750000, 0.06391178]],
        [(2.5, 5.0, 24.0, 3), [0.11880000, 0.69180000, 0.19271844]],
        [(2.5, 1.0, 8.0, 2), [0.04760000, 0.34580000, 0.01179925]],
        [(2.5, 2.0, 14.0, 8), [0.37110000, 0.14490000, 0.03048116]],
        [(2.5, 5.0, 16.0, 3), [0.20050000, 0.57590000, 0.19271844]],
        [(7.5, 3.0, 10.0, 2), [0.13260000, 0.27840000, 0.06391178]],
        [(5.0, 8.0, 4.0, 1), [0.26710000, 0.29980000, 0.57619628]],
        [(2.5, 5.0, 8.0, 8), [0.34900000, 0.25700000, 0.19271844]],
        [(7.5, 5.0, 22.0, 9), [0.30380000, 0.15000000, 0.19271844]],
        [(5.0, 5.0, 12.0, 6), [0.54220000, 0.41410000, 0.19271844]],
        [(7.5, 5.0, 4.0, 8), [0.35150000, 0.30240000, 0.19271844]],
        [(7.5, 5.0, 4.0, 5), [0.38500000, 0.41200000, 0.19271844]],
        [(2.5, 6.0, 10.0, 7), [0.43200000, 0.31180000, 0.29301153]],
        [(8.0, 2, 14.0, 1), [0.07257382, 0.10413956, 0.03048116]],
    ],
    dtype=object,  # pyright: ignore
)

MUNSELL_BOUNDING_HUES: NDArrayFloat = as_float_array(
    [
        ((5.0, 3.0), (7.5, 3.0)),
        ((5.0, 3.0), (7.5, 3.0)),
        ((7.5, 7.0), (10, 7.0)),
        ((10, 3.0), (2.5, 2.0)),
        ((7.5, 6.0), (10, 6.0)),
        ((7.5, 9.0), (10, 9.0)),
        ((5.0, 7.0), (7.5, 7.0)),
        ((5.0, 2.0), (7.5, 2.0)),
        ((7.5, 1.0), (10, 1.0)),
        ((5.0, 10.0), (7.5, 10.0)),
        ((2.5, 10.0), (5.0, 10.0)),
        ((5.0, 9.0), (7.5, 9.0)),
        ((7.5, 3.0), (10, 3.0)),
        ((5.0, 2.0), (7.5, 2.0)),
        ((2.5, 1.0), (5.0, 1.0)),
        ((7.5, 7.0), (10, 7.0)),
        ((7.5, 1.0), (10, 1.0)),
        ((5.0, 3.0), (7.5, 3.0)),
        ((7.5, 2.0), (10, 2.0)),
        ((7.5, 2.0), (10, 2.0)),
        ((7.5, 3.0), (10, 3.0)),
        ((7.5, 9.0), (10, 9.0)),
        ((7.5, 10.0), (10, 10.0)),
        ((7.5, 9.0), (10, 9.0)),
        ((7.5, 5.0), (10, 5.0)),
        ((7.5, 5.0), (10, 5.0)),
        ((5.0, 3.0), (7.5, 3.0)),
        ((7.5, 3.0), (10, 3.0)),
        ((5.0, 9.0), (7.5, 9.0)),
        ((7.5, 4.0), (10, 4.0)),
        ((5.0, 10.0), (7.5, 10.0)),
        ((5.0, 6.0), (7.5, 6.0)),
        ((7.5, 8.0), (10, 8.0)),
        ((5.0, 2.0), (7.5, 2.0)),
        ((2.5, 3.0), (5.0, 3.0)),
        ((7.5, 3.0), (10, 3.0)),
        ((5.0, 4.0), (7.5, 4.0)),
        ((5.0, 3.0), (7.5, 3.0)),
        ((2.5, 8.0), (5.0, 8.0)),
        ((2.5, 7.0), (5.0, 7.0)),
        ((10, 10), (2.5, 9.0)),
        ((2.5, 2.0), (5.0, 2.0)),
        ((2.5, 8.0), (5.0, 8.0)),
        ((7.5, 9.0), (10, 9.0)),
        ((7.5, 5.0), (10, 5.0)),
        ((5.0, 5.0), (7.5, 5.0)),
        ((7.5, 9.0), (10, 9.0)),
        ((2.5, 9.0), (5.0, 9.0)),
        ((5.0, 9.0), (7.5, 9.0)),
        ((5.0, 9.0), (7.5, 9.0)),
        ((5.0, 8.0), (7.5, 8.0)),
        ((5.0, 9.0), (7.5, 9.0)),
        ((7.5, 7.0), (10, 7.0)),
        ((7.5, 10.0), (10, 10.0)),
        ((10, 10), (2.5, 9.0)),
        ((2.5, 8.0), (5.0, 8.0)),
        ((5.0, 8.0), (7.5, 8.0)),
        ((10, 4.0), (2.5, 3.0)),
        ((2.5, 7.0), (5.0, 7.0)),
        ((7.5, 4.0), (10, 4.0)),
        ((5.0, 4.0), (7.5, 4.0)),
        ((5.0, 5.0), (7.5, 5.0)),
        ((5.0, 8.0), (7.5, 8.0)),
        ((5.0, 7.0), (7.5, 7.0)),
        ((5.0, 10.0), (7.5, 10.0)),
        ((7.5, 4.0), (10, 4.0)),
        ((5.0, 8.0), (7.5, 8.0)),
        ((5.0, 6.0), (7.5, 6.0)),
        ((7.5, 8.0), (10, 8.0)),
        ((5.0, 7.0), (7.5, 7.0)),
        ((2.5, 3.0), (5.0, 3.0)),
        ((5.0, 5.0), (7.5, 5.0)),
        ((10, 4.0), (2.5, 3.0)),
        ((5.0, 6.0), (7.5, 6.0)),
        ((5.0, 2.0), (7.5, 2.0)),
        ((2.5, 9.0), (5.0, 9.0)),
        ((7.5, 4.0), (10, 4.0)),
        ((2.5, 8.0), (5.0, 8.0)),
        ((5.0, 5.0), (7.5, 5.0)),
        ((10, 4.0), (2.5, 3.0)),
        ((2.5, 6.0), (5.0, 6.0)),
        ((10, 2.0), (2.5, 1.0)),
        ((10, 7.0), (2.5, 6.0)),
        ((5.0, 8.0), (7.5, 8.0)),
        ((5.0, 7.0), (7.5, 7.0)),
        ((5.0, 9.0), (7.5, 9.0)),
        ((2.5, 10.0), (5.0, 10.0)),
        ((7.5, 3.0), (10, 3.0)),
        ((2.5, 3.0), (5.0, 3.0)),
        ((2.5, 2.0), (5.0, 2.0)),
        ((2.5, 8.0), (5.0, 8.0)),
        ((2.5, 3.0), (5.0, 3.0)),
        ((7.5, 2.0), (10, 2.0)),
        ((2.5, 1.0), (5.0, 1.0)),
        ((10, 9.0), (2.5, 8.0)),
        ((5.0, 9.0), (7.5, 9.0)),
        ((2.5, 6.0), (5.0, 6.0)),
        ((7.5, 8.0), (10, 8.0)),
        ((7.5, 5.0), (10, 5.0)),
        ((2.5, 7.0), (5.0, 7.0)),
    ]
)

MUNSELL_HUE_TO_ANGLE: NDArrayFloat = np.array(
    [
        [2.5, 1, 208.750],
        [2.5, 2, 153.750],
        [2.5, 3, 118.750],
        [2.5, 4, 63.750],
        [2.5, 5, 39.375],
        [2.5, 6, 16.875],
        [2.5, 7, 348.750],
        [2.5, 8, 300.000],
        [2.5, 9, 251.250],
        [2.5, 10, 236.250],
        [5.0, 1, 225.000],
        [5.0, 2, 160.000],
        [5.0, 3, 135.000],
        [5.0, 4, 70.000],
        [5.0, 5, 45.000],
        [5.0, 6, 22.500],
        [5.0, 7, 0.000],
        [5.0, 8, 315.000],
        [5.0, 9, 255.000],
        [5.0, 10, 240.000],
        [7.5, 1, 228.750],
        [7.5, 2, 176.250],
        [7.5, 3, 141.250],
        [7.5, 4, 86.250],
        [7.5, 5, 51.250],
        [7.5, 6, 28.125],
        [7.5, 7, 5.625],
        [7.5, 8, 326.250],
        [7.5, 9, 270.000],
        [7.5, 10, 243.750],
        [10.0, 1, 232.500],
        [10.0, 2, 192.500],
        [10.0, 3, 147.500],
        [10.0, 4, 102.500],
        [10.0, 5, 57.500],
        [10.0, 6, 33.750],
        [10.0, 7, 11.250],
        [10.0, 8, 337.500],
        [10.0, 9, 285.000],
        [10.0, 10, 247.500],
    ]
)

MUNSELL_HUE_TO_ASTM_HUE: NDArrayFloat = np.array(
    [
        [2.5, 0, 72.5],
        [2.5, 1, 62.5],
        [2.5, 2, 52.5],
        [2.5, 3, 42.5],
        [2.5, 4, 32.5],
        [2.5, 5, 22.5],
        [2.5, 6, 12.5],
        [2.5, 7, 2.5],
        [2.5, 8, 92.5],
        [2.5, 9, 82.5],
        [2.5, 10, 72.5],
        [5.0, 0, 75.0],
        [5.0, 1, 65.0],
        [5.0, 2, 55.0],
        [5.0, 3, 45.0],
        [5.0, 4, 35.0],
        [5.0, 5, 25.0],
        [5.0, 6, 15.0],
        [5.0, 7, 5.0],
        [5.0, 8, 95.0],
        [5.0, 9, 85.0],
        [5.0, 10, 75.0],
        [7.5, 0, 77.5],
        [7.5, 1, 67.5],
        [7.5, 2, 57.5],
        [7.5, 3, 47.5],
        [7.5, 4, 37.5],
        [7.5, 5, 27.5],
        [7.5, 6, 17.5],
        [7.5, 7, 7.5],
        [7.5, 8, 97.5],
        [7.5, 9, 87.5],
        [7.5, 10, 77.5],
        [10.0, 0, 80.0],
        [10.0, 1, 70.0],
        [10.0, 2, 60.0],
        [10.0, 3, 50.0],
        [10.0, 4, 40.0],
        [10.0, 5, 30.0],
        [10.0, 6, 20.0],
        [10.0, 7, 10.0],
        [10.0, 8, 100.0],
        [10.0, 9, 90.0],
        [10.0, 10, 80.0],
    ]
)

MUNSELL_INTERPOLATION_METHODS: list = [
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Radial",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Radial",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Radial",
    "Radial",
    "Linear",
    "Radial",
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
    "Radial",
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
    "Radial",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
    "Radial",
    "Radial",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
    "Linear",
    "Radial",
]

MUNSELL_XY_FROM_RENOTATION_OVOID: list = [
    [0.1832, 0.4414],
    [0.2419, 0.3985],
    [0.3564, 0.3279],
    [0.1557, 0.3517],
    [0.3861, 0.3767],
    [0.3441, 0.1698],
    [0.5433, 0.3027],
    [0.1702, 0.2768],
    [0.1303, 0.1639],
    [0.3015, 0.3052],
    [0.2562, 0.2709],
    [0.3100, 0.2750],
    [0.1671, 0.4089],
    [0.2543, 0.3220],
    [0.2312, 0.2899],
    [0.3621, 0.3349],
    [0.1601, 0.2028],
    [0.0784, 0.5761],
    [0.2699, 0.3120],
    [0.2501, 0.3118],
    [0.2929, 0.3303],
    [0.3132, 0.2032],
    [0.2511, 0.2031],
    [0.3161, 0.2691],
    [0.3422, 0.3648],
    [0.3745, 0.4004],
    [0.2445, 0.3914],
    [0.2965, 0.3293],
    [0.3076, 0.2416],
    [0.3032, 0.5748],
    [0.2982, 0.3003],
    [0.3679, 0.3585],
    [0.3389, 0.3079],
    [0.0982, 0.2828],
    [0.2511, 0.4107],
    [0.2711, 0.3455],
    [0.3815, 0.5093],
    [0.2763, 0.3607],
    [0.3382, 0.2496],
    [0.4774, 0.2969],
    [0.2758, 0.2208],
    [0.0828, 0.3108],
    [0.3298, 0.2869],
    [0.2969, 0.0979],
    [0.4190, 0.4791],
    [0.3622, 0.3861],
    [0.3099, 0.2502],
    [0.2718, 0.1604],
    [0.3045, 0.1905],
    [0.2731, 0.1738],
    [0.3682, 0.2983],
    [0.2862, 0.2260],
    [0.4240, 0.3302],
    [0.2657, 0.2528],
    [0.2559, 0.1730],
    [0.3491, 0.2872],
    [0.4073, 0.2235],
    [0.0794, 0.7385],
    [0.4001, 0.3263],
    [0.3546, 0.5490],
    [0.3270, 0.4288],
    [0.3943, 0.4264],
    [0.5130, 0.1893],
    [0.3806, 0.3294],
    [0.2311, 0.2010],
    [0.2816, 0.6563],
    [0.3990, 0.2708],
    [0.5660, 0.3795],
    [0.4581, 0.2549],
    [0.3495, 0.3226],
    [0.1340, 0.6871],
    [0.4574, 0.5062],
    [0.0329, 0.7358],
    [0.3699, 0.3586],
    [0.2419, 0.3352],
    [0.3050, 0.2967],
    [0.3100, 0.4018],
    [0.3470, 0.2259],
    [0.4184, 0.4568],
    [0.1341, 0.6420],
    [0.5456, 0.4040],
    [0.2080, 0.2789],
    [0.5071, 0.3777],
    [0.4617, 0.2506],
    [0.4875, 0.3123],
    [0.2922, 0.1106],
    [0.2638, 0.2624],
    [0.0925, 0.4275],
    [0.1188, 0.6918],
    [0.0476, 0.3458],
    [0.3711, 0.1449],
    [0.2005, 0.5759],
    [0.1326, 0.2784],
    [0.2671, 0.2998],
    [0.3490, 0.2570],
    [0.3038, 0.1500],
    [0.5422, 0.4141],
    [0.3515, 0.3024],
    [0.3850, 0.4120],
    [0.4320, 0.3118],
]


class TestMunsellValuePriest1920:
    """
    Define :func:`colour.notation.munsell.munsell_value_Priest1920` definition
    unit tests methods.
    """

    def test_munsell_value_Priest1920(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Priest1920`
        definition.
        """

        np.testing.assert_allclose(
            munsell_value_Priest1920(12.23634268),
            3.498048410185314,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Priest1920(22.89399987),
            4.7847674833788947,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Priest1920(6.29022535),
            2.5080321668591092,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_value_Priest1920(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Priest1920`
        definition n-dimensional arrays support.
        """

        Y = 12.23634268
        V = munsell_value_Priest1920(Y)

        V = np.tile(V, 6)
        Y = np.tile(Y, 6)
        np.testing.assert_allclose(
            munsell_value_Priest1920(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3))
        Y = np.reshape(Y, (2, 3))
        np.testing.assert_allclose(
            munsell_value_Priest1920(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3, 1))
        Y = np.reshape(Y, (2, 3, 1))
        np.testing.assert_allclose(
            munsell_value_Priest1920(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

    def test_domain_range_scale_munsell_value_Priest1920(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Priest1920`
        definition domain and range scale support.
        """

        Y = 12.23634268
        V = munsell_value_Priest1920(Y)

        d_r = (("reference", 1, 1), ("1", 0.01, 0.1), ("100", 1, 10))
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_value_Priest1920(Y * factor_a),
                    V * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_value_Priest1920(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Priest1920`
        definition nan support.
        """

        munsell_value_Priest1920(np.array([-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]))


class TestMunsellValueMunsell1933:
    """
    Define :func:`colour.notation.munsell.munsell_value_Munsell1933`
    definition unit tests methods.
    """

    def test_munsell_value_Munsell1933(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Munsell1933`
        definition.
        """

        np.testing.assert_allclose(
            munsell_value_Munsell1933(12.23634268),
            4.1627702416858083,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Munsell1933(22.89399987),
            5.5914543020790592,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Munsell1933(6.29022535),
            3.0141971134091761,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_value_Munsell1933(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Munsell1933`
        definition n-dimensional arrays support.
        """

        Y = 12.23634268
        V = munsell_value_Munsell1933(Y)

        V = np.tile(V, 6)
        Y = np.tile(Y, 6)
        np.testing.assert_allclose(
            munsell_value_Munsell1933(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3))
        Y = np.reshape(Y, (2, 3))
        np.testing.assert_allclose(
            munsell_value_Munsell1933(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3, 1))
        Y = np.reshape(Y, (2, 3, 1))
        np.testing.assert_allclose(
            munsell_value_Munsell1933(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

    def test_domain_range_scale_munsell_value_Munsell1933(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Munsell1933`
        definition domain and range scale support.
        """

        Y = 12.23634268
        V = munsell_value_Munsell1933(Y)

        d_r = (("reference", 1, 1), ("1", 0.01, 0.1), ("100", 1, 10))
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_value_Munsell1933(Y * factor_a),
                    V * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_value_Munsell1933(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Munsell1933`
        definition nan support.
        """

        munsell_value_Munsell1933(np.array([-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]))


class TestMunsellValueMoon1943:
    """
    Define :func:`colour.notation.munsell.munsell_value_Moon1943` definition
    unit tests methods.
    """

    def test_munsell_value_Moon1943(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Moon1943`
        definition.
        """

        np.testing.assert_allclose(
            munsell_value_Moon1943(12.23634268),
            4.0688120634976421,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Moon1943(22.89399987),
            5.3133627855494412,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Moon1943(6.29022535),
            3.0645015037679695,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_value_Moon1943(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Moon1943`
        definition n-dimensional arrays support.
        """

        Y = 12.23634268
        V = munsell_value_Moon1943(Y)

        V = np.tile(V, 6)
        Y = np.tile(Y, 6)
        np.testing.assert_allclose(
            munsell_value_Moon1943(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3))
        Y = np.reshape(Y, (2, 3))
        np.testing.assert_allclose(
            munsell_value_Moon1943(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3, 1))
        Y = np.reshape(Y, (2, 3, 1))
        np.testing.assert_allclose(
            munsell_value_Moon1943(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

    def test_domain_range_scale_munsell_value_Moon1943(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Moon1943`
        definition domain and range scale support.
        """

        Y = 12.23634268
        V = munsell_value_Moon1943(Y)

        d_r = (("reference", 1, 1), ("1", 0.01, 0.1), ("100", 1, 10))
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_value_Moon1943(Y * factor_a),
                    V * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_value_Moon1943(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Moon1943`
        definition nan support.
        """

        munsell_value_Moon1943(np.array([-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]))


class TestMunsellValueSaunderson1944:
    """
    Define :func:`colour.notation.munsell.munsell_value_Saunderson1944`
    definition unit tests methods.
    """

    def test_munsell_value_Saunderson1944(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Saunderson1944`
        definition.
        """

        np.testing.assert_allclose(
            munsell_value_Saunderson1944(12.23634268),
            4.0444736723175119,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Saunderson1944(22.89399987),
            5.3783324022305923,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Saunderson1944(6.29022535),
            2.9089633927316823,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_value_Saunderson1944(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Saunderson1944`
        definition n-dimensional arrays support.
        """

        Y = 12.23634268
        V = munsell_value_Saunderson1944(Y)

        V = np.tile(V, 6)
        Y = np.tile(Y, 6)
        np.testing.assert_allclose(
            munsell_value_Saunderson1944(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3))
        Y = np.reshape(Y, (2, 3))
        np.testing.assert_allclose(
            munsell_value_Saunderson1944(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3, 1))
        Y = np.reshape(Y, (2, 3, 1))
        np.testing.assert_allclose(
            munsell_value_Saunderson1944(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

    def test_domain_range_scale_munsell_value_Saunderson1944(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Saunderson1944`
        definition domain and range scale support.
        """

        Y = 12.23634268
        V = munsell_value_Saunderson1944(Y)

        d_r = (("reference", 1, 1), ("1", 0.01, 0.1), ("100", 1, 10))
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_value_Saunderson1944(Y * factor_a),
                    V * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_value_Saunderson1944(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Saunderson1944`
        definition nan support.
        """

        munsell_value_Saunderson1944(
            np.array([-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan])
        )


class TestMunsellValueLadd1955:
    """
    Define :func:`colour.notation.munsell.munsell_value_Ladd1955` definition
    unit tests methods.
    """

    def test_munsell_value_Ladd1955(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Ladd1955`
        definition.
        """

        np.testing.assert_allclose(
            munsell_value_Ladd1955(12.23634268),
            4.0511633044287088,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Ladd1955(22.89399987),
            5.3718647913936772,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_Ladd1955(6.29022535),
            2.9198269939751613,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_value_Ladd1955(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Ladd1955`
        definition n-dimensional arrays support.
        """

        Y = 12.23634268
        V = munsell_value_Ladd1955(Y)

        V = np.tile(V, 6)
        Y = np.tile(Y, 6)
        np.testing.assert_allclose(
            munsell_value_Ladd1955(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3))
        Y = np.reshape(Y, (2, 3))
        np.testing.assert_allclose(
            munsell_value_Ladd1955(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3, 1))
        Y = np.reshape(Y, (2, 3, 1))
        np.testing.assert_allclose(
            munsell_value_Ladd1955(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

    def test_domain_range_scale_munsell_value_Ladd1955(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Ladd1955`
        definition domain and range scale support.
        """

        Y = 12.23634268
        V = munsell_value_Ladd1955(Y)

        d_r = (("reference", 1, 1), ("1", 0.01, 0.1), ("100", 1, 10))
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_value_Ladd1955(Y * factor_a),
                    V * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_value_Ladd1955(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_Ladd1955`
        definition nan support.
        """

        munsell_value_Ladd1955(np.array([-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]))


class TestMunsellValueMcCamy1992:
    """
    Define :func:`colour.notation.munsell.munsell_value_McCamy1987` definition
    unit tests methods.
    """

    def test_munsell_value_McCamy1987(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_McCamy1987`
        definition.
        """

        np.testing.assert_allclose(
            munsell_value_McCamy1987(12.23634268),
            4.081434853194113,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_McCamy1987(22.89399987),
            5.394083970919982,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_McCamy1987(6.29022535),
            2.9750160800320096,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_value_McCamy1987(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_McCamy1987`
        definition n-dimensional arrays support.
        """

        Y = 12.23634268
        V = munsell_value_McCamy1987(Y)

        V = np.tile(V, 6)
        Y = np.tile(Y, 6)
        np.testing.assert_allclose(
            munsell_value_McCamy1987(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3))
        Y = np.reshape(Y, (2, 3))
        np.testing.assert_allclose(
            munsell_value_McCamy1987(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3, 1))
        Y = np.reshape(Y, (2, 3, 1))
        np.testing.assert_allclose(
            munsell_value_McCamy1987(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

    def test_domain_range_scale_munsell_value_McCamy1987(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_McCamy1987`
        definition domain and range scale support.
        """

        Y = 12.23634268
        V = munsell_value_McCamy1987(Y)

        d_r = (("reference", 1, 1), ("1", 0.01, 0.1), ("100", 1, 10))
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_value_McCamy1987(Y * factor_a),
                    V * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_value_McCamy1987(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_McCamy1987`
        definition nan support.
        """

        munsell_value_McCamy1987(np.array([-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]))


class TestMunsellValueASTMD1535:
    """
    Define :func:`colour.notation.munsell.munsell_value_ASTMD1535`
    definition unit tests methods.
    """

    def test_munsell_value_ASTMD1535(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_ASTMD1535`
        definition.
        """

        np.testing.assert_allclose(
            munsell_value_ASTMD1535(12.23634268),
            4.0824437076525664,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_ASTMD1535(22.89399987),
            5.3913268228155395,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_value_ASTMD1535(6.29022535),
            2.9761930839606454,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_value_ASTMD1535(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_ASTMD1535`
        definition n-dimensional arrays support.
        """

        Y = 12.23634268
        V = munsell_value_ASTMD1535(Y)

        V = np.tile(V, 6)
        Y = np.tile(Y, 6)
        np.testing.assert_allclose(
            munsell_value_ASTMD1535(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3))
        Y = np.reshape(Y, (2, 3))
        np.testing.assert_allclose(
            munsell_value_ASTMD1535(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

        V = np.reshape(V, (2, 3, 1))
        Y = np.reshape(Y, (2, 3, 1))
        np.testing.assert_allclose(
            munsell_value_ASTMD1535(Y), V, atol=TOLERANCE_ABSOLUTE_TESTS
        )

    def test_domain_range_scale_munsell_value_ASTMD1535(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_ASTMD1535`
        definition domain and range scale support.
        """

        Y = 12.23634268
        V = munsell_value_ASTMD1535(Y)

        d_r = (("reference", 1, 1), ("1", 0.01, 0.1), ("100", 1, 10))
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_value_ASTMD1535(Y * factor_a),
                    V * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_value_ASTMD1535(self):
        """
        Test :func:`colour.notation.munsell.munsell_value_ASTMD1535`
        definition nan support.
        """

        munsell_value_ASTMD1535(np.array([-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]))


class TestMunsellSpecification_to_xyY:
    """
    Define :func:`colour.notation.munsell.munsell_specification_to_xyY`
    definition unit tests methods.
    """

    def test_munsell_specification_to_xyY(self):
        """
        Test :func:`colour.notation.munsell.munsell_specification_to_xyY`
        definition.
        """

        specification, xyY = (
            as_float_array(list(MUNSELL_SPECIFICATIONS[..., 0])),
            as_float_array(list(MUNSELL_SPECIFICATIONS[..., 1])),
        )
        np.testing.assert_allclose(
            munsell_specification_to_xyY(specification),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        specification, xyY = (
            as_float_array(list(MUNSELL_GREYS_SPECIFICATIONS[..., 0])),
            as_float_array(list(MUNSELL_GREYS_SPECIFICATIONS[..., 1])),
        )
        specification = np.squeeze(specification)
        nan_array = np.full(specification.shape, np.nan)
        specification = tstack([nan_array, specification, nan_array, nan_array])

        np.testing.assert_allclose(
            munsell_specification_to_xyY(specification),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_n_dimensional_munsell_specification_to_xyY(self):
        """
        Test :func:`colour.notation.munsell.munsell_specification_to_xyY`
        definition n-dimensional arrays support.
        """

        specification = np.array([7.18927191, 5.34025196, 16.05861170, 3.00000000])
        xyY = munsell_specification_to_xyY(specification)

        specification = np.tile(specification, (6, 1))
        xyY = np.tile(xyY, (6, 1))
        np.testing.assert_allclose(
            munsell_specification_to_xyY(specification),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        specification = np.reshape(specification, (2, 3, 4))
        xyY = np.reshape(xyY, (2, 3, 3))
        np.testing.assert_allclose(
            munsell_specification_to_xyY(specification),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        specification = np.array([np.nan, 8.9, np.nan, np.nan])
        xyY = munsell_specification_to_xyY(specification)

        specification = np.tile(specification, (6, 1))
        xyY = np.tile(xyY, (6, 1))
        np.testing.assert_allclose(
            munsell_specification_to_xyY(specification),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        specification = np.reshape(specification, (2, 3, 4))
        xyY = np.reshape(xyY, (2, 3, 3))
        np.testing.assert_allclose(
            munsell_specification_to_xyY(specification),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_domain_range_scale_munsell_specification_to_xyY(self):
        """
        Test :func:`colour.notation.munsell.munsell_specification_to_xyY`
        definition domain and range scale support.
        """

        specification = np.array([7.18927191, 5.34025196, 16.05861170, 3.00000000])
        xyY = munsell_specification_to_xyY(specification)

        d_r = (
            ("reference", 1, 1),
            ("1", np.array([0.1, 0.1, 1 / 50, 0.1]), 1),
            ("100", np.array([10, 10, 2, 10]), np.array([1, 1, 100])),
        )
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_specification_to_xyY(specification * factor_a),
                    xyY * factor_b,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    @ignore_numpy_errors
    def test_nan_munsell_specification_to_xyY(self):
        """
        Test :func:`colour.notation.munsell.munsell_specification_to_xyY`
        definition nan support.
        """

        cases = [-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]
        cases = np.array(list(set(product(cases, repeat=4))))
        for case in cases:
            with contextlib.suppress(AssertionError, TypeError, ValueError):
                munsell_specification_to_xyY(case)


class TestMunsellColour_to_xyY:
    """
    Define :func:`colour.notation.munsell.munsell_colour_to_xyY` definition
    unit tests methods.
    """

    def test_domain_range_scale_munsell_colour_to_xyY(self):
        """
        Test :func:`colour.notation.munsell.munsell_colour_to_xyY` definition
        domain and range scale support.
        """

        munsell_colour = "4.2YR 8.1/5.3"
        xyY = munsell_colour_to_xyY(munsell_colour)

        d_r = (
            ("reference", 1),
            ("1", 1),
            ("100", np.array([1, 1, 100])),
        )
        for scale, factor in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    munsell_colour_to_xyY(munsell_colour),
                    xyY * factor,
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )

    def test_n_dimensional_munsell_colour_to_xyY(self):
        """
        Test :func:`colour.notation.munsell.munsell_colour_to_xyY` definition
        n-dimensional arrays support.
        """

        munsell_colour = "4.2YR 8.1/5.3"
        xyY = munsell_colour_to_xyY(munsell_colour)

        munsell_colour = np.tile(munsell_colour, 6)
        xyY = np.tile(xyY, (6, 1))
        np.testing.assert_allclose(
            munsell_colour_to_xyY(munsell_colour),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        munsell_colour = np.reshape(munsell_colour, (2, 3))
        xyY = np.reshape(xyY, (2, 3, 3))
        np.testing.assert_allclose(
            munsell_colour_to_xyY(munsell_colour),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        munsell_colour = "N8.9"
        xyY = munsell_colour_to_xyY(munsell_colour)

        munsell_colour = np.tile(munsell_colour, 6)
        xyY = np.tile(xyY, (6, 1))
        np.testing.assert_allclose(
            munsell_colour_to_xyY(munsell_colour),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        munsell_colour = np.reshape(munsell_colour, (2, 3))
        xyY = np.reshape(xyY, (2, 3, 3))
        np.testing.assert_allclose(
            munsell_colour_to_xyY(munsell_colour),
            xyY,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )


class TestxyY_to_munsell_specification:
    """
    Define :func:`colour.notation.munsell.xyY_to_munsell_specification`
    definition unit tests methods.
    """

    def test_xyY_to_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.xyY_to_munsell_specification`
        definition.
        """

        specification, xyY = (
            as_float_array(list(MUNSELL_SPECIFICATIONS[..., 0])),
            as_float_array(list(MUNSELL_SPECIFICATIONS[..., 1])),
        )

        np.testing.assert_allclose(
            xyY_to_munsell_specification(xyY),
            specification,
            atol=5e-5,
        )

        specification, xyY = (
            as_float_array(list(MUNSELL_GREYS_SPECIFICATIONS[..., 0])),
            as_float_array(list(MUNSELL_GREYS_SPECIFICATIONS[..., 1])),
        )
        specification = np.squeeze(specification)
        nan_array = np.full(specification.shape, np.nan)
        specification = tstack([nan_array, specification, nan_array, nan_array])

        np.testing.assert_allclose(
            xyY_to_munsell_specification(xyY),
            specification,
            atol=0.00001,
        )

    def test_n_dimensional_xyY_to_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.xyY_to_munsell_specification`
        definition n-dimensional arrays support.
        """

        xyY = [0.16623068, 0.45684550, 0.22399519]
        specification = xyY_to_munsell_specification(xyY)

        xyY = np.tile(xyY, (6, 1))
        specification = np.tile(specification, (6, 1))
        np.testing.assert_allclose(
            xyY_to_munsell_specification(xyY),
            specification,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        xyY = np.reshape(xyY, (2, 3, 3))
        specification = np.reshape(specification, (2, 3, 4))
        np.testing.assert_allclose(
            xyY_to_munsell_specification(xyY),
            specification,
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_raise_exception_xyY_to_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.xyY_to_munsell_specification`
        definition raised exception.
        """

        pytest.raises(
            RuntimeError,
            xyY_to_munsell_specification,
            np.array([0.90615118, 0.57945103, 0.91984064]),
        )

    def test_domain_range_scale_xyY_to_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.xyY_to_munsell_specification`
        definition domain and range scale support.
        """

        xyY = [0.16623068, 0.45684550, 0.22399519]
        specification = xyY_to_munsell_specification(xyY)

        d_r = (
            ("reference", 1, 1),
            ("1", 1, np.array([0.1, 0.1, 1 / 50, 0.1])),
            ("100", np.array([1, 1, 100]), np.array([10, 10, 2, 10])),
        )
        for scale, factor_a, factor_b in d_r:
            with domain_range_scale(scale):
                np.testing.assert_allclose(
                    xyY_to_munsell_specification(xyY * factor_a),
                    specification * factor_b,
                    atol=2e-5,
                )

    @ignore_numpy_errors
    def test_nan_xyY_to_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.xyY_to_munsell_specification`
        definition nan support.
        """

        cases = [-1.0, 0.0, 1.0, -np.inf, np.inf, np.nan]
        cases = np.array(list(set(product(cases, repeat=3))))
        for case in cases:
            with contextlib.suppress(AssertionError, TypeError, ValueError):
                xyY_to_munsell_specification(case)


class TestxyY_to_munsell_colour:
    """
    Define :func:`colour.notation.munsell.xyY_to_munsell_colour` definition
    unit tests methods.
    """

    def test_domain_range_scale_xyY_to_munsell_colour(self):
        """
        Test :func:`colour.notation.munsell.xyY_to_munsell_colour` definition
        domain and range scale support.
        """

        xyY = np.array([0.38736945, 0.35751656, 0.59362000])
        munsell_colour = xyY_to_munsell_colour(xyY)

        d_r = (
            ("reference", 1),
            ("1", 1),
            ("100", np.array([1, 1, 100])),
        )
        for scale, factor in d_r:
            with domain_range_scale(scale):
                assert xyY_to_munsell_colour(xyY * factor) == munsell_colour

    def test_n_dimensional_xyY_to_munsell_colour(self):
        """
        Test :func:`colour.notation.munsell.xyY_to_munsell_colour` definition
        n-dimensional arrays support.
        """

        xyY = [0.16623068, 0.45684550, 0.22399519]
        munsell_colour = xyY_to_munsell_colour(xyY)

        xyY = np.tile(xyY, (6, 1))
        munsell_colour = np.tile(munsell_colour, 6)
        np.testing.assert_equal(xyY_to_munsell_colour(xyY), munsell_colour)

        xyY = np.reshape(xyY, (2, 3, 3))
        munsell_colour = np.reshape(munsell_colour, (2, 3))
        np.testing.assert_equal(xyY_to_munsell_colour(xyY), munsell_colour)

        xyY = [*list(CCS_ILLUMINANT_MUNSELL), 1.0]
        munsell_colour = xyY_to_munsell_colour(xyY)

        xyY = np.tile(xyY, (6, 1))
        munsell_colour = np.tile(munsell_colour, 6)
        np.testing.assert_equal(xyY_to_munsell_colour(xyY), munsell_colour)

        xyY = np.reshape(xyY, (2, 3, 3))
        munsell_colour = np.reshape(munsell_colour, (2, 3))
        np.testing.assert_equal(xyY_to_munsell_colour(xyY), munsell_colour)


class TestParseMunsellColour:
    """
    Define :func:`colour.notation.munsell.parse_munsell_colour` definition
    unit tests methods.
    """

    def test_parse_munsell_colour(self):
        """
        Test :func:`colour.notation.munsell.is_grey_munsell_colour`
        definition.
        """

        np.testing.assert_allclose(
            parse_munsell_colour("N5.2"),
            np.array([np.nan, 5.2, np.nan, np.nan]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            parse_munsell_colour("0YR 2.0/4.0"),
            np.array([0.0, 2.0, 4.0, 6]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            parse_munsell_colour("4.2YR 8.1/5.3"),
            np.array([4.2, 8.1, 5.3, 6]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

    def test_raise_exception_parse_munsell_colour(self):
        """
        Test :func:`colour.notation.munsell.is_grey_munsell_colour`
        definition raised exception.
        """

        pytest.raises(ValueError, parse_munsell_colour, "4.2YQ 8.1/5.3")


class TestIsGreyMunsellColour:
    """
    Define :func:`colour.notation.munsell.is_grey_munsell_colour` definition
    unit tests methods.
    """

    def test_is_grey_munsell_colour(self):
        """
        Test :func:`colour.notation.munsell.is_grey_munsell_colour`
        definition.
        """

        assert is_grey_munsell_colour(5.2)

        assert not is_grey_munsell_colour(np.array([0.0, 2.0, 4.0, 6]))

        assert not is_grey_munsell_colour(np.array([4.2, 8.1, 5.3, 6]))

        assert is_grey_munsell_colour(np.array([np.nan, 0.5, np.nan, np.nan]))


class TestNormaliseMunsellSpecification:
    """
    Define :func:`colour.notation.munsell.normalise_munsell_specification`
    definition unit tests methods.
    """

    def test_normalise_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.normalise_munsell_specification`
        definition.
        """

        np.testing.assert_allclose(
            normalise_munsell_specification((0.0, 2.0, 4.0, 6)),
            np.array([10.0, 2.0, 4.0, 7]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            normalise_munsell_specification((0.0, 2.0, 4.0, 8)),
            np.array([10.0, 2.0, 4.0, 9]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            normalise_munsell_specification((0, 2.0, 4.0, 10)),
            np.array([10.0, 2.0, 4.0, 1]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            normalise_munsell_specification(0.5),
            np.array([np.nan, 0.5, np.nan, np.nan]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )


class TestMunsellColourToMunsellSpecification:
    """
    Define :func:`colour.notation.munsell.\
munsell_colour_to_munsell_specification` definition unit tests methods.
    """

    def test_munsell_colour_to_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.\
munsell_colour_to_munsell_specification` definition.
        """

        np.testing.assert_allclose(
            munsell_colour_to_munsell_specification("0.0YR 2.0/4.0"),
            np.array([10.0, 2.0, 4.0, 7]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_colour_to_munsell_specification("0.0RP 2.0/4.0"),
            np.array([10.0, 2.0, 4.0, 9]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_colour_to_munsell_specification("10.0B 2.0/4.0"),
            np.array([10.0, 2.0, 4.0, 1]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_colour_to_munsell_specification("N5.2"),
            np.array([np.nan, 5.2, np.nan, np.nan]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            munsell_colour_to_munsell_specification("0.0YR 2.0/0.0"),
            np.array([np.nan, 2.0, np.nan, np.nan]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )


class TestMunsellSpecificationToMunsellColour:
    """
    Define :func:`colour.notation.munsell.\
munsell_specification_to_munsell_colour` definition unit tests methods.
    """

    def test_munsell_specification_to_munsell_colour(self):
        """
        Test :func:`colour.notation.munsell.\
munsell_specification_to_munsell_colour` definition.
        """

        assert (
            munsell_specification_to_munsell_colour(np.array([10.0, 2.0, 4.0, 7]))
            == "10.0R 2.0/4.0"
        )

        assert (
            munsell_specification_to_munsell_colour(np.array([10.0, 2.0, 4.0, 9]))
            == "10.0P 2.0/4.0"
        )

        assert (
            munsell_specification_to_munsell_colour(np.array([10.0, 2.0, 4.0, 1]))
            == "10.0B 2.0/4.0"
        )

        assert (
            munsell_specification_to_munsell_colour(
                np.array([np.nan, 5.2, np.nan, np.nan])
            )
            == "N5.2"
        )

        assert (
            munsell_specification_to_munsell_colour(np.array([0.0, 2.0, 4.0, 7]))
            == "10.0RP 2.0/4.0"
        )

        assert (
            munsell_specification_to_munsell_colour(np.array([10.0, 0.0, 4.0, 7]))
            == "N0.0"
        )


class Test_xyY_fromRenotation:
    """
    Define :func:`colour.notation.munsell.xyY_from_renotation` definition
    unit tests methods.
    """

    def test_xyY_from_renotation(self):
        """
        Test :func:`colour.notation.munsell.xyY_from_renotation`
        definition.
        """

        np.testing.assert_array_equal(
            xyY_from_renotation([2.5, 0.2, 2.0, 4]),
            np.array([0.713, 1.414, 0.237]),
        )

        np.testing.assert_array_equal(
            xyY_from_renotation([5.0, 0.2, 2.0, 4]),
            np.array([0.449, 1.145, 0.237]),
        )

        np.testing.assert_array_equal(
            xyY_from_renotation([7.5, 0.2, 2.0, 4]),
            np.array([0.262, 0.837, 0.237]),
        )


class TestIsSpecificationInRenotation:
    """
    Define :func:`colour.notation.munsell.is_specification_in_renotation`
    definition unit tests methods.
    """

    def test_is_specification_in_renotation(self):
        """
        Test :func:`colour.notation.munsell.is_specification_in_renotation`
        definition.
        """

        assert is_specification_in_renotation(np.array([2.5, 0.2, 2.0, 4]))

        assert is_specification_in_renotation(np.array([5.0, 0.2, 2.0, 4]))

        assert not is_specification_in_renotation(np.array([25.0, 0.2, 2.0, 4]))


class TestBoundingHuesFromRenotation:
    """
    Define :func:`colour.notation.munsell.bounding_hues_from_renotation`
    definition unit tests methods.
    """

    def test_bounding_hues_from_renotation(self):
        """
        Test :func:`colour.notation.munsell.bounding_hues_from_renotation`
        definition.
        """

        for i, (specification, _xyY) in enumerate(MUNSELL_SPECIFICATIONS):
            hue, _value, _chroma, code = specification
            np.testing.assert_array_equal(
                bounding_hues_from_renotation([hue, code]),
                MUNSELL_BOUNDING_HUES[i],
            )


class TestHueToHueAngle:
    """
    Define :func:`colour.notation.munsell.hue_to_hue_angle` definition unit
    tests methods.
    """

    def test_hue_to_hue_angle(self):
        """Test :func:`colour.notation.munsell.hue_to_hue_angle` definition."""

        for hue, code, angle in MUNSELL_HUE_TO_ANGLE:
            assert hue_to_hue_angle([hue, code]) == angle


class TestHueAngleToHue:
    """
    Define :func:`colour.notation.munsell.hue_angle_to_hue` definition unit
    tests methods.
    """

    def test_hue_angle_to_hue(self):
        """Test :func:`colour.notation.munsell.hue_angle_to_hue` definition."""

        for hue, code, angle in MUNSELL_HUE_TO_ANGLE:
            np.testing.assert_array_equal(hue_angle_to_hue(angle), (hue, code))


class TestHueTo_ASTM_hue:
    """
    Define :func:`colour.notation.munsell.hue_to_ASTM_hue` definition unit
    tests methods.
    """

    def test_hue_to_ASTM_hue(self):
        """Test :func:`colour.notation.munsell.hue_to_ASTM_hue` definition."""

        for hue, code, angle in MUNSELL_HUE_TO_ASTM_HUE:
            assert hue_to_ASTM_hue([hue, code]) == angle


class TestInterpolationMethodFromRenotationOvoid:
    """
    Define :func:`colour.notation.munsell.\
interpolation_method_from_renotation_ovoid` definition unit tests methods.
    """

    def test_interpolation_method_from_renotation_ovoid(self):
        """
        Test :func:`colour.notation.munsell.\
interpolation_method_from_renotation_ovoid` definition.
        """

        for i, (specification, _xyY) in enumerate(MUNSELL_EVEN_SPECIFICATIONS):
            assert (
                interpolation_method_from_renotation_ovoid(specification)
                == MUNSELL_INTERPOLATION_METHODS[i]
            )

        assert (
            interpolation_method_from_renotation_ovoid(
                np.array([np.nan, 5.2, np.nan, np.nan])
            )
            is None
        )

        assert (
            interpolation_method_from_renotation_ovoid(np.array([2.5, 10.0, 2.0, 4]))
            is None
        )


class Test_xy_fromRenotationOvoid:
    """
    Define :func:`colour.notation.munsell.xy_from_renotation_ovoid` definition
    unit tests methods.
    """

    def test_xy_from_renotation_ovoid(self):
        """
        Test :func:`colour.notation.munsell.xy_from_renotation_ovoid`
        definition.
        """

        for i, (specification, _xyY) in enumerate(MUNSELL_EVEN_SPECIFICATIONS):
            if is_specification_in_renotation(specification):
                np.testing.assert_allclose(
                    xy_from_renotation_ovoid(specification),
                    MUNSELL_XY_FROM_RENOTATION_OVOID[i],
                    atol=TOLERANCE_ABSOLUTE_TESTS,
                )


class TestLCHabToMunsellSpecification:
    """
    Define :func:`colour.notation.munsell.LCHab_to_munsell_specification`
    definition unit tests methods.
    """

    def test_LCHab_to_munsell_specification(self):
        """
        Test :func:`colour.notation.munsell.LCHab_to_munsell_specification`
        definition.
        """

        np.testing.assert_allclose(
            LCHab_to_munsell_specification(
                np.array([100.00000000, 21.57210357, 272.22819350])
            ),
            np.array([5.618942638888882, 10.0, 4.314420714000000, 10]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            LCHab_to_munsell_specification(
                np.array([100.00000000, 426.67945353, 72.39590835])
            ),
            np.array([0.109974541666666, 10.0, 85.335890706000001, 5]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            LCHab_to_munsell_specification(
                np.array([100.00000000, 74.05216981, 276.45318193])
            ),
            np.array([6.792550536111119, 10.0, 14.810433961999999, 10]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            LCHab_to_munsell_specification(
                np.array([100.00000000, 21.57210357, 0.00000000])
            ),
            np.array([10.000000000000000, 10.0, 4.314420714000000, 8]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )

        np.testing.assert_allclose(
            LCHab_to_munsell_specification(
                np.array([100.00000000, 21.57210357, 36.00000000])
            ),
            np.array([10.000000000000000, 10.0, 4.314420714000000, 7]),
            atol=TOLERANCE_ABSOLUTE_TESTS,
        )


class TestMaximumChromaFromRenotation:
    """
    Define :func:`colour.notation.munsell.maximum_chroma_from_renotation`
    definition unit tests methods.
    """

    def test_maximum_chroma_from_renotation(self):
        """
        Test :func:`colour.notation.munsell.maximum_chroma_from_renotation`
        definition.
        """

        assert maximum_chroma_from_renotation([2.5, 5, 5]) == 14.0

        assert maximum_chroma_from_renotation([8.675, 1.225, 10]) == 48.0

        assert maximum_chroma_from_renotation([6.875, 3.425, 1]) == 16.0


class TestMunsellSpecification_to_xy:
    """
    Define :func:`colour.notation.munsell.munsell_specification_to_xy`
    definition unit tests methods.
    """

    def test_munsell_specification_to_xy(self):
        """
        Test :func:`colour.notation.munsell.munsell_specification_to_xy`
        definition.
        """

        for specification, xyY in MUNSELL_EVEN_SPECIFICATIONS:
            np.testing.assert_allclose(
                munsell_specification_to_xy(specification),
                xyY[0:2],
                atol=TOLERANCE_ABSOLUTE_TESTS,
            )

        for specification, xyY in MUNSELL_GREYS_SPECIFICATIONS:
            np.testing.assert_allclose(
                munsell_specification_to_xy(specification[0]),
                xyY[0:2],
                atol=TOLERANCE_ABSOLUTE_TESTS,
            )
