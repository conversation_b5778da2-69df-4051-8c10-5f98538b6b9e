## ComfyUI-Manager: installing dependencies done.
[2025-07-28 19:52:42.057] ** ComfyUI startup time: 2025-07-28 19:52:42.057
[2025-07-28 19:52:42.057] ** Platform: Windows
[2025-07-28 19:52:42.057] ** Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-28 19:52:42.057] ** Python executable: D:\AI\koutu\python\python.exe
[2025-07-28 19:52:42.058] ** ComfyUI Path: D:\AI\koutu\ComfyUI
[2025-07-28 19:52:42.058] ** ComfyUI Base Folder Path: D:\AI\koutu\ComfyUI
[2025-07-28 19:52:42.058] ** User directory: D:\AI\koutu\ComfyUI\user
[2025-07-28 19:52:42.058] ** ComfyUI-Manager config path: D:\AI\koutu\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-28 19:52:42.058] ** Log path: D:\AI\koutu\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-28 19:52:45.707]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
[2025-07-28 19:52:45.707]    7.6 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager
[2025-07-28 19:52:45.707] 
[2025-07-28 19:53:01.438] Checkpoint files will always be loaded safely.
[2025-07-28 19:53:01.705] Total VRAM 24564 MB, total RAM 65149 MB
[2025-07-28 19:53:01.705] pytorch version: 2.5.1+cu124
[2025-07-28 19:53:04.868] xformers version: 0.0.28.post3
[2025-07-28 19:53:04.868] Set vram state to: NORMAL_VRAM
[2025-07-28 19:53:04.868] Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
[2025-07-28 19:53:05.617] Using xformers attention
[2025-07-28 19:53:07.171] Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-28 19:53:07.171] ComfyUI version: 0.3.45
[2025-07-28 19:53:07.385] ComfyUI frontend version: 1.23.4
[2025-07-28 19:53:07.387] [Prompt Server] web root: D:\AI\koutu\python\Lib\site-packages\comfyui_frontend_package\static
[2025-07-28 19:53:08.428] [AnimateDiffEvo] - [0;31mERROR[0m - No motion models found. Please download one and place in: ['D:\\AI\\koutu\\ComfyUI\\custom_nodes\\comfyui-animatediff-evolved\\models', 'D:\\AI\\koutu\\ComfyUI\\models\\animatediff_models']
[2025-07-28 19:53:08.802] [Crystools [0;32mINFO[0m] Crystools version: 1.22.1
[2025-07-28 19:53:08.823] [Crystools [0;32mINFO[0m] CPU: Intel(R) Core(TM) Ultra 9 285K - Arch: AMD64 - OS: Windows 10
[2025-07-28 19:53:08.846] [Crystools [0;32mINFO[0m] Pynvml (Nvidia) initialized.
[2025-07-28 19:53:08.846] [Crystools [0;32mINFO[0m] GPU/s:
[2025-07-28 19:53:08.862] [Crystools [0;32mINFO[0m] 0) NVIDIA GeForce RTX 4090 D
[2025-07-28 19:53:08.862] [Crystools [0;32mINFO[0m] NVIDIA Driver: 576.02
[2025-07-28 19:53:09.646] Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\__init__.py", line 1, in <module>
    from .nodes import NODE_CLASS_MAPPINGS as NODES_CLASS, NODE_DISPLAY_NAME_MAPPINGS as NODES_DISPLAY
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\nodes.py", line 10, in <module>
    from .hyvideo.text_encoder import TextEncoder
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\hyvideo\text_encoder\__init__.py", line 9, in <module>
    from .processing_llava import LlavaProcessor
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\hyvideo\text_encoder\processing_llava.py", line 23, in <module>
    from transformers.processing_utils import ProcessingKwargs, ProcessorMixin, Unpack, _validate_images_text_input_order
ImportError: cannot import name '_validate_images_text_input_order' from 'transformers.processing_utils' (D:\AI\koutu\python\Lib\site-packages\transformers\processing_utils.py)

[2025-07-28 19:53:09.646] Cannot import D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper module for custom nodes: cannot import name '_validate_images_text_input_order' from 'transformers.processing_utils' (D:\AI\koutu\python\Lib\site-packages\transformers\processing_utils.py)
[2025-07-28 19:53:09.649] ### Loading: ComfyUI-Impact-Pack (V8.8.5)
[2025-07-28 19:53:10.100] [Impact Pack] Wildcards loading done.
[2025-07-28 19:53:10.104] ### Loading: ComfyUI-Inspire-Pack (V1.14.1)
[2025-07-28 19:53:10.156] Total VRAM 24564 MB, total RAM 65149 MB
[2025-07-28 19:53:10.157] pytorch version: 2.5.1+cu124
[2025-07-28 19:53:10.157] xformers version: 0.0.28.post3
[2025-07-28 19:53:10.157] Set vram state to: NORMAL_VRAM
[2025-07-28 19:53:10.157] Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
[2025-07-28 19:53:10.289] ### Loading: ComfyUI-Manager (V3.31.1)
[2025-07-28 19:53:10.290] [ComfyUI-Manager] network_mode: public
[2025-07-28 19:53:10.742] ### ComfyUI Version: v0.3.45-25-ge6d9f627 | Released on '2025-07-27'
[2025-07-28 19:53:11.148] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-28 19:53:11.185] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-28 19:53:11.203] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-28 19:53:11.225] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-28 19:53:11.400] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-28 19:53:11.494] (pysssss:WD14Tagger) [DEBUG] Available ORT providers: TensorrtExecutionProvider, CUDAExecutionProvider, CPUExecutionProvider
[2025-07-28 19:53:11.494] (pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[2025-07-28 19:53:11.499] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-28 19:53:11.499] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-28 19:53:11.499] [36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-28 19:53:11.976] DWPose: Onnxruntime with acceleration providers detected
[2025-07-28 19:53:12.072] 
[36mEfficiency Nodes:[0m Attempting to add Control Net options to the 'HiRes-Fix Script' Node (comfyui_controlnet_aux add-on)...[92mSuccess![0m
[2025-07-28 19:53:14.546] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-28 19:53:14.546] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-28 19:53:15.152] FETCH ComfyRegistry Data: 5/92
[2025-07-28 19:53:16.290] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-28 19:53:16.291] 
	[3m[93m"Art is the journey of a free soul."[0m[3m - Alev Oguz[0m
[2025-07-28 19:53:16.291] 
[2025-07-28 19:53:16.318] 
[2025-07-28 19:53:16.318] [92m[rgthree-comfy] Loaded 42 fantastic nodes. 🎉[00m
[2025-07-28 19:53:16.318] 
[2025-07-28 19:53:16.323] 
Import times for custom nodes:
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\sd-dynamic-thresholding
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-WD14-Tagger
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\AIGODLIKE-COMFYUI-TRANSLATION
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_essentials
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_UltimateSDUpscale
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-custom-scripts
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-advanced-controlnet
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-animatediff-evolved
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Inspire-Pack
[2025-07-28 19:53:16.323]    0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-07-28 19:53:16.323]    0.1 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-KJNodes
[2025-07-28 19:53:16.323]    0.1 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-layerdiffuse
[2025-07-28 19:53:16.323]    0.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-crystools
[2025-07-28 19:53:16.323]    0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Impact-Pack
[2025-07-28 19:53:16.323]    0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-VideoHelperSuite
[2025-07-28 19:53:16.323]    0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-28 19:53:16.323]    0.7 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager
[2025-07-28 19:53:16.323]    0.8 seconds (IMPORT FAILED): D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper
[2025-07-28 19:53:16.323]    4.2 seconds: D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894
[2025-07-28 19:53:16.323] 
[2025-07-28 19:53:16.639] Context impl SQLiteImpl.
[2025-07-28 19:53:16.639] Will assume non-transactional DDL.
[2025-07-28 19:53:16.640] No target revision found.
[2025-07-28 19:53:16.665] Starting server

[2025-07-28 19:53:16.665] To see the GUI go to: http://127.0.0.1:8188
[2025-07-28 19:53:19.282] FETCH ComfyRegistry Data: 10/92
[2025-07-28 19:53:23.329] got prompt
[2025-07-28 19:53:23.416] FETCH ComfyRegistry Data: 15/92
[2025-07-28 19:53:23.692] Cache check: Missing model files: birefnet.py
[2025-07-28 19:53:23.692] Downloading required model files...
[2025-07-28 19:53:23.692] Downloading RMBG-2.0 model files...
[2025-07-28 19:53:23.693] Downloading config.json...
[2025-07-28 19:53:23.694] D:\AI\koutu\python\Lib\site-packages\huggingface_hub\file_download.py:980: UserWarning: `local_dir_use_symlinks` parameter is deprecated and will be ignored. The process to download files to a local folder has been updated and do not rely on symlinks anymore. You only need to pass a destination folder as`local_dir`.
For more details, check out https://huggingface.co/docs/huggingface_hub/main/en/guides/download#download-files-to-local-folder.
  warnings.warn(
[2025-07-28 19:53:23.696] Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: config.json))
[2025-07-28 19:53:23.696] Downloading model.safetensors...
[2025-07-28 19:53:23.698] Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: model.safetensors))
[2025-07-28 19:53:23.698] Downloading birefnet.py...
[2025-07-28 19:53:24.432] Downloading BiRefNet_config.py...
[2025-07-28 19:53:24.434] Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: BiRefNet_config.py))
[2025-07-28 19:53:24.434] Model files downloaded successfully
[2025-07-28 19:53:24.473] [RMBG ERROR] Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
[2025-07-28 19:53:24.473] [RMBG ERROR] Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
[2025-07-28 19:53:24.486] !!! Exception during processing !!! Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
[2025-07-28 19:53:24.488] Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 177, in process_image
    self.load_model(model_name)
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 161, in load_model
    self.model = AutoModelForImageSegmentation.from_pretrained(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\auto_factory.py", line 547, in from_pretrained
    config, kwargs = AutoConfig.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\configuration_auto.py", line 1262, in from_pretrained
    return config_class.from_pretrained(pretrained_model_name_or_path, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 627, in from_pretrained
    return cls.from_dict(config_dict, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 789, in from_dict
    config = cls(**config_dict)
             ^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\.cache\huggingface\modules\transformers_modules\RMBG-2.0\BiRefNet_config.py", line 13, in __init__
    super().__init__(**kwargs)
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 284, in __init__
    self.tie_word_embeddings = tie_word_embeddings
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 206, in __setattr__
    super().__setattr__(key, value)
AttributeError: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 516, in process_image
    mask = model_instance.process_image(img, model, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 220, in process_image
    handle_model_error(f"Error in batch processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 88, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "D:\AI\koutu\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 584, in process_image
    handle_model_error(f"Error in image processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 88, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

[2025-07-28 19:53:24.490] Prompt executed in 1.16 seconds
[2025-07-28 19:53:27.510] FETCH ComfyRegistry Data: 20/92
[2025-07-28 19:53:31.806] FETCH ComfyRegistry Data: 25/92
[2025-07-28 19:53:33.046] Cannot connect to comfyregistry.
[2025-07-28 19:53:33.076] nightly_channel: 
[2025-07-28 19:53:33.076] https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-07-28 19:53:33.076] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-28 19:53:33.262] [ComfyUI-Manager] All startup tasks have been completed.
