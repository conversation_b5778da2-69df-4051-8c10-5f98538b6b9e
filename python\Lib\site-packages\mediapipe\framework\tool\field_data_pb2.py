# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/tool/field_data.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)mediapipe/framework/tool/field_data.proto\x12\tmediapipe\".\n\x0bMessageData\x12\x10\n\x08type_url\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x0c\"\x96\x02\n\tFieldData\x12\x15\n\x0bint32_value\x18\x01 \x01(\x11H\x00\x12\x15\n\x0bint64_value\x18\x02 \x01(\x12H\x00\x12\x16\n\x0cuint32_value\x18\x03 \x01(\rH\x00\x12\x16\n\x0cuint64_value\x18\x04 \x01(\x04H\x00\x12\x16\n\x0c\x64ouble_value\x18\x05 \x01(\x01H\x00\x12\x15\n\x0b\x66loat_value\x18\x06 \x01(\x02H\x00\x12\x14\n\nbool_value\x18\x07 \x01(\x08H\x00\x12\x14\n\nenum_value\x18\x08 \x01(\x11H\x00\x12\x16\n\x0cstring_value\x18\t \x01(\tH\x00\x12/\n\rmessage_value\x18\n \x01(\x0b\x32\x16.mediapipe.MessageDataH\x00\x42\x07\n\x05value')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.tool.field_data_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_MESSAGEDATA']._serialized_start=56
  _globals['_MESSAGEDATA']._serialized_end=102
  _globals['_FIELDDATA']._serialized_start=105
  _globals['_FIELDDATA']._serialized_end=383
# @@protoc_insertion_point(module_scope)
