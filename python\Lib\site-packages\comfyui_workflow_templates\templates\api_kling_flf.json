{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 46, "last_link_id": 29, "nodes": [{"id": 38, "type": "SaveVideo", "pos": [1977.3602294921875, 1339.8397216796875], "size": [502.320068359375, 403.54498291015625], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 29}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {}}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 44, "type": "<PERSON>downNote", "pos": [800.3977661132812, 1158.9505615234375], "size": [371.3343811035156, 255.60939025878906], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "LoadImage", "pos": [1190, 1160], "size": [310, 326], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [27]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {}}, "widgets_values": ["first_frame.png", "image"]}, {"id": 42, "type": "LoadImage", "pos": [1190, 1530], "size": [315, 314], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [28]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"image": true, "upload": true}}, "widgets_values": ["last_frame.png", "image"]}, {"id": 46, "type": "KlingStartEndFrameNode", "pos": [1546.9512939453125, 1343.6197509765625], "size": [400, 254], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "start_frame", "type": "IMAGE", "link": 27}, {"name": "end_frame", "type": "IMAGE", "link": 28}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [29]}, {"name": "video_id", "type": "STRING", "links": null}, {"name": "duration", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "KlingStartEndFrameNode"}, "widgets_values": ["Time-lapse video recording, rotating around", "low quality, blurry, distorted, unnatural pose, disproportionate dragon body, Western dragon design, golden sparkles, colorful elements, orange light particles, multicolored tones, stiff movement, straight flying, simplistic clouds, flat effects, lack of details, opaque dragon body, rough texture, messy composition, overexposed, low resolution, long shot, stationary dragon", 1, "16:9", "pro mode / 5s duration / kling-v1-6"]}], "links": [[27, 40, 0, 46, 0, "IMAGE"], [28, 42, 0, 46, 1, "IMAGE"], [29, 46, 0, 38, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.9090909090909091, "offset": [-533.101390398026, -1038.8385687661378]}, "frontendVersion": "1.19.1", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}