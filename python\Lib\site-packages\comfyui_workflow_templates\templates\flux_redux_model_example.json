{"id": "06010f12-03bc-41ce-86bd-14f321d5a152", "revision": 0, "last_node_id": 48, "last_link_id": 131, "nodes": [{"id": 34, "type": "PrimitiveNode", "pos": [-150, 260], "size": [210, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "width"}, "slot_index": 0, "links": [112, 115]}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1024, "fixed"]}, {"id": 35, "type": "PrimitiveNode", "pos": [-150, 400], "size": [210, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "height"}, "slot_index": 0, "links": [113, 114]}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [1024, "fixed"]}, {"id": 27, "type": "EmptySD3LatentImage", "pos": [130, 320], "size": [210, 110], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 112}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 113}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 30, "type": "ModelSamplingFlux", "pos": [700, 230], "size": [315, 130], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 56}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 115}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 114}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [54, 55]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1024, 1024]}, {"id": 16, "type": "KSamplerSelect", "pos": [1040, 40], "size": [220, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [19]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 22, "type": "BasicGuider", "pos": [1040, 150], "size": [222.35000610351562, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 54}, {"name": "conditioning", "type": "CONDITIONING", "link": 130}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [30]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 17, "type": "BasicScheduler", "pos": [1040, 250], "size": [220, 106], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 55}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [20]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 25, "type": "RandomNoise", "pos": [700, 100], "size": [270, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [37]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "RandomNoise"}, "widgets_values": [958831004022715, "randomize"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [220, -10], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 10}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [41]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cute anime girl with massive fluffy fennec ears"], "color": "#232", "bgcolor": "#353"}, {"id": 41, "type": "StyleModelApply", "pos": [710, -340], "size": [320, 122], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 122}, {"name": "style_model", "type": "STYLE_MODEL", "link": 119}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 120}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [129]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 26, "type": "FluxGuidance", "pos": [710, -180], "size": [317.3999938964844, 58], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 41}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [122]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 39, "type": "CLIPVisionEncode", "pos": [710, -470], "size": [320, 80], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 117}, {"name": "image", "type": "IMAGE", "link": 118}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [120]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 45, "type": "StyleModelApply", "pos": [1170, -350], "size": [300, 122], "flags": {}, "order": 22, "mode": 4, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 129}, {"name": "style_model", "type": "STYLE_MODEL", "link": 125}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 126}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [130]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 46, "type": "CLIPVisionEncode", "pos": [1170, -470], "size": [300, 80], "flags": {}, "order": 18, "mode": 4, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 127}, {"name": "image", "type": "IMAGE", "link": 131}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [126]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 12, "type": "UNETLoader", "pos": [-150, -220], "size": [311.82000732421875, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [56]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "UNETLoader", "models": [{"name": "flux1-dev.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-dev/resolve/main/flux1-dev.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 11, "type": "DualCLIPLoader", "pos": [-150, -90], "size": [311.82000732421875, 130], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [10]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "DualCLIPLoader", "models": [{"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors", "directory": "text_encoders"}, {"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 10, "type": "VAELoader", "pos": [-150, 80], "size": [311.82000732421875, 60.43000030517578], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "StyleModelLoader", "pos": [-150, -430], "size": [311.82000732421875, 60], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [119, 125]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "StyleModelLoader", "models": [{"name": "flux1-redux-dev.safetensors", "url": "https://huggingface.co/Comfy-Org/Flux1-Redux-Dev/resolve/main/flux1-redux-dev.safetensors", "directory": "style_models"}]}, "widgets_values": ["flux1-redux-dev.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPVisionLoader", "pos": [-150, -320], "size": [311.82000732421875, 60], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [117, 127]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPVisionLoader", "models": [{"name": "sigclip_vision_patch14_384.safetensors", "url": "https://huggingface.co/Comfy-Org/sigclip_vision_384/resolve/main/sigclip_vision_patch14_384.safetensors?download=true", "directory": "clip_vision"}]}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 37, "type": "Note", "pos": [700, 400], "size": [315, 117.9800033569336], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["The reference sampling implementation auto adjusts the shift value based on the resolution, if you don't want this you can just bypass (CTRL-B) this ModelSamplingFlux node.\n"], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "LoadImage", "pos": [710, -920], "size": [315, 314], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [118]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LoadImage"}, "widgets_values": ["sd3_controlnet_example.png", "image"]}, {"id": 47, "type": "LoadImage", "pos": [1070, -920], "size": [310, 310], "flags": {}, "order": 11, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [131]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LoadImage"}, "widgets_values": ["sd3_controlnet_example.png", "image"]}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [1310, 110], "size": [220, 110], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 37}, {"name": "guider", "type": "GUIDER", "link": 30}, {"name": "sampler", "type": "SAMPLER", "link": 19}, {"name": "sigmas", "type": "SIGMAS", "link": 20}, {"name": "latent_image", "type": "LATENT", "link": 116}], "outputs": [{"name": "output", "type": "LATENT", "slot_index": 0, "links": [24]}, {"name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 8, "type": "VAEDecode", "pos": [1590, -120], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 24}, {"name": "vae", "type": "VAE", "link": 12}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1590, -10], "size": [540, 540], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}, {"id": 48, "type": "<PERSON>downNote", "pos": [-740, -480], "size": [560, 540], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorial](https://comfyanonymous.github.io/ComfyUI_examples/flux/#redux)\n\n## Model Links\n\n**Diffusion model**\n\n- [flux1-dev.safetensors](https://huggingface.co/Comfy-Org/flux1-dev/resolve/main/flux1-dev.safetensors)\n\n**Text encoder**\n\n- [clip_l.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors)\n- [t5xxl_fp16.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors) or [t5xxl_fp8_e4m3fn.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn.safetensors)\n\n**CLIP Vision**\n\n- [sigclip_vision_patch14_384.safetensors](https://huggingface.co/Comfy-Org/sigclip_vision_384/resolve/main/sigclip_vision_patch14_384.safetensors)\n\n**Style model**\n\n- [flux1-redux-dev.safetensors](https://huggingface.co/Comfy-Org/Flux1-Redux-Dev/resolve/main/flux1-redux-dev.safetensors)\n\n**VAE**\n- [ae.safetensors](https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors)\n\nTip: You can set the weight_dtype above to one of the fp8 types if you have memory issues.\n\n**Models save location**\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   └─── flux1-dev.safetensors\n│   ├── style_models/\n│   │   └─── flux1-redux-dev.safetensors\n│   ├── text_encoders/\n│   │   ├── clip_l.safetensors\n│   │   └─── t5xxl_fp16.safetensors or t5xxl_fp8_e4m3fn.safetensors\n│   ├── clip_vision/\n│   │   └── sigclip_vision_patch14_384.safetensors\n│   └── vae/\n│       └── ae.safetensors\n```\n\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 43, "type": "Note", "pos": [1590, -310], "size": [320, 140], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The redux model lets you prompt with images. It can be used with any Flux1 dev or schnell model workflow.\n\nYou can chain multiple \"Apply Style Model\" nodes if you want to mix multiple images together."], "color": "#432", "bgcolor": "#653"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [10, 11, 0, 6, 0, "CLIP"], [12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [41, 6, 0, 26, 0, "CONDITIONING"], [54, 30, 0, 22, 0, "MODEL"], [55, 30, 0, 17, 0, "MODEL"], [56, 12, 0, 30, 0, "MODEL"], [112, 34, 0, 27, 0, "INT"], [113, 35, 0, 27, 1, "INT"], [114, 35, 0, 30, 2, "INT"], [115, 34, 0, 30, 1, "INT"], [116, 27, 0, 13, 4, "LATENT"], [117, 38, 0, 39, 0, "CLIP_VISION"], [118, 40, 0, 39, 1, "IMAGE"], [119, 42, 0, 41, 1, "STYLE_MODEL"], [120, 39, 0, 41, 2, "CLIP_VISION_OUTPUT"], [122, 26, 0, 41, 0, "CONDITIONING"], [125, 42, 0, 45, 1, "STYLE_MODEL"], [126, 46, 0, 45, 2, "CLIP_VISION_OUTPUT"], [127, 38, 0, 46, 0, "CLIP_VISION"], [129, 41, 0, 45, 0, "CONDITIONING"], [130, 45, 0, 22, 1, "CONDITIONING"], [131, 47, 0, 46, 1, "IMAGE"]], "groups": [{"id": 1, "title": "Step3 - Upload reference image", "bounding": [680, -1000, 870, 420], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Custom sampler", "bounding": [680, -80, 880, 610], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step1 - Load models", "bounding": [-160, -510, 350, 680], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Image size", "bounding": [-160, 190, 550, 310], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Apply Redux model", "bounding": [680, -560, 870, 460], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Step2 - Prompt", "bounding": [210, -80, 442.8499755859375, 247.91000366210938], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5644739300537773, "offset": [1186.6527775485154, 1301.57278504358]}, "groupNodes": {}, "frontendVersion": "1.23.4"}, "version": 0.4}