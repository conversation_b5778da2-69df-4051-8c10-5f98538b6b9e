
このノードは、4倍のアップスケールプロセスを通じて画像の解像度を向上させることに特化しており、出力を洗練するためにコンディショニング要素を組み込んでいます。拡散技術を活用して画像をアップスケールし、スケール比率とノイズ増強の調整を可能にして、プロセスを微調整します。

## 入力

| パラメータ            | Comfy dtype        | 説明 |
|----------------------|--------------------|-------------|
| `images`             | `IMAGE`            | アップスケールされる入力画像。このパラメータは、出力画像の品質と解像度に直接影響を与えるため、重要です。 |
| `positive`           | `CONDITIONING`     | アップスケールプロセスを望ましい属性や特徴に導くポジティブなコンディショニング要素。 |
| `negative`           | `CONDITIONING`     | アップスケールプロセスが避けるべきネガティブなコンディショニング要素で、望ましくない属性や特徴から出力を遠ざけるのに役立ちます。 |
| `scale_ratio`        | `FLOAT`            | 画像の解像度が増加する係数を決定します。スケール比率が高いほど、より大きな出力画像が得られ、詳細と明瞭さが向上します。 |
| `noise_augmentation` | `FLOAT`            | アップスケールプロセス中に適用されるノイズ増強のレベルを制御します。これにより、変動を導入し、出力画像の堅牢性を向上させることができます。 |

## 出力

| パラメータ     | データ型 | 説明 |
|---------------|--------------|-------------|
| `positive`    | `CONDITIONING` | アップスケールプロセスから得られる洗練されたポジティブなコンディショニング要素。 |
| `negative`    | `CONDITIONING` | アップスケールプロセスから得られる洗練されたネガティブなコンディショニング要素。 |
| `latent`      | `LATENT`     | アップスケールプロセス中に生成される潜在表現で、さらなる処理やモデルトレーニングに利用できます。 |
