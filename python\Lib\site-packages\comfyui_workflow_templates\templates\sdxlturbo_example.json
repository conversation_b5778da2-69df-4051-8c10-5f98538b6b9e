{"last_node_id": 28, "last_link_id": 54, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [352, 176], "size": [425.28, 180.61], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 39}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [20], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 20, "type": "CheckpointLoaderSimple", "pos": [-17, -70], "size": [343.7, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [41, 45], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "shape": 3, "links": [38, 39], "slot_index": 1}, {"name": "VAE", "type": "VAE", "shape": 3, "links": [40], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "sd_xl_turbo_1.0_fp16.safetensors", "url": "https://huggingface.co/stabilityai/sdxl-turbo/resolve/main/sd_xl_turbo_1.0_fp16.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["sd_xl_turbo_1.0_fp16.safetensors"]}, {"id": 14, "type": "KSamplerSelect", "pos": [452, -144], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "shape": 3, "links": [18]}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler_ancestral"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [462, 398], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [23], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": 8, "type": "VAEDecode", "pos": [1183, -66], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 28}, {"name": "vae", "type": "VAE", "link": 40, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [53, 54], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 25, "type": "PreviewImage", "pos": [1213, 93], "size": [501.7, 541.92], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 53}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 22, "type": "SDTurboScheduler", "pos": [452, -248], "size": [315, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 45, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "shape": 3, "links": [49], "slot_index": 0}], "properties": {"Node name for S&R": "SDTurboScheduler"}, "widgets_values": [1, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [351, -45], "size": [422.85, 164.31], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 38, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [19], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful landscape scenery glass bottle with a galaxy inside cute fennec fox snow HDR sunset"]}, {"id": 27, "type": "SaveImage", "pos": [1843, -154], "size": [466.79, 516.83], "flags": {}, "order": 10, "mode": 2, "inputs": [{"name": "images", "type": "IMAGE", "link": 54}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 13, "type": "SamplerCustom", "pos": [800, -66], "size": [355.2, 230], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 41, "slot_index": 0}, {"name": "positive", "type": "CONDITIONING", "link": 19, "slot_index": 1}, {"name": "negative", "type": "CONDITIONING", "link": 20}, {"name": "sampler", "type": "SAMPLER", "link": 18, "slot_index": 3}, {"name": "sigmas", "type": "SIGMAS", "link": 49, "slot_index": 4}, {"name": "latent_image", "type": "LATENT", "link": 23, "slot_index": 5}], "outputs": [{"name": "output", "type": "LATENT", "shape": 3, "links": [28], "slot_index": 0}, {"name": "denoised_output", "type": "LATENT", "shape": 3, "links": null}], "properties": {"Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 0, "fixed", 1]}, {"id": 28, "type": "<PERSON>downNote", "pos": [-15, 90], "size": [225, 60], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/sdturbo/)"], "color": "#432", "bgcolor": "#653"}], "links": [[18, 14, 0, 13, 3, "SAMPLER"], [19, 6, 0, 13, 1, "CONDITIONING"], [20, 7, 0, 13, 2, "CONDITIONING"], [23, 5, 0, 13, 5, "LATENT"], [28, 13, 0, 8, 0, "LATENT"], [38, 20, 1, 6, 0, "CLIP"], [39, 20, 1, 7, 0, "CLIP"], [40, 20, 2, 8, 1, "VAE"], [41, 20, 0, 13, 0, "MODEL"], [45, 20, 0, 22, 0, "MODEL"], [49, 22, 0, 13, 4, "SIGMAS"], [53, 8, 0, 25, 0, "IMAGE"], [54, 8, 0, 27, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Unmute (CTRL-M) if you want to save images.", "bounding": [1815, -255, 536, 676], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.02, "offset": [311.24, 325.56]}}, "version": 0.4}