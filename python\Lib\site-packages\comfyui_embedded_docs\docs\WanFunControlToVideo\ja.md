このノードは、AlibabaのWan Fun Controlモデルをサポートするために追加され、[このコミット](https://github.com/comfyanonymous/ComfyUI/commit/3661c833bcc41b788a7c9f0e7bc48524f8ee5f82)の後に追加されました。

- **目的:** Wan 2.1 Fun Controlモデルを使用して、ビデオ生成に必要な条件情報を準備します。

WanFunControlToVideoノードは、ビデオ生成のためのWan Fun Controlモデルをサポートするように設計されたComfyUIの追加機能であり、ビデオ作成のためにWanFun制御を利用することを目的としています。

このノードは、重要な条件情報の準備ポイントとして機能し、潜在空間の中心点を初期化し、Wan 2.1 Funモデルを使用して次のビデオ生成プロセスをガイドします。ノードの名前はその機能を明確に示しており、さまざまな入力を受け入れ、それらをWanFunフレームワーク内でビデオ生成を制御するための適切な形式に変換します。

ノードのComfyUIノード階層内での位置は、ビデオ生成パイプラインの初期段階で動作し、実際のサンプリングやビデオフレームのデコードの前に条件信号を操作することに焦点を当てていることを示しています。

## 入力

| パラメータ名      | 必須 | データ型           | 説明                                                  | デフォルト値 |
|:-------------------|:---------|:-------------------|:-------------------------------------------------------------|:-------------|
| positive           | はい      | CONDITIONING       | 標準のComfyUIのポジティブ条件データで、通常は「CLIP Text Encode」ノードから取得されます。ポジティブプロンプトは、生成されるビデオの内容、主題、芸術スタイルをユーザーが想像するものを説明します。 | N/A  |
| negative           | はい      | CONDITIONING       | 標準のComfyUIのネガティブ条件データで、通常は「CLIP Text Encode」ノードによって生成されます。ネガティブプロンプトは、ユーザーが生成されるビデオで避けたい要素、スタイル、またはアーティファクトを指定します。 | N/A  |
| vae                | はい      | VAE                | Wan 2.1 Funモデルファミリーと互換性のあるVAE（変分オートエンコーダ）モデルが必要で、画像/ビデオデータのエンコードとデコードに使用されます。 | N/A  |
| width              | はい      | INT                | 出力ビデオフレームの希望幅（ピクセル単位）で、デフォルト値は832、最小値は16、最大値はnodes.MAX_RESOLUTIONによって決定され、ステップサイズは16です。 | 832  |
| height             | はい      | INT                | 出力ビデオフレームの希望高さ（ピクセル単位）で、デフォルト値は480、最小値は16、最大値はnodes.MAX_RESOLUTIONによって決定され、ステップサイズは16です。 | 480  |
| length             | はい      | INT                | 生成されるビデオの総フレーム数で、デフォルト値は81、最小値は1、最大値はnodes.MAX_RESOLUTIONによって決定され、ステップサイズは4です。 | 81   |
| batch_size         | はい      | INT                | 一度に生成されるビデオの数で、デフォルト値は1、最小値は1、最大値は4096です。 | 1    |
| clip_vision_output | いいえ       | CLIP_VISION_OUTPUT | （オプション）CLIPビジョンモデルによって抽出された視覚的特徴で、視覚スタイルとコンテンツのガイダンスを可能にします。 | なし |
| start_image        | いいえ       | IMAGE              | （オプション）生成されるビデオの開始に影響を与える初期画像です。 | なし |
| control_video      | いいえ       | IMAGE              | （オプション）ユーザーが生成されるビデオの動きと潜在的な構造をガイドするために提供する前処理されたControlNet参照ビデオを提供できるようにします。 | なし |

## 出力

| パラメータ名      | データ型           | 説明                                                  |
|:-------------------|:-------------------|:-------------------------------------------------------------|
| positive           | CONDITIONING       | エンコードされたstart_imageとcontrol_videoを含む、強化されたポジティブ条件データを提供します。 |
| negative           | CONDITIONING       | 同様に強化されたネガティブ条件データを提供し、同じconcat_latent_imageを含みます。 |
| latent             | LATENT             | キー「samples」を持つ空の潜在テンソルを含む辞書です。 |
