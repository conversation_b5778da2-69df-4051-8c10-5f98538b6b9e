이 노드는 새로운 ComfyUI 버전에서 Apply ControlNet으로 이름이 변경되었으며, 이전 버전인 Apply ControlNet (OLD)를 대체했습니다. 이전의 Apply ControlNet (OLD)는 현재 어느 정도 활성화된 상태와 유사하므로, 이 노드에 대한 최신 문서는 `Apply ControlNet`로 이동되었습니다.

이 노드는 이미지와 ControlNet 모델을 기반으로 조건 데이터에 고급 ControlNet 변환을 적용합니다. 생성된 콘텐츠에 대한 ControlNet의 영향을 세밀하게 조정할 수 있어, 조건에 대한 보다 정밀하고 다양한 수정이 가능합니다.

## 입력

| 매개변수        | 데이터 유형    | 설명                                                                                                                                                                                                                      |
| --------------- | -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `positive`      | `CONDITIONING` | ControlNet 변환이 적용될 긍정적 조건 데이터입니다. 생성된 콘텐츠에서 강화하거나 유지하고자 하는 속성이나 특징을 나타냅니다.                                                                                               |
| `negative`      | `CONDITIONING` | 생성된 콘텐츠에서 감소시키거나 제거하고자 하는 속성이나 특징을 나타내는 부정적 조건 데이터입니다. ControlNet 변환은 이 데이터에도 적용되어 콘텐츠의 특성을 균형 있게 조정할 수 있습니다.                                  |
| `control_net`   | `CONTROL_NET`  | 조건 데이터에 대한 특정 조정 및 향상을 정의하는 데 중요한 ControlNet 모델입니다. 참조 이미지와 강도 매개변수를 해석하여 변환을 적용하며, 긍정적 및 부정적 조건 데이터의 속성을 수정하여 최종 출력에 크게 영향을 미칩니다. |
| `image`         | `IMAGE`        | ControlNet 변환의 참조로 사용되는 이미지입니다. ControlNet이 조건 데이터에 가하는 조정에 영향을 미치며, 특정 특징의 강화 또는 억제를 안내합니다.                                                                          |
| `strength`      | `FLOAT`        | 조건 데이터에 대한 ControlNet의 영향 강도를 결정하는 스칼라 값입니다. 값이 높을수록 더 두드러진 조정이 이루어집니다.                                                                                                      |
| `start_percent` | `FLOAT`        | ControlNet 효과의 시작 백분율로, 지정된 범위에 걸쳐 변환을 점진적으로 적용할 수 있습니다.                                                                                                                                 |
| `end_percent`   | `FLOAT`        | 변환이 적용되는 범위를 정의하는 ControlNet 효과의 종료 백분율입니다. 이를 통해 조정 과정을 보다 세밀하게 제어할 수 있습니다.                                                                                              |

## 출력

| 매개변수   | 데이터 유형    | 설명                                                                                                                        |
| ---------- | -------------- | --------------------------------------------------------------------------------------------------------------------------- |
| `positive` | `CONDITIONING` | 입력 매개변수를 기반으로 한 ControlNet 변환 적용 후의 수정된 긍정적 조건 데이터로, 강화된 내용을 반영합니다.                |
| `negative` | `CONDITIONING` | 입력 매개변수를 기반으로 한 ControlNet 변환 적용 후의 수정된 부정적 조건 데이터로, 특정 특징의 억제 또는 제거를 반영합니다. |
