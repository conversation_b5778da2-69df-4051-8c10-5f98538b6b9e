이 노드는 조건 컨텍스트 내에서 특정 영역을 설정하여 조건 정보를 수정하도록 설계되었습니다. 지정된 치수와 강도에 따라 조건 요소의 정밀한 공간 조작을 가능하게 하여 목표로 하는 조정 및 향상을 지원합니다.

## 입력

| 매개변수       | 데이터 유형  | 설명                                                                                      |
| -------------- | ------------ | ----------------------------------------------------------------------------------------- |
| `CONDITIONING` | CONDITIONING | 수정할 조건 데이터입니다. 공간 조정을 적용하는 기반으로 사용됩니다.                       |
| `width`        | `INT`        | 조건 컨텍스트 내에서 설정할 영역의 너비를 지정하여 조정의 수평 범위에 영향을 미칩니다.    |
| `height`       | `INT`        | 설정할 영역의 높이를 결정하여 조건 수정의 수직 범위에 영향을 미칩니다.                    |
| `x`            | `INT`        | 설정할 영역의 수평 시작점을 나타내어 조건 컨텍스트 내에서 조정의 위치를 설정합니다.       |
| `y`            | `INT`        | 영역 조정의 수직 시작점을 나타내어 조건 컨텍스트 내에서 위치를 설정합니다.                |
| `strength`     | `FLOAT`      | 지정된 영역 내에서 조건 수정의 강도를 정의하여 조정의 영향을 세밀하게 제어할 수 있습니다. |

## 출력

| 매개변수       | 데이터 유형  | 설명                                                        |
| -------------- | ------------ | ----------------------------------------------------------- |
| `CONDITIONING` | CONDITIONING | 지정된 영역 설정 및 조정을 반영한 수정된 조건 데이터입니다. |
