[{"moduleName": "default", "title": "Basics", "type": "image", "templates": [{"name": "default", "title": "Image Generation", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images from text prompts."}, {"name": "image2image", "title": "Image to Image", "mediaType": "image", "mediaSubtype": "webp", "description": "Transform existing images using text prompts.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/img2img/"}, {"name": "lora", "title": "<PERSON><PERSON>", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with LoRA models for specialized styles or subjects.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/lora/"}, {"name": "lora_multiple", "title": "<PERSON><PERSON>", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images by combining multiple LoRA models.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/lora/"}, {"name": "inpaint_example", "title": "Inpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Edit specific parts of images seamlessly.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/inpaint/"}, {"name": "inpaint_model_outpainting", "title": "Outpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Extend images beyond their original boundaries.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/inpaint/#outpainting"}, {"name": "embedding_example", "title": "Embedding", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images using textual inversion for consistent styles.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/textual_inversion_embeddings/"}, {"name": "gligen_textbox_example", "title": "Gligen Textbox", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with precise object placement using text boxes.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/gligen/"}]}, {"moduleName": "default", "title": "Flux", "type": "image", "templates": [{"name": "flux_kontext_dev_basic", "title": "Flux Kontext Dev(Basic)", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "hoverDissolve", "description": "Edit image using Flux Kontext with full node visibility, perfect for learning the workflow."}, {"name": "flux_kontext_dev_grouped", "title": "Flux Kontext Dev(Grouped)", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "hoverDissolve", "description": "Streamlined version of Flux Kontext with grouped nodes for cleaner workspace."}, {"name": "flux_dev_checkpoint_example", "title": "Flux Dev fp8", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images using Flux Dev fp8 quantized version. Suitable for devices with limited VRAM, requires only one model file, but image quality is slightly lower than the full version.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-dev-1"}, {"name": "flux_schnell", "title": "Flux Schnell fp8", "mediaType": "image", "mediaSubtype": "webp", "description": "Quickly generate images with Flux Schnell fp8 quantized version. Ideal for low-end hardware, requires only 4 steps to generate images.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-schnell-1"}, {"name": "flux_dev_full_text_to_image", "title": "Flux Dev full text to image", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate high-quality images with Flux Dev full version. Requires larger VRAM and multiple model files, but provides the best prompt following capability and image quality.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-dev-1"}, {"name": "flux_schnell_full_text_to_image", "title": "Flux Schnell full text to image", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images quickly with Flux Schnell full version. Uses Apache2.0 license, requires only 4 steps to generate images while maintaining good image quality.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-schnell-1"}, {"name": "flux_fill_inpaint_example", "title": "Flux Inpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Fill missing parts of images using Flux inpainting.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#fill-inpainting-model"}, {"name": "flux_fill_outpaint_example", "title": "Flux Outpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Extend images beyond boundaries using Flux outpainting.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#fill-inpainting-model"}, {"name": "flux_canny_model_example", "title": "Flux Canny Model", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by edge detection using Flux Canny.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#canny-and-depth"}, {"name": "flux_depth_lora_example", "title": "Flux Depth Lora", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by depth information using Flux LoRA.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#canny-and-depth"}, {"name": "flux_redux_model_example", "title": "Flux Redux Model", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images by transferring style from reference images using Flux Redux.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#redux"}]}, {"moduleName": "default", "title": "Image", "type": "image", "templates": [{"name": "image_omnigen2_t2i", "title": "OmniGen2 Text to Image", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate high-quality images from text prompts using OmniGen2's unified 7B multimodal model with dual-path architecture.", "tutorialUrl": "https://docs.comfy.org/tutorials/image/omnigen/omnigen2"}, {"name": "image_omnigen2_image_edit", "title": "OmniGen2 Image Edit", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "hoverDissolve", "description": "Edit images with natural language instructions using OmniGen2's advanced image editing capabilities and text rendering support.", "tutorialUrl": "https://docs.comfy.org/tutorials/image/omnigen/omnigen2"}, {"name": "image_cosmos_predict2_2B_t2i", "title": "Cosmos Predict2 2B T2I", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with Cosmos-Predict2 2B T2I, delivering physically accurate, high-fidelity, and detail-rich image generation.", "tutorialUrl": "https://docs.comfy.org/tutorials/image/omnigen/omnigen2"}, {"name": "image_chroma_text_to_image", "title": "Chroma text to image", "mediaType": "image", "mediaSubtype": "webp", "description": "Chroma is modified from flux and has some changes in the architecture."}, {"name": "hidream_i1_dev", "title": "HiDream I1 Dev", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with HiDream I1 Dev - Balanced version with 28 inference steps, suitable for medium-range hardware."}, {"name": "hidream_i1_fast", "title": "HiDream I1 Fast", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images quickly with HiDream I1 Fast - Lightweight version with 16 inference steps, ideal for rapid previews on lower-end hardware."}, {"name": "hidream_i1_full", "title": "HiDream I1 Full", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with HiDream I1 Full - Complete version with 50 inference steps for highest quality output."}, {"name": "hidream_e1_1", "title": "HiDream E1.1 Image Edit", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "description": "Edit images with HiDream E1.1 – it’s better in image quality and editing accuracy than HiDream-E1-Full."}, {"name": "hidream_e1_full", "title": "HiDream E1 Image Edit", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "description": "Edit images with HiDream E1 - Professional natural language image editing model."}, {"name": "sd3.5_simple_example", "title": "SD3.5 Simple", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images using SD 3.5.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35"}, {"name": "sd3.5_large_canny_controlnet_example", "title": "SD3.5 Large Canny ControlNet", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by edge detection using SD 3.5 Canny ControlNet.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35-controlnets"}, {"name": "sd3.5_large_depth", "title": "SD3.5 Large Depth", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by depth information using SD 3.5.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35-controlnets"}, {"name": "sd3.5_large_blur", "title": "SD3.5 Large Blur", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by blurred reference images using SD 3.5.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35-controlnets"}, {"name": "sdxl_simple_example", "title": "SDXL Simple", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate high-quality images using SDXL.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/"}, {"name": "sdxl_refiner_prompt_example", "title": "SDXL Refiner Prompt", "mediaType": "image", "mediaSubtype": "webp", "description": "Enhance SDXL images using refiner models.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/"}, {"name": "sdxl_revision_text_prompts", "title": "SDXL Revision Text Prompts", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images by transferring concepts from reference images using SDXL Revision.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/#revision"}, {"name": "sdxl_revision_zero_positive", "title": "SDXL Revision Zero Positive", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images using both text prompts and reference images with SDXL Revision.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/#revision"}, {"name": "sdxlturbo_example", "title": "SDXL Turbo", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images in a single step using SDXL Turbo.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdturbo/"}, {"name": "image_lotus_depth_v1_1", "title": "Lotus Depth", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "description": "Run Lotus Depth in ComfyUI for zero-shot, efficient monocular depth estimation with high detail retention."}]}, {"moduleName": "default", "title": "Video", "type": "video", "templates": [{"name": "video_wan_ati", "title": "Wan ATI", "description": "Trajectory-controlled video generation.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/wan-ati"}, {"name": "video_wan_vace_14B_t2v", "title": "Wan VACE Text to Video", "description": "Transform text descriptions into high-quality videos. Supports both 480p and 720p with VACE-14B model.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_14B_ref2v", "title": "Wan VACE Reference to Video", "description": "Create videos that match the style and content of a reference image. Perfect for style-consistent video generation.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_14B_v2v", "title": "Wan VACE Control Video", "description": "Generate videos by controlling input videos and reference images using Wan VACE.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_outpainting", "title": "Wan VACE Outpainting", "description": "Generate extended videos by expanding video size using Wan VACE outpainting.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_flf2v", "title": "Wan VACE First-Last Frame", "description": "Generate smooth video transitions by defining start and end frames. Supports custom keyframe sequences.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_inpainting", "title": "Wan VACE Inpainting", "description": "Edit specific regions in videos while preserving surrounding content. Great for object removal or replacement.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan2.1_fun_camera_v1.1_1.3B", "title": "Wan 2.1 Fun Camera 1.3B", "description": "Generate dynamic videos with cinematic camera movements using Wan 2.1 Fun Camera 1.3B model.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "video_wan2.1_fun_camera_v1.1_14B", "title": "Wan 2.1 Fun Camera 14B", "description": "Generate high-quality videos with advanced camera control using the full 14B model", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "text_to_video_wan", "title": "Wan 2.1 Text to Video", "description": "Generate videos from text prompts using Wan 2.1.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/wan/#text-to-video"}, {"name": "image_to_video_wan", "title": "Wan 2.1 Image to Video", "description": "Generate videos from images using Wan 2.1.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/wan/#image-to-video"}, {"name": "wan2.1_fun_inp", "title": "Wan 2.1 Inpainting", "description": "Generate videos from start and end frames using Wan 2.1 inpainting.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/fun-inp"}, {"name": "wan2.1_fun_control", "title": "Wan 2.1 ControlNet", "description": "Generate videos guided by pose, depth, and edge controls using Wan 2.1 ControlNet.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/fun-control"}, {"name": "wan2.1_flf2v_720_f16", "title": "Wan 2.1 FLF2V 720p F16", "description": "Generate videos by controlling first and last frames using Wan 2.1 FLF2V.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/wan-flf"}, {"name": "video_cosmos_predict2_2B_video2world_480p_16fps", "title": "Cosmos Predict2 2B Video2World 480p 16fps", "description": "Generate videos with Cosmos-Predict2 2B Video2World, generating physically accurate, high-fidelity, and consistent video simulations.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "http://docs.comfy.org/tutorials/video/cosmos/cosmos-predict2-video2world"}, {"name": "ltxv_text_to_video", "title": "LTXV Text to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos from text prompts.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/ltxv/#text-to-video"}, {"name": "ltxv_image_to_video", "title": "LTXV Image to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos from still images.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/ltxv/#image-to-video"}, {"name": "mochi_text_to_video_example", "title": "Mochi Text to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos from text prompts using Mochi model.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/mochi/"}, {"name": "hunyuan_video_text_to_video", "title": "Hunyuan Video Text to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos from text prompts using Hunyuan model.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/hunyuan_video/"}, {"name": "image_to_video", "title": "SVD Image to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos from still images.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/video/#image-to-video"}, {"name": "txt_to_image_to_video", "title": "SVD Text to Image to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos by first creating images from text prompts.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/video/#image-to-video"}]}, {"moduleName": "default", "title": "Image API", "type": "image", "templates": [{"name": "api_bfl_flux_1_kontext_multiple_images_input", "title": "BFL Flux.1 Kontext Multiple Image Input", "description": "Input multiple images and edit them with Flux.1 Kontext.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_bfl_flux_1_kontext_pro_image", "title": "BFL Flux.1 Kontext Pro", "description": "Edit images with Flux.1 Kontext pro image.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_bfl_flux_1_kontext_max_image", "title": "BFL Flux.1 Kontext Max", "description": "Edit images with Flux.1 Kontext max image.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_bfl_flux_pro_t2i", "title": "BFL Flux[Pro]: Text to Image", "description": "Generate images with excellent prompt following and visual quality using FLUX.1 Pro.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_luma_photon_i2i", "title": "Luma Photon: Image to Image", "description": "Guide image generation using a combination of images and prompt.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_luma_photon_style_ref", "title": "Luma Photon: Style Reference", "description": "Generate images by blending style references with precise control using Luma Photon.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_recraft_image_gen_with_color_control", "title": "Recraft: Color Control Image Generation", "description": "Generate images with custom color palettes and brand-specific visuals using Recraft.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_recraft_image_gen_with_style_control", "title": "Recraft: Style Control Image Generation", "description": "Control style with visual examples, align positioning, and fine-tune objects. Store and share styles for perfect brand consistency.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_recraft_vector_gen", "title": "Recraft: Vector Generation", "description": "Generate high-quality vector images from text prompts using Recraft's AI vector generator.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_text_to_image", "title": "Runway: Text to Image", "description": "Generate high-quality images from text prompts using Runway's AI model.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_reference_to_image", "title": "Runway: Reference to Image", "description": "Generate new images based on reference styles and compositions with Runway's AI.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_stability_ai_stable_image_ultra_t2i", "title": "Stability AI: Stable Image Ultra Text to Image", "description": "Generate high quality images with excellent prompt adherence. Perfect for professional use cases at 1 megapixel resolution.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_stability_ai_i2i", "title": "Stability AI: Image to Image", "description": "Transform images with high-quality generation using Stability AI, perfect for professional editing and style transfer.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_stability_ai_sd3.5_t2i", "title": "Stability AI: SD3.5 Text to Image", "description": "Generate high quality images with excellent prompt adherence. Perfect for professional use cases at 1 megapixel resolution.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_stability_ai_sd3.5_i2i", "title": "Stability AI: SD3.5 Image to Image", "description": "Generate high quality images with excellent prompt adherence. Perfect for professional use cases at 1 megapixel resolution.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_ideogram_v3_t2i", "title": "Ideogram V3: Text to Image", "description": "Generate professional-quality images with excellent prompt alignment, photorealism, and text rendering using Ideogram V3.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_openai_image_1_t2i", "title": "OpenAI: GPT-Image-1 Text to Image", "description": "Generate images from text prompts using OpenAI GPT Image 1 API.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_image_1_i2i", "title": "OpenAI: GPT-Image-1 Image to Image", "description": "Generate images from input images using OpenAI GPT Image 1 API.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_image_1_inpaint", "title": "OpenAI: GPT-Image-1 Inpaint", "description": "Edit images using inpainting with OpenAI GPT Image 1 API.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_image_1_multi_inputs", "title": "OpenAI: GPT-Image-1 Multi Inputs", "description": "Generate images from multiple inputs using OpenAI GPT Image 1 API.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_dall_e_2_t2i", "title": "OpenAI: Dall-E 2 Text to Image", "description": "Generate images from text prompts using OpenAI Dall-E 2 API.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-2"}, {"name": "api_openai_dall_e_2_inpaint", "title": "OpenAI: Dall-E 2 Inpaint", "description": "Edit images using inpainting with OpenAI Dall-E 2 API.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-2"}, {"name": "api_openai_dall_e_3_t2i", "title": "OpenAI: Dall-E 3 Text to Image", "description": "Generate images from text prompts using OpenAI Dall-E 3 API.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-3"}]}, {"moduleName": "default", "title": "Video API", "type": "video", "templates": [{"name": "api_kling_i2v", "title": "Kling: Image to Video", "description": "Generate videos with excellent prompt adherence for actions, expressions, and camera movements using <PERSON><PERSON>.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_kling_effects", "title": "Kling: Video Effects", "description": "Generate dynamic videos by applying visual effects to images using Kling.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_kling_flf", "title": "Kling: FLF2V", "description": "Generate videos through controlling the first and last frames.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_luma_i2v", "title": "Luma: Image to Video", "description": "Take static images and instantly create magical high quality animations.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_luma_t2v", "title": "Luma: Text to Video", "description": "High-quality videos can be generated using simple prompts.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_moonvalley_text_to_video", "title": "Moonvalley: Text to Video", "description": "Generate cinematic, 1080p videos from text prompts through a model trained exclusively on licensed data.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_moonvalley_image_to_video", "title": "Moonvalley: Image to Video", "description": "Generate cinematic, 1080p videos with an image through a model trained exclusively on licensed data.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_hailuo_minimax_t2v", "title": "MiniMax: Text to Video", "description": "Generate high-quality videos directly from text prompts. Explore MiniMax's advanced AI capabilities to create diverse visual narratives with professional CGI effects and stylistic elements to bring your descriptions to life.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_hailuo_minimax_i2v", "title": "MiniMax: Image to Video", "description": "Generate refined videos from images and text with CGI integration using MiniMax.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pixverse_i2v", "title": "PixVerse: Image to Video", "description": "Generate dynamic videos from static images with motion and effects using PixVerse.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pixverse_template_i2v", "title": "PixVerse Templates: Image to Video", "description": "Generate dynamic videos from static images with motion and effects using PixVerse.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pixverse_t2v", "title": "PixVerse: Text to Video", "description": "Generate videos with accurate prompt interpretation and stunning video dynamics.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_gen3a_turbo_image_to_video", "title": "Runway: Gen3a Turbo Image to Video", "description": "Generate cinematic videos from static images using Runway Gen3a Turbo.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_gen4_turo_image_to_video", "title": "Runway: Gen4 Turbo Image to Video", "description": "Generate dynamic videos from images using Runway Gen4 Turbo.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_first_last_frame", "title": "Runway: First Last Frame to Video", "description": "Generate smooth video transitions between two keyframes with Runway's precision.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pika_i2v", "title": "Pika: Image to Video", "description": "Generate smooth animated videos from single static images using Pika AI.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pika_scene", "title": "Pika Scenes: Images to Video", "description": "Generate videos that incorporate multiple input images using Pika Scenes.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_veo2_i2v", "title": "Veo2: Image to Video", "description": "Generate videos from images using Google Veo2 API.", "mediaType": "image", "mediaSubtype": "webp"}]}, {"moduleName": "default", "title": "3D API", "type": "image", "templates": [{"name": "api_rodin_image_to_model", "title": "Rodin: Image to Model", "description": "Generate detailed 3D models from single photos using Rodin AI.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_rodin_multiview_to_model", "title": "Rodin: Multiview to Model", "description": "<PERSON>ulpt comprehensive 3D models using <PERSON><PERSON>'s multi-angle reconstruction.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_tripo_text_to_model", "title": "Tripo: Text to Model", "description": "Craft 3D objects from descriptions with <PERSON><PERSON>'s text-driven modeling.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_tripo_image_to_model", "title": "Tripo: Image to Model", "description": "Generate professional 3D assets from 2D images using Tripo engine.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_tripo_multiview_to_model", "title": "Tripo: Multiview to Model", "description": "Build 3D models from multiple angles with Tripo's advanced scanner.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}]}, {"moduleName": "default", "title": "LLM API", "type": "image", "templates": [{"name": "api_openai_chat", "title": "OpenAI: <PERSON><PERSON>", "description": "Engage with OpenAI's advanced language models for intelligent conversations.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_google_gemini", "title": "Google Gemini: <PERSON><PERSON>", "description": "Experience Google's multimodal AI with Gemini's reasoning capabilities.", "mediaType": "image", "mediaSubtype": "webp"}]}, {"moduleName": "default", "title": "Upscaling", "type": "image", "templates": [{"name": "hiresfix_latent_workflow", "title": "Upscale", "mediaType": "image", "mediaSubtype": "webp", "description": "Upscale images by enhancing quality in latent space.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/"}, {"name": "esrgan_example", "title": "ESRGAN", "mediaType": "image", "mediaSubtype": "webp", "description": "Upscale images using ESRGAN models to enhance quality.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/upscale_models/"}, {"name": "hiresfix_esrgan_workflow", "title": "HiresFix ESRGAN Workflow", "mediaType": "image", "mediaSubtype": "webp", "description": "Upscale images using ESRGAN models during intermediate generation steps.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/#non-latent-upscaling"}, {"name": "latent_upscale_different_prompt_model", "title": "Latent Upscale Different Prompt Model", "mediaType": "image", "mediaSubtype": "webp", "description": "Upscale images while changing prompts across generation passes.", "thumbnailVariant": "zoomHover", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/#more-examples"}]}, {"moduleName": "default", "title": "ControlNet", "type": "image", "templates": [{"name": "controlnet_example", "title": "Scribble ControlNet", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by scribble reference images using ControlNet.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/"}, {"name": "2_pass_pose_worship", "title": "Pose ControlNet 2 Pass", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by pose references using ControlNet.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#pose-controlnet"}, {"name": "depth_controlnet", "title": "Depth ControlNet", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by depth information using ControlNet.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#t2i-adapter-vs-controlnets"}, {"name": "depth_t2i_adapter", "title": "Depth T2I Adapter", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images guided by depth information using T2I adapter.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#t2i-adapter-vs-controlnets"}, {"name": "mixing_controlnets", "title": "Mixing ControlNets", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images by combining multiple ControlNet models.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#mixing-controlnets"}]}, {"moduleName": "default", "title": "Area Composition", "type": "image", "templates": [{"name": "area_composition", "title": "Area Composition", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images by controlling composition with defined areas.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/area_composition/"}, {"name": "area_composition_square_area_for_subject", "title": "Area Composition Square Area for Subject", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with consistent subject placement using area composition.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/area_composition/#increasing-consistency-of-images-with-area-composition"}]}, {"moduleName": "default", "title": "3D", "type": "3d", "templates": [{"name": "3d_hunyuan3d_image_to_model", "title": "Hunyuan3D 2.0", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate 3D models from single images using Hunyuan3D 2.0.", "tutorialUrl": ""}, {"name": "3d_hunyuan3d_multiview_to_model", "title": "Hunyuan3D 2.0 MV", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate 3D models from multiple views using Hunyuan3D 2.0 MV.", "tutorialUrl": "", "thumbnailVariant": "hoverDissolve"}, {"name": "3d_hunyuan3d_multiview_to_model_turbo", "title": "Hunyuan3D 2.0 MV Turbo", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate 3D models from multiple views using Hunyuan3D 2.0 MV Turbo.", "tutorialUrl": "", "thumbnailVariant": "hoverDissolve"}, {"name": "stable_zero123_example", "title": "Stable Zero123", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate 3D views from single images using Stable Zero123.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/3d/"}]}, {"moduleName": "default", "title": "Audio", "type": "audio", "templates": [{"name": "audio_stable_audio_example", "title": "Stable Audio", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Generate audio from text prompts using Stable Audio.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/audio/"}, {"name": "audio_ace_step_1_t2a_instrumentals", "title": "ACE-Step v1 Text to Instrumentals Music", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Generate instrumental music from text prompts using ACE-Step v1.", "tutorialUrl": "https://docs.comfy.org/tutorials/audio/ace-step/ace-step-v1"}, {"name": "audio_ace_step_1_t2a_song", "title": "ACE Step v1 Text to Song", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Generate songs with vocals from text prompts using ACE-Step v1, supporting multilingual and style customization.", "tutorialUrl": "https://docs.comfy.org/tutorials/audio/ace-step/ace-step-v1"}, {"name": "audio_ace_step_1_m2m_editing", "title": "ACE Step v1 M2M Editing", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Edit existing songs to change style and lyrics using ACE-Step v1 M2M.", "tutorialUrl": "https://docs.comfy.org/tutorials/audio/ace-step/ace-step-v1"}]}]