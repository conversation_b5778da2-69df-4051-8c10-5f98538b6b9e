{"id": "7087dfe4-e1e5-4bca-92a2-1a41758c0dac", "revision": 0, "last_node_id": 39, "last_link_id": 91, "nodes": [{"id": 29, "type": "CheckpointLoaderSimple", "pos": [70, 150], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [80]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [81, 82]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [83, 84]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "512-inpainting-ema.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-inpainting/resolve/main/512-inpainting-ema.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["512-inpainting-ema.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [432, 158], "size": [422.81243896484375, 161.2169189453125], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 81}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["a close-up of a delicate pink rose with velvety petals,reflecting soft ambient light,Dark green-toned light\n\nThe background consists of blurred pink roses and green foliage, creating a dreamy and harmonious depth. \n\n(soft lighting, dim background, cinematic lighting, realistic shading, gentle contrast, warm tones), "], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [430, 370], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 82}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["watermark, text"], "color": "#223", "bgcolor": "#335"}, {"id": 20, "type": "LoadImage", "pos": [-40, 670], "size": [344, 346], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [90]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["example.png", "image"]}, {"id": 38, "type": "ImageScaleToTotalPixels", "pos": [320, 670], "size": [250, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 90}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [91]}], "properties": {"Node name for S&R": "ImageScaleToTotalPixels"}, "widgets_values": ["nearest-exact", 0.25]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [900, 120], "size": [315, 474], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 80}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 72}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [42]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [476514576444494, "randomize", 20, 7, "dpmpp_2m", "karras", 1]}, {"id": 8, "type": "VAEDecode", "pos": [1240, 120], "size": [210, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 42}, {"name": "vae", "type": "VAE", "link": 83}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [22]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1240, 210], "size": [420, 380], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 22}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["ComfyUI"]}, {"id": 30, "type": "ImagePadForOutpaint", "pos": [320, 790], "size": [250, 174], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 91}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [87]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": [86]}], "properties": {"Node name for S&R": "ImagePadForOutpaint", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [40, 40, 40, 128, 10]}, {"id": 39, "type": "<PERSON>downNote", "pos": [320, 1010], "size": [250, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "Learn about this node", "properties": {}, "widgets_values": ["Select the node then click the \"?\" icon on the selection toolbox"], "color": "#432", "bgcolor": "#653"}, {"id": 36, "type": "<PERSON>downNote", "pos": [600, 670], "size": [260, 100], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "About Scale Image to Total Pixels", "properties": {}, "widgets_values": ["The model this template uses is trained based on a 512*512 image dataset. So if you use an input image that's too large, it might cause some issues. We've added the Scale image to total pixels node to scale images. If you're quite familiar with this model, you can remove it."], "color": "#432", "bgcolor": "#653"}, {"id": 26, "type": "VAEEncodeForInpaint", "pos": [600, 860], "size": [226.8000030517578, 98], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 87}, {"name": "vae", "type": "VAE", "link": 84}, {"name": "mask", "type": "MASK", "link": 86}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [72]}], "properties": {"Node name for S&R": "VAEEncodeForInpaint", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [10]}, {"id": 35, "type": "<PERSON>downNote", "pos": [-340, 110], "size": [380, 250], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "Tutorial links", "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org/tutorials/basic/outpaint) | [教程](https://docs.comfy.org/zh-CN/tutorials/basic/outpaint)\n\n## Model link\n**checkpoint**\n\n[512-inpainting-ema.safetensors](https://huggingface.co/stabilityai/stable-diffusion-2-inpainting/resolve/main/512-inpainting-ema.safetensors)\n\n\nModel Storage Location\n\n```\n📂 ComfyUI/\n├── 📂 models/\n│   ├── 📂 checkpoints/\n│   │   └── 512-inpainting-ema.safetensors\n\n```\n\n\n"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [22, 8, 0, 9, 0, "IMAGE"], [42, 3, 0, 8, 0, "LATENT"], [72, 26, 0, 3, 3, "LATENT"], [80, 29, 0, 3, 0, "MODEL"], [81, 29, 1, 6, 0, "CLIP"], [82, 29, 1, 7, 0, "CLIP"], [83, 29, 2, 8, 1, "VAE"], [84, 29, 2, 26, 1, "VAE"], [86, 30, 1, 26, 2, "MASK"], [87, 30, 0, 26, 0, "IMAGE"], [90, 20, 0, 38, 0, "IMAGE"], [91, 38, 0, 30, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Step2 - Load image and pad for outpainting", "bounding": [-60, 570, 930, 540], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step1 - Load model", "bounding": [60, 80, 335, 181.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - Prompt", "bounding": [420, 80, 447.280029296875, 477.2099914550781], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6422651457018385, "offset": [1348.2270447393946, 268.16170419988515]}, "frontendVersion": "1.24.1"}, "version": 0.4}