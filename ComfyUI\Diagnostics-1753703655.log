ComfyUI Launcher Diagnostic File

Date: 2025-07-28 19:54:22
Launcher Version: 2.8.13.414
Data File Version: 2025-04-30 01:29
ComfyUI Version: e6d9f6274494c5ac96295deb1bea54de50189059 (2025-07-28 07:51:36)
Working Directory: D:\AI\koutu
App Directory: D:\AI\koutu\ComfyUI
------------------------
System Information: 
OS: Microsoft Windows NT 10.0.26100.0
CPU: 24 cores
Memory Size: 65536 MB Total, 34748 MB Free
Allocated Page File Size: 4096 MB Total, 4096 MB Free
Page File Settings: 0 MB Initial, 0 MB Maximum


NVIDIA Management Library:
  NVIDIA Driver Version: 576.02
  NVIDIA Management Library Version: 12.576.02

CUDA Driver:
  Version: 12090
  Devices: 
    00000000:02:00.0 0: NVIDIA GeForce RTX 4090 D [89] 23 GB

NvApi:
  Version: 57602 r575_92

HIP Driver:
  Not Available

DirectML Driver: 
  Devices: 
    9861 0: NVIDIA GeForce RTX 4090 D 23 GB

Intel Level Zero Driver:
  Not Available

------------------------
Environment Variables: 
OS=Windows_NT
APPDATA=C:\Users\<USER>\AppData\Roaming
ProgramFiles=C:\Program Files
Path=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;C:\ProgramData\miniconda3\Library\bin;;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\miniconda3;C:\Users\<USER>\miniconda3\Library\mingw-w64\bin;C:\Users\<USER>\miniconda3\Library\usr\bin;C:\Users\<USER>\miniconda3\Library\bin;C:\Users\<USER>\miniconda3\Scripts;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\ProgramData\miniconda3;C:\ProgramData\miniconda3\Scripts;C:\ProgramData\miniconda3\Library\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;D:\Tools\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
SystemDrive=C:
CommonProgramW6432=C:\Program Files\Common Files
USERNAME=admin
ProgramData=C:\ProgramData
PROCESSOR_LEVEL=6
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 198 Stepping 2, GenuineIntel
EFC_15296_1592913036=1
PUBLIC=C:\Users\<USER>\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6
HOMEPATH=\Users\ronal
VS140COMNTOOLS=C:\Program Files (x86)\Microsoft Visual Studio 14.0\Common7\Tools\
SystemRoot=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
SESSIONNAME=Console
TMP=C:\Users\<USER>\AppData\Local\Temp
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
LOGONSERVER=\\REECHEN-PC6
ProgramW6432=C:\Program Files
USERPROFILE=C:\Users\<USER>\Program Files (x86)
EFC_15296_2283032206=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
COMPUTERNAME=REECHEN-PC6
CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
NUMBER_OF_PROCESSORS=24
EFC_15296_2775293581=1
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
USERDOMAIN_ROAMINGPROFILE=REECHEN-PC6
CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6
ALLUSERSPROFILE=C:\ProgramData
PROCESSOR_ARCHITECTURE=AMD64
OneDrive=C:\Users\<USER>\OneDrive
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
ComSpec=C:\Windows\system32\cmd.exe
DriverData=C:\Windows\System32\Drivers\DriverData
USERDOMAIN=REECHEN-PC6
EFC_15296_1262719628=1
PROCESSOR_REVISION=c602
FPS_BROWSER_USER_PROFILE_STRING=Default
CommonProgramFiles=C:\Program Files\Common Files
windir=C:\Windows
------------------------
Paths: 
Python: D:\AI\koutu\python\python.exe
  - Python Paths:
    - D:\AI\koutu\python\DLLs
    - D:\AI\koutu\python\Lib
    - D:\AI\koutu\python
    - D:\AI\koutu\python\Lib\site-packages
    - D:\AI\koutu\python\Lib\site-packages\win32
    - D:\AI\koutu\python\Lib\site-packages\win32\lib
    - D:\AI\koutu\python\Lib\site-packages\Pythonwin
  - Python Packages:
    - absl_py 2.1.0 
    - accelerate 1.9.0 
    - addict 2.4.0 
    - aiohappyeyeballs 2.4.4 
    - aiohttp 3.11.11 
    - aiosignal 1.3.2 
    - albucore 0.0.24 
    - albumentations 2.0.8 
    - alembic 1.16.4 
    - annotated_types 0.7.0 
    - antlr4_python3_runtime 4.9.3 
    - anyio 4.8.0 
    - attrs 25.1.0 
    - av 14.2.0 
    - beautifulsoup4 4.13.4 
    - cachetools 5.5.1 
    - certifi 2025.7.14 
    - cffi 1.17.1 
    - chardet 5.2.0 
    - charset_normalizer 3.4.2 
    - click 8.1.8 
    - clip_interrogator 0.6.0 
    - cmake 3.31.4 
    - colorama 0.4.6 
    - coloredlogs 15.0.1 
    - colorlog 6.9.0 
    - color_matcher 0.5.0 
    - colour_science 0.4.6 
    - comfyui_embedded_docs 0.2.4 
    - comfyui_frontend_package 1.23.4 
    - comfyui_workflow_templates 0.1.40 
    - contourpy 1.3.3 
    - cryptography 44.0.0 
    - cssselect2 0.7.0 
    - cstr 0.1.0 
    - cycler 0.12.1 
    - ddt 1.7.2 
    - deepdiff 8.2.0 
    - Deprecated 1.2.18 
    - diffusers 0.34.0 
    - dill 0.3.9 
    - docutils 0.21.2 
    - easydict 1.13 
    - einops 0.8.0 
    - fairscale 0.4.13 
    - ffmpy 0.3.0 
    - filelock 3.18.0 
    - flatbuffers 25.1.24 
    - flet 0.26.0 
    - fonttools 4.59.0 
    - frozenlist 1.5.0 
    - fsspec 2025.7.0 
    - ftfy 6.3.1 
    - fvcore 0.1.5.post20221221 
    - gdown 5.2.0 
    - gitdb 4.0.12 
    - GitPython 3.1.44 
    - greenlet 3.2.3 
    - groundingdino_py 0.4.0 
    - h11 0.14.0 
    - httpcore 1.0.7 
    - httpx 0.28.1 
    - huggingface_hub 0.34.1 
    - humanfriendly 10.0 
    - idna 3.10 
    - imageio 2.37.0 
    - imageio_ffmpeg 0.6.0 
    - img2texture 1.0.6 
    - importlib_metadata 8.7.0 
    - iopath 0.1.9 
    - jax 0.7.0 
    - jaxlib 0.7.0 
    - jinja2 3.1.6 
    - joblib 1.4.2 
    - jsonschema 4.23.0 
    - jsonschema_specifications 2024.10.1 
    - kiwisolver 1.4.8 
    - kornia 0.8.1 
    - kornia_rs 0.1.9 
    - lazy_loader 0.4 
    - llvmlite 0.44.0 
    - lxml 5.3.0 
    - mako 1.3.10 
    - manifold3d 3.0.1 
    - mapbox_earcut 1.0.3 
    - markdown_it_py 3.0.0 
    - MarkupSafe 3.0.2 
    - matplotlib 3.10.3 
    - matrix_client 0.4.0 
    - mdurl 0.1.2 
    - mediapipe 0.10.20 
    - ml_dtypes 0.5.1 
    - mpmath 1.3.0 
    - mss 10.0.0 
    - multidict 6.1.0 
    - networkx 3.5 
    - numba 0.61.2 
    - numpy 1.26.4 
    - nvidia_ml_py 12.570.86 
    - oauthlib 3.2.2 
    - omegaconf 2.3.0 
    - onnxruntime 1.20.1 
    - onnxruntime_gpu 1.20.1 
    - opencv_contrib_python_headless ********* 
    - opencv_python ********* 
    - open_clip_torch 2.30.0 
    - opt_einsum 3.4.0 
    - orderly_set 5.3.0 
    - packaging 25.0 
    - pandas 2.2.3 
    - piexif 1.1.3 
    - pilgram 1.2.1 
    - pillow 11.3.0 
    - pip 24.0 
    - pixeloe 0.1.1 
    - platformdirs 4.3.8 
    - pooch 1.8.2 
    - portalocker 3.1.1 
    - propcache 0.2.1 
    - protobuf 4.25.6 
    - psutil 7.0.0 
    - pycocotools 2.0.10 
    - pycollada 0.8 
    - pycparser 2.22 
    - pydantic 2.11.7 
    - pydantic_core 2.33.2 
    - pydantic_settings 2.10.1 
    - PyGithub 2.5.0 
    - pygments 2.19.1 
    - PyJWT 2.10.1 
    - pymatting 1.1.14 
    - PyNaCl 1.5.0 
    - pynvml 12.0.0 
    - pyparsing 3.2.3 
    - pyreadline3 3.5.4 
    - PySocks 1.7.1 
    - python_dateutil 2.9.0.post0 
    - python_dotenv 1.1.1 
    - pytz 2025.1 
    - pywin32 308 
    - PyYAML 6.0.2 
    - py_cpuinfo 9.0.0 
    - referencing 0.36.2 
    - regex 2024.11.6 
    - rembg 2.0.62 
    - repath 0.9.0 
    - reportlab 4.3.0 
    - requests 2.32.4 
    - rich 13.9.4 
    - rpds_py 0.22.3 
    - Rtree 1.3.0 
    - safetensors 0.5.3 
    - scikit_image 0.25.1 
    - scikit_learn 1.6.1 
    - scipy 1.16.1 
    - seaborn 0.13.2 
    - segment_anything 1.0 
    - sentencepiece 0.2.0 
    - setuptools 65.5.0 
    - shapely 2.0.7 
    - shellingham 1.5.4 
    - simpleeval 1.0.3 
    - simsimd 6.5.0 
    - six 1.17.0 
    - smmap 5.0.2 
    - sniffio 1.3.1 
    - sounddevice 0.5.1 
    - soundfile 0.13.1 
    - soupsieve 2.7 
    - spandrel 0.4.1 
    - sqlalchemy 2.0.41 
    - stringzilla 3.12.5 
    - supervision 0.6.0 
    - svg.path 6.3 
    - svglib 1.5.1 
    - sympy 1.13.1 
    - tabulate 0.9.0 
    - termcolor 2.5.0 
    - threadpoolctl 3.5.0 
    - tifffile 2025.1.10 
    - timm 1.0.19 
    - tinycss2 1.4.0 
    - tokenizers 0.21.2 
    - toml 0.10.2 
    - torch 2.5.1+cu124 
    - torchaudio 2.5.1+cu124 
    - torchsde 0.2.6 
    - torchvision 0.20.1+cu124 
    - tqdm 4.67.1 
    - trampoline 0.1.2 
    - transformers 4.54.0 
    - transparent_background 1.3.4 
    - trimesh 4.6.1 
    - typer 0.15.1 
    - typing_extensions 4.14.1 
    - typing_inspection 0.4.1 
    - tzdata 2025.1 
    - ultralytics 8.3.40 
    - ultralytics_thop 2.0.14 
    - urllib3 2.5.0 
    - uv 0.5.27 
    - vhacdx 0.0.8.post1 
    - wcwidth 0.2.13 
    - webcolors 24.11.1 
    - webencodings 0.5.1 
    - wget 3.2 
    - wrapt 1.17.2 
    - xatlas 0.0.9 
    - xformers 0.0.28.post3 
    - xxhash 3.5.0 
    - yacs 0.1.8 
    - yapf 0.43.0 
    - yarl 1.18.3 
    - zipp 3.23.0 
Git: D:\AI\koutu\git\cmd\git.exe
Shell: C:\Windows\system32\cmd.exe
Cache Path: D:\AI\koutu\.cache
------------------------
Engine Validator: 
PyTorch: 2.5.1+cu124 C(12040)____
OnnxRuntime: CUDA(12020)
------------------------
Port Info: 
Exclusion List:
  Non-administered:
    
  Administered:
    
In Use (v4):
  53 => 25732
  135 => 1908
  139 => 4
  445 => 4
  3389 => 1668
  5040 => 14912
  5225 => 15444
  5793 => 43588
  7680 => 36012
  8188 => 37412
  9080 => 6704
  12101 => 25732
  12639 => 25732
  12649 => 25732
  12650 => 25732
  12679 => 25732
  12759 => 25732
  12769 => 25732
  36510 => 40516
  37510 => 40516
  46000 => 3268
  46025 => 3268
  46050 => 3268
  46075 => 36212
  49664 => 1532
  49665 => 1432
  49668 => 3436
  49670 => 4276
  49677 => 5644
  49680 => 6312
  49701 => 1504
  50010 => 43588
  56100 => 4172
  56101 => 4172
  56135 => 4172
  58089 => 16680
In Use (v6):
  135 => 1908
  445 => 4
  3389 => 1668
  5225 => 15444
  7680 => 36012
  49664 => 1532
  49665 => 1432
  49668 => 3436
  49670 => 4276
  49677 => 5644
  49680 => 6312
  49701 => 1504
------------------------
Config: 
Audience Type: 新手
Lock Engine: True
Engine: CUDA GPU 0: NVIDIA GeForce RTX 4090 D (24 GB) [0]
VRAM Optimization: Auto [Auto]
Port: 8188 [8188]
XAttn Optimization: Xformers [Xformers]
CPU VAE: False [False]
Upcast Attention: True [True]
Precision: Auto [Auto]
Text Encoder Precision: Auto [Auto]
UNet Precision: Auto [Auto]
VAE Precision: Auto [Auto]
Force Channels Last: False [False]
Preview Method: Auto [Auto]
Smart Memory: True [True]
Deterministic: False [False]
Multi User: False [False]
Fast: True [True]
Listen: False [False]
Server Name:  []
HF Offline Mode: False [False]
Cuda Allocator Backend: CudaMallocAsync [CudaMallocAsync]
Prevent Sysmem Fallback: False
Extra Args: 
------------------------
Network Preferences: 
Proxy Address: 
Proxy Git: False
Proxy Pip: False
Proxy Model Download: False
Proxy Env: False
Mirror Pypi: True
Mirror Git: True
Mirror ExtensionList: True
Mirror Huggingface: True
Github Acceleration: False
------------------------
Log: 
[START] Security scan
[DONE] Security scan
## ComfyUI-Manager: installing dependencies done.
** ComfyUI startup time: 2025-07-28 19:52:42.057
** Platform: Windows
** Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
** Python executable: D:\AI\koutu\python\python.exe
** ComfyUI Path: D:\AI\koutu\ComfyUI
** ComfyUI Base Folder Path: D:\AI\koutu\ComfyUI
** User directory: D:\AI\koutu\ComfyUI\user
** ComfyUI-Manager config path: D:\AI\koutu\ComfyUI\user\default\ComfyUI-Manager\config.ini
** Log path: D:\AI\koutu\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
   7.6 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager

Checkpoint files will always be loaded safely.
Total VRAM 24564 MB, total RAM 65149 MB
pytorch version: 2.5.1+cu124
xformers version: 0.0.28.post3
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
Using xformers attention
Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
ComfyUI version: 0.3.45
ComfyUI frontend version: 1.23.4
[Prompt Server] web root: D:\AI\koutu\python\Lib\site-packages\comfyui_frontend_package\static
[AnimateDiffEvo] - [0;31mERROR[0m - No motion models found. Please download one and place in: ['D:\\AI\\koutu\\ComfyUI\\custom_nodes\\comfyui-animatediff-evolved\\models', 'D:\\AI\\koutu\\ComfyUI\\models\\animatediff_models']
[Crystools [0;32mINFO[0m] Crystools version: 1.22.1
[Crystools [0;32mINFO[0m] CPU: Intel(R) Core(TM) Ultra 9 285K - Arch: AMD64 - OS: Windows 10
[Crystools [0;32mINFO[0m] Pynvml (Nvidia) initialized.
[Crystools [0;32mINFO[0m] GPU/s:
[Crystools [0;32mINFO[0m] 0) NVIDIA GeForce RTX 4090 D
[Crystools [0;32mINFO[0m] NVIDIA Driver: 576.02
Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\__init__.py", line 1, in <module>
    from .nodes import NODE_CLASS_MAPPINGS as NODES_CLASS, NODE_DISPLAY_NAME_MAPPINGS as NODES_DISPLAY
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\nodes.py", line 10, in <module>
    from .hyvideo.text_encoder import TextEncoder
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\hyvideo\text_encoder\__init__.py", line 9, in <module>
    from .processing_llava import LlavaProcessor
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper\hyvideo\text_encoder\processing_llava.py", line 23, in <module>
    from transformers.processing_utils import ProcessingKwargs, ProcessorMixin, Unpack, _validate_images_text_input_order
ImportError: cannot import name '_validate_images_text_input_order' from 'transformers.processing_utils' (D:\AI\koutu\python\Lib\site-packages\transformers\processing_utils.py)

Cannot import D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper module for custom nodes: cannot import name '_validate_images_text_input_order' from 'transformers.processing_utils' (D:\AI\koutu\python\Lib\site-packages\transformers\processing_utils.py)
### Loading: ComfyUI-Impact-Pack (V8.8.5)

[Impact Pack] Wildcards loading done.
### Loading: ComfyUI-Inspire-Pack (V1.14.1)
Total VRAM 24564 MB, total RAM 65149 MB
pytorch version: 2.5.1+cu124
xformers version: 0.0.28.post3
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 4090 D : cudaMallocAsync
### Loading: ComfyUI-Manager (V3.31.1)
[ComfyUI-Manager] network_mode: public
### ComfyUI Version: v0.3.45-25-ge6d9f627 | Released on '2025-07-27'
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
(pysssss:WD14Tagger) [DEBUG] Available ORT providers: TensorrtExecutionProvider, CUDAExecutionProvider, CPUExecutionProvider
(pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
[36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[36;20m[D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
DWPose: Onnxruntime with acceleration providers detected

[36mEfficiency Nodes:[0m Attempting to add Control Net options to the 'HiRes-Fix Script' Node (comfyui_controlnet_aux add-on)...[92mSuccess![0m
[34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m

FETCH ComfyRegistry Data: 5/92

[34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m

	[3m[93m"Art is the journey of a free soul."[0m[3m - Alev Oguz[0m

[92m[rgthree-comfy] Loaded 42 fantastic nodes. 🎉[00m

Import times for custom nodes:
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\websocket_image_save.py
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\sd-dynamic-thresholding
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-WD14-Tagger
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_ipadapter_plus
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\AIGODLIKE-COMFYUI-TRANSLATION
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_essentials
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_UltimateSDUpscale
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-custom-scripts
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-advanced-controlnet
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\rgthree-comfy
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-animatediff-evolved
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Inspire-Pack
   0.0 seconds: D:\AI\koutu\ComfyUI\custom_nodes\efficiency-nodes-comfyui
   0.1 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-KJNodes
   0.1 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-layerdiffuse
   0.4 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-crystools
   0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-Impact-Pack
   0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-VideoHelperSuite
   0.5 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui_controlnet_aux
   0.7 seconds: D:\AI\koutu\ComfyUI\custom_nodes\comfyui-manager
   0.8 seconds (IMPORT FAILED): D:\AI\koutu\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper
   4.2 seconds: D:\AI\koutu\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894

Context impl SQLiteImpl.
Will assume non-transactional DDL.
No target revision found.
Starting server

To see the GUI go to: http://127.0.0.1:8188
FETCH ComfyRegistry Data: 10/92
got prompt
FETCH ComfyRegistry Data: 15/92

Cache check: Missing model files: birefnet.py
Downloading required model files...

Downloading RMBG-2.0 model files...

Downloading config.json...

D:\AI\koutu\python\Lib\site-packages\huggingface_hub\file_download.py:980: UserWarning: `local_dir_use_symlinks` parameter is deprecated and will be ignored. The process to download files to a local folder has been updated and do not rely on symlinks anymore. You only need to pass a destination folder as`local_dir`.
For more details, check out https://huggingface.co/docs/huggingface_hub/main/en/guides/download#download-files-to-local-folder.
  warnings.warn(
Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: config.json))
Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: config.json))
Downloading model.safetensors...
Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: model.safetensors))
Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: model.safetensors))
Downloading birefnet.py...
Downloading BiRefNet_config.py...
Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: BiRefNet_config.py))
Couldn't access the Hub to check for update but local file already exists. Defaulting to existing file. (error: Cannot access file since 'local_files_only=True' as been set. (repo_id: 1038lab/RMBG-2.0, repo_type: model, revision: main, filename: BiRefNet_config.py))
Model files downloaded successfully

[RMBG ERROR] Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
[RMBG ERROR] Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
!!! Exception during processing !!! Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter
Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 177, in process_image
    self.load_model(model_name)
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 161, in load_model
    self.model = AutoModelForImageSegmentation.from_pretrained(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\auto_factory.py", line 547, in from_pretrained
    config, kwargs = AutoConfig.from_pretrained(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\models\auto\configuration_auto.py", line 1262, in from_pretrained
    return config_class.from_pretrained(pretrained_model_name_or_path, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 627, in from_pretrained
    return cls.from_dict(config_dict, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 789, in from_dict
    config = cls(**config_dict)
             ^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\.cache\huggingface\modules\transformers_modules\RMBG-2.0\BiRefNet_config.py", line 13, in __init__
    super().__init__(**kwargs)
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 284, in __init__
    self.tie_word_embeddings = tie_word_embeddings
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\python\Lib\site-packages\transformers\configuration_utils.py", line 206, in __setattr__
    super().__setattr__(key, value)
AttributeError: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 516, in process_image
    mask = model_instance.process_image(img, model, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 220, in process_image
    handle_model_error(f"Error in batch processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 88, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI\koutu\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "D:\AI\koutu\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 584, in process_image
    handle_model_error(f"Error in image processing: {str(e)}")
  File "D:\AI\koutu\ComfyUI\custom_nodes\comfyui-rmbg\AILab_RMBG.py", line 88, in handle_model_error
    raise RuntimeError(message)
RuntimeError: Error in image processing: Error in batch processing: property 'tie_word_embeddings' of 'BiRefNetConfig' object has no setter

Prompt executed in 1.16 seconds
FETCH ComfyRegistry Data: 20/92
FETCH ComfyRegistry Data: 25/92
Cannot connect to comfyregistry.
nightly_channel: 
https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote

FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
 [DONE]
[ComfyUI-Manager] All startup tasks have been completed.
------------------------
Fault Traceback: 
Not Available
