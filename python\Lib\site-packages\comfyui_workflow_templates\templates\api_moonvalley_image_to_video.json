{"id": "ba9df292-1ee9-4b7c-af08-690441990d87", "revision": 0, "last_node_id": 34, "last_link_id": 36, "nodes": [{"id": 27, "type": "SaveVideo", "pos": [1190, 620], "size": [531.188232421875, 398.43743896484375], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 35}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 29, "type": "LoadImage", "pos": [320, 620], "size": [430, 390], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [36]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 30, "type": "<PERSON>downNote", "pos": [-90, 620], "size": [390, 300], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["## Moonvalley Template Docs\n\n[Moonvalley video generation](http://docs.comfy.org/tutorials/api-nodes/moonvalley/moonvalley-video-generation)\n\n## API Node Docs\n[API Node](https://docs.comfy.org/tutorials/api-nodes/overview)\n## FAQ about login issues\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Ensure normal connectivity to our API services (VPN may be needed in some regions).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key)", "Task in progress: 119s"], "color": "#322", "bgcolor": "#533"}, {"id": 34, "type": "MoonvalleyImg2VideoNode", "pos": [770, 620], "size": [400, 360], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 36}], "outputs": [{"name": "video", "type": "VIDEO", "links": [35]}], "properties": {"Node name for S&R": "MoonvalleyImg2VideoNode"}, "widgets_values": ["A bedroom is transformed into a surreal underwater dreamscape, completely submerged in deep turquoise water where caustic light patterns ripple and dance across the walls, floor, and ceiling. In the center of this submerged room, a ballerina gracefully holds a pose en pointe, her form silhouetted against the ethereal blue glow. She moves with impossible slowness, transitioning between ballet positions as her sheer white dress billows around her in the gentle, silent currents. Tiny bubbles occasionally escape her lips, rising towards the undulating surface above, which serves as the room's only light source. The scene is both beautiful and unsettling, a silent performance in a liquid world where reality is suspended. Background: The ceiling is the agitated surface of the water, with ethereal beams of light filtering down and constantly shifting. A bed sits against the back wall, its form partially obscured by the moving shadows and light. Middleground: A silhouetted ballerina stands as the focal point, her arms outstretched in a graceful ballet pose. Her diaphanous dress flows weightlessly around her, moved by invisible underwater currents as she performs her silent dance. Foreground: The floor is covered in a moving tapestry of bright caustic reflections from the water's surface above. The ballerina's shadow and reflection are cast upon the floor, distorting and swaying with the light.", "gopro, bright, contrast, static, overexposed, bright, vignette, artifacts, still, noise, texture, scanlines, videogame, 360 camera, VR, transition, flare, saturation, distorted, warped, wide angle, contrast, saturated, vibrant, glowing, cross dissolve, texture, videogame, saturation, cheesy, ugly hands, mutated hands, mutant, disfigured, extra fingers, blown out, horrible, blurry, worst quality, bad, transition, dissolve, cross-dissolve, melt, fade in, fade out, wobbly, weird, low quality, plastic, stock footage, video camera, boring, static", "16:9 (1920 x 1080)", 7, 2328774152, "randomize", 100], "color": "#432", "bgcolor": "#653"}], "links": [[35, 34, 0, 27, 0, "VIDEO"], [36, 29, 0, 34, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8349250140851268, "offset": [211.16429396547588, -268.58170883064673]}, "frontendVersion": "1.24.0-1"}, "version": 0.4}