このノードは、条件設定コンテキスト内の特定のエリアを設定することで、条件情報を修正するために設計されています。指定された寸法と強度に基づいて、条件要素の精密な空間操作を可能にし、ターゲットを絞った調整と強化を実現します。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `CONDITIONING` | CONDITIONING | 修正される条件データ。空間調整を適用するための基礎として機能します。 |
| `width`   | `INT`      | 条件設定コンテキスト内で設定されるエリアの幅を指定し、調整の水平範囲に影響を与えます。 |
| `height`  | `INT`      | 設定されるエリアの高さを決定し、条件修正の垂直範囲に影響を与えます。 |
| `x`       | `INT`      | 設定されるエリアの水平開始点であり、条件設定コンテキスト内での調整の位置を決定します。 |
| `y`       | `INT`      | エリア調整の垂直開始点であり、条件設定コンテキスト内での位置を確立します。 |
| `strength`| `FLOAT`    | 指定されたエリア内での条件修正の強度を定義し、調整の影響を微調整するための制御を可能にします。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `CONDITIONING` | CONDITIONING | 指定されたエリア設定と調整を反映した修正済みの条件データ。 |
