{"id": "899481e8-5323-4aee-b3b4-faa5a3254f5d", "revision": 0, "last_node_id": 16, "last_link_id": 12, "nodes": [{"id": 14, "type": "LoadImage", "pos": [1150, 1210], "size": [342.5999755859375, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.webp", "image"]}, {"id": 10, "type": "OpenAIGPTImage1", "pos": [1530, 1210], "size": [400, 300], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 11}, {"name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [12]}], "properties": {"Node name for S&R": "OpenAIGPTImage1"}, "widgets_values": ["A cute cat sitting on a boat, holding a glass of champagne, with the vast ocean in the background, in the style of Studio Ghibli", 1011675199, "randomize", "high", "opaque", "1024x1024", 1]}, {"id": 15, "type": "SaveImage", "pos": [1960, 1210], "size": [315, 270], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 12}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 13, "type": "<PERSON>downNote", "pos": [780, 1210], "size": [336.1567077636719, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/openai/gpt-image-1)"], "color": "#432", "bgcolor": "#653"}, {"id": 16, "type": "<PERSON>downNote", "pos": [783.4011840820312, 1349.1826171875], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[11, 14, 0, 10, 0, "IMAGE"], [12, 10, 0, 15, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.****************, "offset": [-517.*************, -864.4137931034484]}, "frontendVersion": "1.18.5", "node_versions": {"comfy-core": "0.3.29"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}