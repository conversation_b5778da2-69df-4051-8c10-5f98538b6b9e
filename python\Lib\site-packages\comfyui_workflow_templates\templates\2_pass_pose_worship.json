{"last_node_id": 59, "last_link_id": 87, "nodes": [{"id": 5, "type": "EmptyLatentImage", "pos": [384.30364990234375, 456.1753234863281], "size": [320, 110], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [58]}], "properties": {"Node name for S&R": "EmptyLatentImage", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": [1024, 1024, 1]}, {"id": 11, "type": "ControlNetLoader", "pos": [-356.8330383300781, 745.0005493164062], "size": [294.1156005859375, 64.891845703125], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [50]}], "properties": {"Node name for S&R": "ControlNetLoader", "cnr_id": "comfy-core", "ver": "0.3.23", "models": [{"name": "control_v11p_sd15_openpose_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_openpose_fp16.safetensors", "directory": "controlnet"}]}, "widgets_values": ["control_v11p_sd15_openpose_fp16.safetensors"]}, {"id": 13, "type": "VAELoader", "pos": [-349.86773681640625, 563.1127319335938], "size": [300.7235107421875, 60.889339447021484], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [55, 64]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.23", "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"]}, {"id": 14, "type": "LatentUpscale", "pos": [340, 760], "size": [315, 130], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 62}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [79]}], "properties": {"Node name for S&R": "LatentUpscale", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": ["nearest-exact", 1536, 1536, "disabled"]}, {"id": 37, "type": "CLIPSetLastLayer", "pos": [0, 1130], "size": [315, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 66}], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [86, 87]}], "properties": {"Node name for S&R": "CLIPSetLastLayer", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": [-2]}, {"id": 38, "type": "CLIPSetLastLayer", "pos": [-358.06170654296875, 354.73944091796875], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 52}], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [68, 70]}], "properties": {"Node name for S&R": "CLIPSetLastLayer", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": [-2]}, {"id": 45, "type": "ControlNetApplyAdvanced", "pos": [385.0694274902344, 212.31187438964844], "size": [315, 186], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 69}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 71}, {"label": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 50}, {"label": "image", "name": "image", "type": "IMAGE", "link": 49}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [56]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [57]}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": [1, 0, 1]}, {"id": 46, "type": "LoadImage", "pos": [-353.276123046875, 867.865966796875], "size": [286.0498352050781, 346.3801574707031], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [49]}, {"label": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": ["input (3).png", "image"]}, {"id": 47, "type": "CheckpointLoaderSimple", "pos": [-358.06170654296875, 204.73941040039062], "size": [315, 98], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [59]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [52]}, {"label": "VAE", "name": "VAE", "type": "VAE", "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.23", "models": [{"name": "majicmixRealistic_v7.safetensors", "url": "https://civitai.com/api/download/models/176425?type=Model&format=SafeTensor&size=pruned&fp=fp16", "directory": "checkpoints"}]}, "widgets_values": ["majicmixRealistic_v7.safetensors"]}, {"id": 48, "type": "PreviewImage", "pos": [1242.7208251953125, 286.6751403808594], "size": [378.1794128417969, 342.7917175292969], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 53}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": []}, {"id": 49, "type": "VAEDecode", "pos": [1123.2257080078125, 197.05747985839844], "size": [210, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 60}, {"label": "vae", "name": "vae", "type": "VAE", "link": 55}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [53]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": []}, {"id": 50, "type": "K<PERSON><PERSON><PERSON>", "pos": [750.01904296875, 209.16563415527344], "size": [320, 416], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 59}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 56}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 57}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 58}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [60, 62]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": [200926035270122, "randomize", 20, 7, "dpmpp_2m", "karras", 1]}, {"id": 51, "type": "SaveImage", "pos": [1126.7664794921875, 844.6260986328125], "size": [416.9862976074219, 452.1423034667969], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 63}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": ["ComfyUI"]}, {"id": 52, "type": "VAEDecode", "pos": [1121.33935546875, 748.5275268554688], "size": [210, 46], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 80}, {"label": "vae", "name": "vae", "type": "VAE", "link": 64}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [63]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": []}, {"id": 53, "type": "CheckpointLoaderSimple", "pos": [-10, 940], "size": [315, 98], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [83]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [66]}, {"label": "VAE", "name": "VAE", "type": "VAE", "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.23", "models": [{"name": "japaneseStyleRealistic_v20.safetensors", "url": "https://civitai.com/api/download/models/85426?type=Model&format=SafeTensor&size=pruned&fp=fp16", "directory": "checkpoints"}]}, "widgets_values": ["japaneseStyleRealistic_v20.safetensors"]}, {"id": 54, "type": "CLIPTextEncode", "pos": [-13.613101959228516, 212.2252960205078], "size": [371.67529296875, 161.90484619140625], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 68}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [69]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": ["A serene portrait of a young Asian woman is captured in this photograph, set against a soft, diffused background. The subject's face is delicately illuminated by a subtle, natural light source, which accentuates the gentle curves of her features and imbues her skin with a warm, luminous glow. The lighting is carefully calibrated to minimize harsh shadows, resulting in a softly nuanced and realistic representation of the subject's complexion.\n\nThe camera is positioned at a slight angle, providing a subtle, three-quarter view of the subject's face. This perspective allows the viewer to appreciate the intricate details of her facial structure, while also creating a sense of intimacy and closeness.\n\nThe subject's dark hair cascades down her back, framing her face and adding depth to the composition. A bouquet of delicate, pale purple flowers is positioned in the foreground, their slender stems and petals artfully arranged to create a sense of movement and dynamism.\n\nThroughout the image, the photographer's attention to detail and mastery of lighting techniques are evident, resulting in a captivating and beautifully rendered portrait that invites the viewer to linger and explore the subject's serene, enigmatic expression."]}, {"id": 55, "type": "CLIPTextEncode", "pos": [-14.076957702636719, 419.5546569824219], "size": [371.67529296875, 161.90484619140625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 70}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [71]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"]}, {"id": 56, "type": "CLIPTextEncode", "pos": [340, 940], "size": [310.0844421386719, 159.99681091308594], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 86}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [77]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": ["A serene portrait of a young Asian woman is captured in this photograph, set against a soft, diffused background. The subject's face is delicately illuminated by a subtle, natural light source, which accentuates the gentle curves of her features and imbues her skin with a warm, luminous glow. The lighting is carefully calibrated to minimize harsh shadows, resulting in a softly nuanced and realistic representation of the subject's complexion.\n\nThe camera is positioned at a slight angle, providing a subtle, three-quarter view of the subject's face. This perspective allows the viewer to appreciate the intricate details of her facial structure, while also creating a sense of intimacy and closeness.\n\nThe subject's dark hair cascades down her back, framing her face and adding depth to the composition. A bouquet of delicate, pale purple flowers is positioned in the foreground, their slender stems and petals artfully arranged to create a sense of movement and dynamism.\n\nThroughout the image, the photographer's attention to detail and mastery of lighting techniques are evident, resulting in a captivating and beautifully rendered portrait that invites the viewer to linger and explore the subject's serene, enigmatic expression."]}, {"id": 57, "type": "CLIPTextEncode", "pos": [340, 1140], "size": [310.0844421386719, 159.99681091308594], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 87}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [78]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"]}, {"id": 58, "type": "K<PERSON><PERSON><PERSON>", "pos": [750.9767456054688, 754.1883544921875], "size": [315, 474], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 83}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 77}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 78}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 79}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [80]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.23"}, "widgets_values": [291031404803333, "randomize", 10, 6, "euler", "normal", 0.5]}, {"id": 59, "type": "<PERSON>downNote", "pos": [-368, 1368], "size": [336, 152], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [ControlNet - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#2-pass-pose-worship) — Overview\n> \n> [Pose ControlNet Usage - docs.comfy.org](https://docs.comfy.org/tutorials/controlnet/pose-controlnet-2-pass) — Explanation of concepts and step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[49, 46, 0, 45, 3, "IMAGE"], [50, 11, 0, 45, 2, "CONTROL_NET"], [52, 47, 1, 38, 0, "CLIP"], [53, 49, 0, 48, 0, "IMAGE"], [55, 13, 0, 49, 1, "VAE"], [56, 45, 0, 50, 1, "CONDITIONING"], [57, 45, 1, 50, 2, "CONDITIONING"], [58, 5, 0, 50, 3, "LATENT"], [59, 47, 0, 50, 0, "MODEL"], [60, 50, 0, 49, 0, "LATENT"], [62, 50, 0, 14, 0, "LATENT"], [63, 52, 0, 51, 0, "IMAGE"], [64, 13, 0, 52, 1, "VAE"], [66, 53, 1, 37, 0, "CLIP"], [68, 38, 0, 54, 0, "CLIP"], [69, 54, 0, 45, 0, "CONDITIONING"], [70, 38, 0, 55, 0, "CLIP"], [71, 55, 0, 45, 1, "CONDITIONING"], [77, 56, 0, 58, 1, "CONDITIONING"], [78, 57, 0, 58, 2, "CONDITIONING"], [79, 14, 0, 58, 3, "LATENT"], [80, 58, 0, 52, 0, "LATENT"], [83, 53, 0, 58, 0, "MODEL"], [86, 37, 0, 56, 0, "CLIP"], [87, 37, 0, 57, 0, "CLIP"]], "groups": [{"id": 1, "title": "ControlNet", "bounding": [-368, 672, 315.6295471191406, 646.8197021484375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Preview image", "bounding": [1112, 136, 553.7849731445312, 509.4486389160156], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Save image", "bounding": [1112, 672, 554.8024291992188, 646.3330078125], "color": "#88A", "font_size": 24, "flags": {}}, {"id": 4, "title": "1st pose controlNet", "bounding": [-368, 128, 1459.4947509765625, 513.32421875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "2nd Image to Image", "bounding": [-40, 672, 1127.2510986328125, 645.9175415039062], "color": "#88A", "font_size": 24, "flags": {}}], "config": {}, "extra": {"node_versions": {"comfy-core": "0.3.14"}}, "version": 0.4}