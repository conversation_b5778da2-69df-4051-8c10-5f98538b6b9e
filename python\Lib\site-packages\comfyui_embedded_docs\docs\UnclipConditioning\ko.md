
이 노드는 CLIP 비전 출력을 조건화 프로세스에 통합하도록 설계되었으며, 지정된 강도 및 노이즈 증강 매개변수에 따라 이러한 출력의 영향을 조정합니다. 시각적 컨텍스트로 조건화를 풍부하게 하여 생성 프로세스를 향상시킵니다.

## 입력

| 매개변수              | Comfy dtype            | 설명 |
|------------------------|------------------------|-------------|
| `conditioning`         | `CONDITIONING`         | CLIP 비전 출력을 추가할 기본 조건화 데이터로, 추가 수정의 기초가 됩니다. |
| `clip_vision_output`   | `CLIP_VISION_OUTPUT`   | CLIP 비전 모델의 출력으로, 조건화에 통합되는 시각적 컨텍스트를 제공합니다. |
| `strength`             | `FLOAT`                | 조건화에 대한 CLIP 비전 출력의 영향 강도를 결정합니다. |
| `noise_augmentation`   | `FLOAT`                | 조건화에 통합하기 전에 CLIP 비전 출력에 적용할 노이즈 증강 수준을 지정합니다. |

## 출력

| 매개변수             | Comfy dtype            | 설명 |
|-----------------------|------------------------|-------------|
| `conditioning`         | `CONDITIONING`         | 강도 및 노이즈 증강이 적용된 CLIP 비전 출력이 통합된 풍부한 조건화 데이터입니다. |
