このノードは、2つのコンディショニング入力を1つの出力に結合し、その情報を効果的に統合します。

## 入力

| パラメータ            | Comfy dtype        | 説明 |
|----------------------|--------------------|-------------|
| `conditioning_1`      | `CONDITIONING`     | 結合される最初のコンディショニング入力です。`conditioning_2`と同等の役割を果たします。 |
| `conditioning_2`      | `CONDITIONING`     | 結合される2番目のコンディショニング入力です。`conditioning_1`と同等に重要です。 |

## 出力

| パラメータ            | Comfy dtype        | 説明 |
|----------------------|--------------------|-------------|
| `conditioning`        | `CONDITIONING`     | `conditioning_1`と`conditioning_2`を結合した結果で、統合された情報を含んでいます。 |
