ConditioningAverage 노드는 두 세트의 조건 데이터를 혼합하여 지정된 강도에 따라 가중 평균을 적용하도록 설계되었습니다. 이 과정은 조건 영향의 동적 조정을 가능하게 하여 생성된 콘텐츠나 기능의 미세 조정을 촉진합니다.

## 입력

| 매개변수             | Comfy dtype        | 설명 |
|----------------------|--------------------|-------------|
| `conditioning_to`     | `CONDITIONING`     | 혼합이 적용될 주요 조건 데이터 세트를 나타냅니다. 이는 가중 평균 작업의 기반으로 작용합니다. |
| `conditioning_from`   | `CONDITIONING`     | 주요 세트에 혼합될 보조 조건 데이터 세트를 나타냅니다. 이 데이터는 지정된 강도에 따라 최종 출력에 영향을 미칩니다. |
| `conditioning_to_strength` | `FLOAT` | 주요 및 보조 조건 데이터 간의 혼합 강도를 결정하는 스칼라 값입니다. 이는 가중 평균의 균형에 직접적인 영향을 미칩니다. |

## 출력

| 매개변수            | Comfy dtype        | 설명 |
|----------------------|--------------------|-------------|
| `conditioning`        | `CONDITIONING`     | 주요 및 보조 조건 데이터를 혼합한 결과로, 가중 평균을 반영하는 새로운 조건 세트를 생성합니다. |
