{"id": "908d0bfb-e192-4627-9b57-147496e6e2dd", "revision": 0, "last_node_id": 44, "last_link_id": 63, "nodes": [{"id": 39, "type": "VAELoader", "pos": [-320, 470], "size": [270, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [58]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 40, "type": "DualCLIPLoader", "pos": [-320, 290], "size": [270, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [59]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "DualCLIPLoader", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 27, "type": "EmptySD3LatentImage", "pos": [-320, 630], "size": [270, 120], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "ConditioningZeroOut", "pos": [10, 470], "size": [200, 30], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 62}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [63]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 8, "type": "VAEDecode", "pos": [200, 470], "size": [210, 46], "flags": {"collapsed": true}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 52}, {"name": "vae", "type": "VAE", "link": 58}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [380, 110], "size": [640, 660], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": ["ComfyUI"]}, {"id": 38, "type": "UNETLoader", "pos": [-320, 150], "size": [270, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [61]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "UNETLoader", "models": [{"name": "flux1-schnell.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-schnell/resolve/main/flux1-schnell.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["flux1-schnell.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 31, "type": "K<PERSON><PERSON><PERSON>", "pos": [10, 520], "size": [315, 262], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 61}, {"name": "positive", "type": "CONDITIONING", "link": 60}, {"name": "negative", "type": "CONDITIONING", "link": 63}, {"name": "latent_image", "type": "LATENT", "link": 51}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [167447334682596, "randomize", 4, 1, "euler", "simple", 1]}, {"id": 41, "type": "CLIPTextEncodeFlux", "pos": [0, 150], "size": [340, 250], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 59}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [60, 62]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "CLIPTextEncodeFlux"}, "widgets_values": ["Cute retro mini car, pastel - colored 3D flowers overflowing from it, soft green background, minimalist and fresh style, high - precision rendering, spring - like vibrant atmosphere, delicate petal details, gentle color grading, whimsical and lovely scene", "Create a 3D - styled image: A cute, retro - looking mini car with soft, pastel - colored flowers (like daisies, pink blooms) overflowing from it. Set against a gentle green background, giving a fresh, spring - vibe. Make it look whimsical and delicate, like a sweet illustration.", 3.5]}, {"id": 43, "type": "<PERSON>downNote", "pos": [-910, 110], "size": [560, 360], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["**Diffusion Model**\n\n- [flux1-schnell.safetensors](https://huggingface.co/Comfy-Org/flux1-schnell/resolve/main/flux1-schnell.safetensors)\n\n**Text Encoder**\n\n- [clip_l.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/blob/main/clip_l.safetensors)\n\n- [t5xxl_fp16.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors) or [t5xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors)\n\n**VAE**\n\n- [ae.safetensors](https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors)\n\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   └─── flux1-schnell.safetensors\n│   ├── text_encoders/\n│   │   ├── clip_l.safetensors\n│   │   └─── t5xxl_fp16.safetensors # or t5xxl_fp8_e4m3fn_scaled.safetensors\n│   └── vae/\n│       └── ae.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [51, 27, 0, 31, 3, "LATENT"], [52, 31, 0, 8, 0, "LATENT"], [58, 39, 0, 8, 1, "VAE"], [59, 40, 0, 41, 0, "CLIP"], [60, 41, 0, 31, 1, "CONDITIONING"], [61, 38, 0, 31, 0, "MODEL"], [62, 41, 0, 42, 0, "CONDITIONING"], [63, 42, 0, 31, 2, "CONDITIONING"]], "groups": [{"id": 1, "title": "Step 1 - Load Models Here", "bounding": [-330, 80, 300, 460], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step 2 - Image Size", "bounding": [-330, 560, 300, 200], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step 3 - Prompt", "bounding": [-10, 80, 360, 333.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.9680000000000099, "offset": [995.4367874070468, 273.41433531597636]}, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}