#!/usr/bin/env python3
"""
清理 ComfyUI 相关缓存以解决插件加载问题
"""

import os
import shutil
import glob

def clear_python_cache():
    """清理 Python 缓存文件"""
    print("Clearing Python cache files...")
    
    # 清理 __pycache__ 目录
    pycache_dirs = []
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                pycache_dirs.append(os.path.join(root, dir_name))
    
    for cache_dir in pycache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"  Removed: {cache_dir}")
        except Exception as e:
            print(f"  Failed to remove {cache_dir}: {e}")
    
    # 清理 .pyc 文件
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"  Removed: {pyc_file}")
        except Exception as e:
            print(f"  Failed to remove {pyc_file}: {e}")

def clear_huggingface_cache():
    """清理 HuggingFace 缓存"""
    print("Clearing HuggingFace cache...")
    
    cache_paths = [
        '.cache/huggingface/transformers',
        '.cache/huggingface/hub',
    ]
    
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            try:
                shutil.rmtree(cache_path)
                print(f"  Removed: {cache_path}")
            except Exception as e:
                print(f"  Failed to remove {cache_path}: {e}")
        else:
            print(f"  Not found: {cache_path}")

def clear_comfyui_cache():
    """清理 ComfyUI 相关缓存"""
    print("Clearing ComfyUI cache...")
    
    cache_paths = [
        'ComfyUI/user/default/ComfyUI-Manager/cache',
        'ComfyUI/temp',
        'ComfyUI/output/.temp',
    ]
    
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            try:
                shutil.rmtree(cache_path)
                print(f"  Removed: {cache_path}")
            except Exception as e:
                print(f"  Failed to remove {cache_path}: {e}")
        else:
            print(f"  Not found: {cache_path}")

def restart_recommendation():
    """重启建议"""
    print("\n" + "="*50)
    print("缓存清理完成！")
    print("\n建议执行以下步骤：")
    print("1. 重启 ComfyUI 启动器")
    print("2. 如果问题仍然存在，请重启整个系统")
    print("3. 重新运行工作流测试")
    print("="*50)

def main():
    """主函数"""
    print("开始清理缓存...")
    print()
    
    clear_python_cache()
    print()
    
    clear_huggingface_cache()
    print()
    
    clear_comfyui_cache()
    print()
    
    restart_recommendation()

if __name__ == "__main__":
    main()
