
**노드 기능:** `Save Image-이미지 저장` 노드는 주로 ComfyUI의 **output** 폴더에 이미지를 저장하는 데 사용됩니다. 중간 과정에서 이미지를 저장하지 않고 미리보기만 하려면 `Preview Image-이미지 미리보기` 노드를 사용할 수 있습니다.
기본 저장 위치: `ComfyUI/output/`

## 입력

| 매개변수          | 데이터 유형 | 설명                                                                                                                 |
| ----------------- | ----------- | -------------------------------------------------------------------------------------------------------------------- |
| `images`          | `IMAGE`     | 저장할 이미지입니다. 이 매개변수는 디스크에 처리 및 저장될 이미지 데이터를 직접 포함하므로 매우 중요합니다.          |
| `filename_prefix` | STRING      | `ComfyUI/output/` 폴더에 저장될 이미지의 파일 이름 접두사입니다. 기본값은 `ComfyUI`이지만 사용자 지정할 수 있습니다. |

## 우클릭 메뉴 옵션

이미지 생성이 완료된 후 해당 메뉴에서 우클릭하면 다음과 같은 노드별 옵션과 기능이 제공됩니다:

| 옵션 이름                | 기능                           |
| ------------------------ | ------------------------------ |
| `Save Image-이미지 저장` | 이미지를 로컬에 저장           |
| `Copy Image-이미지 복사` | 이미지를 클립보드에 복사       |
| `Open Image-이미지 열기` | 브라우저 새 탭에서 이미지 열기 |

저장된 이미지는 일반적으로 PNG 형식이며 모든 이미지 생성 데이터가 포함됩니다. 해당 워크플로우를 재생성에 사용하려면 해당 이미지를 ComfyUI에 로드하기만 하면 워크플로우가 로드됩니다.
