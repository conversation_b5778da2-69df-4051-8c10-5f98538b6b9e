
이 노드는 StableZero123 모델에서 사용하기 위해 데이터를 처리하고 조건을 설정하도록 설계되었습니다. 이 모델에 최적화된 특정 형식으로 입력을 준비하는 데 중점을 둡니다.

## 입력

| 매개변수             | Comfy dtype        | 설명 |
|----------------------|--------------------|-------------|
| `clip_vision`         | `CLIP_VISION`      | 모델의 요구 사항에 맞춰 시각 데이터를 처리하여 모델의 시각적 컨텍스트 이해를 향상시킵니다. |
| `init_image`          | `IMAGE`            | 모델의 초기 이미지 입력으로 사용되어, 추가 이미지 기반 작업의 기준을 설정합니다. |
| `vae`                 | `VAE`              | 변분 오토인코더 출력을 통합하여, 모델이 이미지를 생성하거나 수정할 수 있도록 지원합니다. |
| `width`               | `INT`              | 출력 이미지의 너비를 지정하여, 모델의 필요에 따라 동적으로 크기를 조정할 수 있습니다. |
| `height`              | `INT`              | 출력 이미지의 높이를 결정하여, 출력 차원을 사용자 정의할 수 있습니다. |
| `batch_size`          | `INT`              | 한 번에 처리되는 이미지 수를 제어하여, 계산 효율성을 최적화합니다. |
| `elevation`           | `FLOAT`            | 3D 모델 렌더링을 위한 고도 각도를 조정하여, 모델의 공간적 이해를 향상시킵니다. |
| `azimuth`             | `FLOAT`            | 3D 모델 시각화를 위한 방위각을 수정하여, 모델의 방향 인식을 개선합니다. |

## 출력

| 매개변수     | 데이터 유형 | 설명 |
|---------------|--------------|-------------|
| `positive`    | `CONDITIONING` | 긍정적 조건 벡터를 생성하여, 모델의 긍정적 기능 강화를 돕습니다. |
| `negative`    | `CONDITIONING` | 부정적 조건 벡터를 생성하여, 모델이 특정 기능을 피하도록 지원합니다. |
| `latent`      | `LATENT`     | 잠재 표현을 생성하여, 데이터에 대한 모델의 깊은 통찰을 촉진합니다. |
