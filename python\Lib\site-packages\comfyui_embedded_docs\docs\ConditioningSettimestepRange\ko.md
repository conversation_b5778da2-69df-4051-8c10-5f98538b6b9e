이 노드는 특정 시간 단계 범위를 설정하여 컨디셔닝의 시간적 측면을 조정하도록 설계되었습니다. 이를 통해 컨디셔닝 프로세스의 시작 및 종료 지점을 정밀하게 제어할 수 있어 보다 목표 지향적이고 효율적인 생성을 가능하게 합니다.

## 입력

| 매개변수       | 데이터 유형  | 설명                                                                                                                                           |
| -------------- | ------------ | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| `CONDITIONING` | CONDITIONING | 컨디셔닝 입력은 생성 프로세스의 현재 상태를 나타내며, 이 노드는 특정 시간 단계 범위를 설정하여 이를 수정합니다.                                |
| `start`        | `FLOAT`      | 시작 매개변수는 전체 생성 프로세스의 백분율로 시간 단계 범위의 시작을 지정하여 컨디셔닝 효과가 시작되는 시점을 세밀하게 제어할 수 있게 합니다. |
| `end`          | `FLOAT`      | 종료 매개변수는 시간 단계 범위의 끝점을 백분율로 정의하여 컨디셔닝 효과의 지속 시간과 종료를 정밀하게 제어할 수 있게 합니다.                   |

## 출력

| 매개변수       | 데이터 유형  | 설명                                                                                           |
| -------------- | ------------ | ---------------------------------------------------------------------------------------------- |
| `CONDITIONING` | CONDITIONING | 출력은 지정된 시간 단계 범위가 적용된 수정된 컨디셔닝으로, 추가 처리나 생성을 위해 준비됩니다. |
