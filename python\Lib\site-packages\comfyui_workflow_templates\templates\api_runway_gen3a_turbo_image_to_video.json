{"id": "cbeeb8a3-8519-434e-a8c4-874227569ca0", "revision": 0, "last_node_id": 7, "last_link_id": 5, "nodes": [{"id": 1, "type": "<PERSON>downNote", "pos": [1070, 1420], "size": [390, 320], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 4, "type": "LoadImage", "pos": [1510, 1420], "size": [274.*********, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Steampunk.png", "image"]}, {"id": 6, "type": "SaveVideo", "pos": [2240, 1420], "size": [374, 460], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 5}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 7, "type": "RunwayImageToVideoNodeGen3a", "pos": [1810, 1420], "size": [410, 380], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "start_frame", "type": "IMAGE", "link": 4}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [5]}], "properties": {"Node name for S&R": "RunwayImageToVideoNodeGen3a"}, "widgets_values": ["Steampunk girl on flying airship deck, wind blowing her hair and dress. She shifts her stance and raises a brass telescope to her eye. Skirt and gear accessories flutter dynamically. A giant gear-decorated airship glides behind her through the sky, partially obscured by clouds.  Steam rises in the distance. Smooth cinematic dolly-in with slight orbit. Warm golden light, dreamy steampunk mood. High detail, stable animation, no flicker.", 5, "1280:768", 513022171, "randomize", "Result URL: https://dnznrvs05pmza.cloudfront.net/666248bf-93c9-40a2-b051-efcd4abf6d4a.mp4?_jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXlIYXNoIjoiZGZkMmNkNDMxNTYxZWRmMCIsImJ1Y2tldCI6InJ1bndheS10YXNrLWFydGlmYWN0cyIsInN0YWdlIjoicHJvZCIsImV4cCI6MTc0ODMwNDAwMH0.DMREQKShhzgQEjkiAGWkdr5Q96X-W2tJH3swJOUEHgw"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 4, 0, 7, 0, "IMAGE"], [5, 7, 0, 6, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [-1210.6423660664404, -1182.020513246768]}, "frontendVersion": "1.21.0"}, "version": 0.4}