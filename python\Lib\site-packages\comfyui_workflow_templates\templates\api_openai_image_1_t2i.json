{"id": "899481e8-5323-4aee-b3b4-faa5a3254f5d", "revision": 0, "last_node_id": 16, "last_link_id": 13, "nodes": [{"id": 14, "type": "SaveImage", "pos": [2240, 1230], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 11}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 16, "type": "LoadImage", "pos": [1519.1990966796875, 1223.9404296875], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [12]}, {"name": "MASK", "type": "MASK", "links": [13]}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 10, "type": "OpenAIGPTImage1", "pos": [1820.525390625, 1223.504150390625], "size": [400, 300], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 12}, {"name": "mask", "shape": 7, "type": "MASK", "link": 13}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11]}], "properties": {"Node name for S&R": "OpenAIGPTImage1"}, "widgets_values": ["On a sunny day, a delicate Eiffel Tower made entirely of felt and wool, with rich details, highly realistic structure but with a cute, slightly exaggerated cartoonish style. The scene is filled with felt-textured small cars, pedestrians, people riding bicycles, and little dogs. In the background, there is a blue sky with soft clouds and a blurred, gentle Paris city skyline. The overall tone is bright, warm, and soft. The atmosphere is full of childlike fun and handcrafted charm, with every object resembling soft plush toys. The viewpoint is slightly low-angle, making the Eiffel Tower appear tall yet friendly.\n", 667955921, "randomize", "high", "opaque", "1024x1024", 1]}, {"id": 13, "type": "<PERSON>downNote", "pos": [1150, 1210], "size": [340.91766357421875, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1) ｜ [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/openai/gpt-image-1)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 15, "type": "<PERSON>downNote", "pos": [1156.906005859375, 1352.0977783203125], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[11, 10, 0, 14, 0, "IMAGE"], [12, 16, 0, 10, 0, "IMAGE"], [13, 16, 1, 10, 1, "MASK"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.227023724491937, "offset": [-952.0515659992724, -980.6401949887988]}, "frontendVersion": "1.18.5", "node_versions": {"comfy-core": "0.3.29"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}