{"id": "2a6b0ab5-480c-4acc-a44f-8f9b5036a76b", "revision": 0, "last_node_id": 65, "last_link_id": 130, "nodes": [{"id": 9, "type": "SaveImage", "pos": [560.6922607421875, 101.67475128173828], "size": [845.74560546875, 898.2359619140625], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33"}, "widgets_values": ["ComfyUI"]}, {"id": 58, "type": "ConditioningZeroOut", "pos": [-246.95835876464844, 348.4148864746094], "size": [317.4000244140625, 26], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 119}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [124]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 61, "type": "ControlNetApplyAdvanced", "pos": [-254.43515014648438, 403.69256591796875], "size": [270, 186], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 123}, {"name": "negative", "type": "CONDITIONING", "link": 124}, {"name": "control_net", "type": "CONTROL_NET", "link": 125}, {"name": "image", "type": "IMAGE", "link": 127}, {"name": "vae", "shape": 7, "type": "VAE", "link": 122}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [128]}, {"name": "negative", "type": "CONDITIONING", "links": [129]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.7000000000000002, 0, 1]}, {"id": 45, "type": "LoadImage", "pos": [-712.6139526367188, 420.5608825683594], "size": [373.2554016113281, 336.6703796386719], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [127]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["vcmd_create_dragon_mascot_characters_that_best_suitable_for_Sin_5ba6beab-2ad7-4810-997e-387c27bea297.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 46, "type": "ControlNetLoader", "pos": [-711.37744140625, 259.4809265136719], "size": [380.7906799316406, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [125]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader", "models": [{"name": "sd3.5_large_controlnet_depth.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_depth.safetensors?download=true", "directory": "controlnet"}]}, "widgets_values": ["sd3.5_large_controlnet_depth.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-716.0094604492188, 90.00731658935547], "size": [387.85345458984375, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [112]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [117]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [8, 122]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "sd3.5_large_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/sd3.5_large_fp8_scaled.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["sd3.5_large_fp8_scaled.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 56, "type": "K<PERSON><PERSON><PERSON>", "pos": [173.2810516357422, 384.3057556152344], "size": [306.7953186035156, 262], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 112}, {"name": "positive", "type": "CONDITIONING", "link": 128}, {"name": "negative", "type": "CONDITIONING", "link": 129}, {"name": "latent_image", "type": "LATENT", "link": 115}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [0, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 33, "type": "EmptySD3LatentImage", "pos": [-708.1129150390625, 827.324462890625], "size": [300.9447021484375, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [115]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1], "color": "#322", "bgcolor": "#533"}, {"id": 57, "type": "CLIPTextEncode", "pos": [-256.62030029296875, 95.26077270507812], "size": [400, 200], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 117}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [119, 123]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["hairy dragon stuffed toy with light green color in a fairy tale background, fluffy hair, standing with 2 legs"]}, {"id": 8, "type": "VAEDecode", "pos": [178.41342163085938, 239.4571075439453], "size": [278.8823547363281, 46.5799446105957], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 116}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [13]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 64, "type": "<PERSON>downNote", "pos": [-1096.2403564453125, 393.0308532714844], "size": [338.3355407714844, 162.93470764160156], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Please use the preprocessed images, or install [comfyui_controlnet_aux](https://github.com/Fannovel16/comfyui_controlnet_aux) to preprocess the images.\n\n---\n\n请使用经过预处理的图像，或者安装 [comfyui_controlnet_aux](https://github.com/Fannovel16/comfyui_controlnet_aux) 来对图像进行预处理"], "color": "#432", "bgcolor": "#653"}, {"id": 65, "type": "<PERSON>downNote", "pos": [-1094.0626220703125, 54.434391021728516], "size": [339.5606689453125, 262.63995361328125], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "Model links", "properties": {}, "widgets_values": ["Please visit [stable-diffusion-3.5-controlnets](https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets) and log in to agree to the protocol to get permission to download.\n\n访问链接并同意对应的仓库协议才能具有下载权限\n\n---\n\n[sd3.5_large_controlnet_depth.safetensors](https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_depth.safetensors) \nSave in  **ComfyUI/models/controlnet** \n\n\n[sd3.5_large_fp8_scaled.safetensors](https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/sd3.5_large_fp8_scaled.safetensors?download=true)\nSave in **ComfyUI/models/checkpoints** \n"], "color": "#432", "bgcolor": "#653"}], "links": [[8, 4, 2, 8, 1, "VAE"], [13, 8, 0, 9, 0, "IMAGE"], [112, 4, 0, 56, 0, "MODEL"], [115, 33, 0, 56, 3, "LATENT"], [116, 56, 0, 8, 0, "LATENT"], [117, 4, 1, 57, 0, "CLIP"], [119, 57, 0, 58, 0, "CONDITIONING"], [122, 4, 2, 61, 4, "VAE"], [123, 57, 0, 61, 0, "CONDITIONING"], [124, 58, 0, 61, 1, "CONDITIONING"], [125, 46, 0, 61, 2, "CONTROL_NET"], [127, 45, 0, 61, 3, "IMAGE"], [128, 61, 0, 56, 1, "CONDITIONING"], [129, 61, 1, 56, 2, "CONDITIONING"]], "groups": [{"id": 1, "title": "Load models here", "bounding": [-726.0094604492188, 16.407316207885742, 407.85345458984375, 311.0736083984375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Need preprocessed image", "bounding": [-722.6139526367188, 346.96087646484375, 402.7755432128906, 424.4949951171875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [1580.579103493252, 75.42708476368196]}, "frontendVersion": "1.16.9"}, "version": 0.4}