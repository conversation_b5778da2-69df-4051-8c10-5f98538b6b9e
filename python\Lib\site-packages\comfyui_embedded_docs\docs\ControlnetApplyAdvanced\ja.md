このノードは新しいComfyUIのバージョンでAppliquer ControlNetに改名され、旧バージョンのAppliquer ControlNet (OLD)を置き換えました。以前のAppliquer ControlNet (OLD)は現在、ある程度有効な状態に似ているため、このノードの最新のドキュメントは `Appliquer ControlNet`に移動されました。

このノードは、画像とコントロールネットモデルに基づいて、条件データに高度なコントロールネット変換を適用します。生成されたコンテンツに対するコントロールネットの影響を微調整し、条件のより正確で多様な修正を可能にします。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `positive` | `CONDITIONING` | コントロールネット変換が適用されるポジティブな条件データ。生成されたコンテンツで強化または維持したい属性や特徴を表します。 |
| `negative` | `CONDITIONING` | ネガティブな条件データで、生成されたコンテンツから減少または除去したい属性や特徴を表します。このデータにもコントロールネット変換が適用され、コンテンツの特性をバランスよく調整します。 |
| `control_net` | `CONTROL_NET` | 条件データに対する特定の調整と強化を定義するために重要なコントロールネットモデル。参照画像と強度パラメータを解釈して変換を適用し、ポジティブおよびネガティブな条件データの属性を変更することで最終出力に大きな影響を与えます。 |
| `image` | `IMAGE` | コントロールネット変換の参照として機能する画像。コントロールネットが条件データに対して行う調整に影響を与え、特定の特徴の強化または抑制を導きます。 |
| `strength` | `FLOAT` | 条件データに対するコントロールネットの影響の強度を決定するスカラー値。値が高いほど、より顕著な調整が行われます。 |
| `start_percent` | `FLOAT` | コントロールネットの効果の開始パーセンテージで、指定された範囲内での変換の段階的な適用を可能にします。 |
| `end_percent` | `FLOAT` | コントロールネットの効果の終了パーセンテージで、変換が適用される範囲を定義します。これにより、調整プロセスをより微細に制御できます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `positive` | `CONDITIONING` | コントロールネット変換の適用後に修正されたポジティブな条件データで、入力パラメータに基づいて行われた強化を反映します。 |
| `negative` | `CONDITIONING` | コントロールネット変換の適用後に修正されたネガティブな条件データで、入力パラメータに基づいて特定の特徴の抑制または除去を反映します。 |
