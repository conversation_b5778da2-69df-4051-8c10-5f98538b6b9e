# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/packet_factory.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(mediapipe/framework/packet_factory.proto\x12\tmediapipe\"\"\n\x14PacketFactoryOptions*\n\x08\xa0\x9c\x01\x10\x80\x80\x80\x80\x02\"\x95\x01\n\x13PacketFactoryConfig\x12\x16\n\x0epacket_factory\x18\x01 \x01(\t\x12\x1a\n\x12output_side_packet\x18\x02 \x01(\t\x12\x18\n\x0f\x65xternal_output\x18\xea\x07 \x01(\t\x12\x30\n\x07options\x18\x03 \x01(\x0b\x32\x1f.mediapipe.PacketFactoryOptions\"E\n\x13PacketManagerConfig\x12.\n\x06packet\x18\x01 \x03(\x0b\x32\x1e.mediapipe.PacketFactoryConfigB0\n\x1a\x63om.google.mediapipe.protoB\x12PacketFactoryProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.packet_factory_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB\022PacketFactoryProto'
  _globals['_PACKETFACTORYOPTIONS']._serialized_start=55
  _globals['_PACKETFACTORYOPTIONS']._serialized_end=89
  _globals['_PACKETFACTORYCONFIG']._serialized_start=92
  _globals['_PACKETFACTORYCONFIG']._serialized_end=241
  _globals['_PACKETMANAGERCONFIG']._serialized_start=243
  _globals['_PACKETMANAGERCONFIG']._serialized_end=312
# @@protoc_insertion_point(module_scope)
