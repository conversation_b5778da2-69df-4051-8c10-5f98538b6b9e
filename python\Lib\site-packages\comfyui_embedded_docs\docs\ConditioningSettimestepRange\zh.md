此节点设计用于通过设置特定时间步范围来调整条件的时序方面。它允许对条件过程的起始和结束点进行精确控制，从而实现更有针对性的高效生成。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `条件` | `CONDITIONING` | 条件输入代表生成过程的当前状态，此节点通过设置特定时间步范围来修改它。 |
| `开始` | `FLOAT` | `start`参数指定时间步范围的起始点，作为总生成过程的百分比，允许对条件效果开始的时间进行微调控制。 |
| `结束` | `FLOAT` | `end`参数定义时间步范围的终点作为百分比，使您能够精确控制条件效果的持续时间和结束。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `条件` | `CONDITIONING` | 输出是应用了指定时间步范围的修改后条件，已准备好进行进一步的处理或生成。 |
