{"last_node_id": 37, "last_link_id": 58, "nodes": [{"id": 33, "type": "CLIPTextEncode", "pos": [390, 400], "size": [422.85, 164.31], "flags": {"collapsed": true}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 54, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [55], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 27, "type": "EmptySD3LatentImage", "pos": [471, 455], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "shape": 3, "links": [51], "slot_index": 0}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1], "color": "#323", "bgcolor": "#535"}, {"id": 8, "type": "VAEDecode", "pos": [1151, 195], "size": [210, 46], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 52}, {"name": "vae", "type": "VAE", "link": 46}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1375, 194], "size": [985.3, 1060.38], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 31, "type": "K<PERSON><PERSON><PERSON>", "pos": [816, 192], "size": [315, 262], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 47}, {"name": "positive", "type": "CONDITIONING", "link": 58}, {"name": "negative", "type": "CONDITIONING", "link": 55}, {"name": "latent_image", "type": "LATENT", "link": 51}], "outputs": [{"name": "LATENT", "type": "LATENT", "shape": 3, "links": [52], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [173805153958730, "randomize", 4, 1, "euler", "simple", 1]}, {"id": 30, "type": "CheckpointLoaderSimple", "pos": [48, 192], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [47], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "shape": 3, "links": [45, 54], "slot_index": 1}, {"name": "VAE", "type": "VAE", "shape": 3, "links": [46], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "flux1-schnell-fp8.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-schnell/resolve/main/flux1-schnell-fp8.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["flux1-schnell-fp8.safetensors"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [384, 192], "size": [422.85, 164.31], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 45}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [58], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a bottle with a beautiful rainbow galaxy inside it on top of a wooden table in the middle of a modern kitchen beside a plate of vegetables and mushrooms and a wine glasse that contains a planet earth with a plate with a half eaten apple pie on it"], "color": "#232", "bgcolor": "#353"}, {"id": 34, "type": "Note", "pos": [831, 501], "size": [282.86, 164.08], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["Note that Flux dev and schnell do not have any negative prompt so CFG should be set to 1.0. Setting CFG to 1.0 means the negative prompt is ignored.\n\nThe schnell model is a distilled model that can generate a good image with only 4 steps."], "color": "#432", "bgcolor": "#653"}, {"id": 37, "type": "<PERSON>downNote", "pos": [45, 345], "size": [225, 60], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-schnell-1)"], "color": "#432", "bgcolor": "#653"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [45, 30, 1, 6, 0, "CLIP"], [46, 30, 2, 8, 1, "VAE"], [47, 30, 0, 31, 0, "MODEL"], [51, 27, 0, 31, 3, "LATENT"], [52, 31, 0, 8, 0, "LATENT"], [54, 30, 1, 33, 0, "CLIP"], [55, 33, 0, 31, 2, "CONDITIONING"], [58, 6, 0, 31, 1, "CONDITIONING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [0.68, 1.83]}}, "version": 0.4}