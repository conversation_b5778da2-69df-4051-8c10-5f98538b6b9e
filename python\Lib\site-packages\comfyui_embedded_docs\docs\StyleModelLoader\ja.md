このノードは、`ComfyUI/models/style_models` フォルダーにあるモデルを検出し、
また、extra_model_paths.yaml ファイルで設定された追加パスのモデルも読み込みます。
場合によっては、**ComfyUIのインターフェースを更新する**必要があるかもしれませんので、対応するフォルダー内のモデルファイルを読み込むことができます。

StyleModelLoaderノードは、指定されたパスからスタイルモデルをロードするために設計されています。これにより、特定の芸術スタイルを画像に適用するためのスタイルモデルを取得し、初期化するプロセスが容易になり、ロードされたスタイルモデルに基づいて視覚出力をカスタマイズすることが可能になります。

## 入力

| フィールド名          | Comfy dtype     | Python dtype | 説明                                                                                   |
|----------------------|-----------------|--------------|---------------------------------------------------------------------------------------|
| `style_model_name`   | COMBO[STRING] | `str`        | ロードするスタイルモデルの名前を指定します。この名前は、事前定義されたディレクトリ構造内でモデルファイルを特定するために使用され、ユーザー入力やアプリケーションのニーズに基づいて異なるスタイルモデルを動的にロードすることを可能にします。 |

## 出力

| フィールド名     | Comfy dtype   | Python dtype  | 説明                                                                                   |
|-----------------|---------------|---------------|---------------------------------------------------------------------------------------|
| `style_model`   | `STYLE_MODEL` | `StyleModel`  | ロードされたスタイルモデルを返し、画像にスタイルを適用する準備が整います。これにより、異なる芸術スタイルを適用することで視覚出力を動的にカスタマイズすることが可能になります。 |
