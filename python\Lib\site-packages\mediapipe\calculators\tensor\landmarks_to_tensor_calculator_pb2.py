# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/tensor/landmarks_to_tensor_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nAmediapipe/calculators/tensor/landmarks_to_tensor_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xa7\x02\n\"LandmarksToTensorCalculatorOptions\x12K\n\nattributes\x18\x01 \x03(\x0e\x32\x37.mediapipe.LandmarksToTensorCalculatorOptions.Attribute\x12\x16\n\x07\x66latten\x18\x02 \x01(\x08:\x05\x66\x61lse\">\n\tAttribute\x12\x05\n\x01X\x10\x00\x12\x05\n\x01Y\x10\x01\x12\x05\n\x01Z\x10\x02\x12\x0e\n\nVISIBILITY\x10\x03\x12\x0c\n\x08PRESENCE\x10\x04\x32\\\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xfb\xa6\xa1\xbc\x01 \x01(\x0b\x32-.mediapipe.LandmarksToTensorCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.tensor.landmarks_to_tensor_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_LANDMARKSTOTENSORCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_LANDMARKSTOTENSORCALCULATOROPTIONS']._serialized_start=119
  _globals['_LANDMARKSTOTENSORCALCULATOROPTIONS']._serialized_end=414
  _globals['_LANDMARKSTOTENSORCALCULATOROPTIONS_ATTRIBUTE']._serialized_start=258
  _globals['_LANDMARKSTOTENSORCALCULATOROPTIONS_ATTRIBUTE']._serialized_end=320
# @@protoc_insertion_point(module_scope)
