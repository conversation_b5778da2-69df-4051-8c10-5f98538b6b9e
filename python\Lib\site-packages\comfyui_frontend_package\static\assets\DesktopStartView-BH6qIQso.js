import { defineComponent, openBlock, createBlock, withCtx, createVNode, unref } from "./vendor-vue-H4UETSFK.js";
import { script$29 as script } from "./vendor-primevue-DRGUzLTK.js";
import { _sfc_main as _sfc_main$1 } from "./BaseViewTemplate-CpCvKU12.js";
import "./index-CWzmkThr.js";
import "./vendor-vue-i18n-DiivlA3w.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "DesktopStartView",
  setup(__props) {
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$1, { dark: "" }, {
        default: withCtx(() => [
          createVNode(unref(script), { class: "m-8 w-48 h-48" })
        ]),
        _: 1
      });
    };
  }
});
export {
  _sfc_main as default
};
//# sourceMappingURL=DesktopStartView-BH6qIQso.js.map
