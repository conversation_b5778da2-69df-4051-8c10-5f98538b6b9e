{"id": "7cbcec68-7fa6-47bb-a38a-da689949a001", "revision": 0, "last_node_id": 178, "last_link_id": 281, "nodes": [{"id": 39, "type": "VAELoader", "pos": [-70, 1140], "size": [337.76861572265625, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [223]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "FluxKontextImageScale", "pos": [300, 1230], "size": [270, 30], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 257}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [222]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale"}, "widgets_values": []}, {"id": 144, "type": "SaveImage", "pos": [590, 1820], "size": [390, 390], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 281}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41"}, "widgets_values": ["ComfyUI"]}, {"id": 157, "type": "SaveImage", "pos": [1010, 1820], "size": [390, 380], "flags": {}, "order": 13, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 280}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41"}, "widgets_values": ["ComfyUI"]}, {"id": 150, "type": "ImageStitch", "pos": [300, 1300], "size": [270, 150], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 255}, {"name": "image2", "shape": 7, "type": "IMAGE", "link": 256}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [257, 273]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ImageStitch"}, "widgets_values": ["right", true, 0, "white"]}, {"id": 172, "type": "PreviewImage", "pos": [300, 1500], "size": [270, 300], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 273}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 133, "type": "LoadImage", "pos": [-70, 1250], "size": [340, 350], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [255]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 124, "type": "VAEEncode", "pos": [300, 1140], "size": [270, 50], "flags": {"collapsed": false}, "order": 9, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 222}, {"name": "vae", "type": "VAE", "link": 223}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [277]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 175, "type": "workflow>FLUX.1 Kontext Image Edit", "pos": [590, 1140], "size": [400, 546], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "LATENT", "type": "LATENT", "link": 277}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [281]}, {"name": "LATENT", "type": "LATENT", "links": [279]}], "properties": {"Node name for S&R": "workflow>FLUX.1 Kontext Image Edit", "models": [{"name": "flux1-dev-kontext_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors", "directory": "diffusion_models"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}, {"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": [386100423184929, "fixed", 20, 1, "euler", "simple", 1, 2.5, "ComfyUI", "Place both cute 3D characters together in one scene where they are hugging", "", "flux1-dev-kontext_fp8_scaled.safetensors", "default", "t5xxl_fp8_e4m3fn_scaled.safetensors", "clip_l.safetensors", "flux", "default", "ae.safetensors"]}, {"id": 152, "type": "LoadImage", "pos": [-70, 1650], "size": [350, 400], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [256]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_00240_.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 174, "type": "<PERSON>downNote", "pos": [590, 900], "size": [820, 180], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "✨ New ComfyUI feature for Flux.1 Kontext Dev", "properties": {}, "widgets_values": ["We have added an **Edit** button to the **Selection Toolbox** of the node for **FLUX.1 Kontext Image Edit** support. When clicked, it quickly adds a **FLUX.1 Kontext Image Edit** group node to the Latent output of your current workflow. This enables an interactive editing experience where you can:\n\n- Create multiple editing iterations, each preserved as a separate node\n- Easily branch off from any previous edit point to explore different creative directions\n- Return to any earlier version and start a new editing branch\n- Modify parameters in earlier nodes and automatically update all downstream edits\n- Execute or re-execute any branch of edits at any time\n- When you want to maintain the effect of the corresponding branch, please set the seed of the corresponding group node to fixed.\n\n\nThis workflow mirrors the iterative nature of LLM conversations, but with the added advantage of visual editing and the ability to maintain multiple parallel editing paths."], "color": "#322", "bgcolor": "#533"}, {"id": 149, "type": "<PERSON>downNote", "pos": [-610, 1130], "size": [510, 480], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "Model links", "properties": {}, "widgets_values": ["[tutorial](http://docs.comfy.org/tutorials/flux/flux-1-kontext-dev) | [教程](http://docs.comfy.org/zh-CN/tutorials/flux/flux-1-kontext-dev)\n\nFor reference:\n- **fp8_scaled**: Requires about 20GB of VRAM.\n- **Original**: Requires about 32GB of VRAM.\n\nTested on an A100 GPU\n\n## Model links\n\n**diffusion model**\n\n- [flux1-dev-kontext_fp8_scaled.safetensors](https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors)\n\n**vae**\n\n- [ae.safetensors](https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/blob/main/split_files/vae/ae.safetensors)\n\n**text encoder**\n\n- [clip_l.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/blob/main/clip_l.safetensors)\n- [t5xxl_fp16.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors) or [t5xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors)\n\nModel Storage Location\n\n```\n📂 ComfyUI/\n├── 📂 models/\n│   ├── 📂 diffusion_models/\n│   │   └── flux1-dev-kontext_fp8_scaled.safetensors\n│   ├── 📂 vae/\n│   │   └── ae.safetensor\n│   └── 📂 text_encoders/\n│       ├── clip_l.safetensors\n│       └── t5xxl_fp16.safetensors or t5xxl_fp8_e4m3fn_scaled.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 148, "type": "<PERSON>downNote", "pos": [-610, 1660], "size": [510, 390], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "Flux Kontext Prompt Techniques", "properties": {}, "widgets_values": ["\n## Flux Kontext Prompt Techniques\n\n### 1. Basic Modifications\n- Simple and direct: `\"Change the car color to red\"`\n- Maintain style: `\"Change to daytime while maintaining the same style of the painting\"`\n\n### 2. Style Transfer\n**Principles:**\n- Clearly name style: `\"Transform to Bauhaus art style\"`\n- Describe characteristics: `\"Transform to oil painting with visible brushstrokes, thick paint texture\"`\n- Preserve composition: `\"Change to Bauhaus style while maintaining the original composition\"`\n\n### 3. Character Consistency\n**Framework:**\n- Specific description: `\"The woman with short black hair\"` instead of \"she\"\n- Preserve features: `\"while maintaining the same facial features, hairstyle, and expression\"`\n- Step-by-step modifications: Change background first, then actions\n\n### 4. Text Editing\n- Use quotes: `\"Replace 'joy' with 'BFL'\"`\n- Maintain format: `\"Replace text while maintaining the same font style\"`\n\n## Common Problem Solutions\n\n### Character Changes Too Much\n❌ Wrong: `\"Transform the person into a Viking\"`\n✅ Correct: `\"Change the clothes to be a viking warrior while preserving facial features\"`\n\n### Composition Position Changes\n❌ Wrong: `\"Put him on a beach\"`\n✅ Correct: `\"Change the background to a beach while keeping the person in the exact same position, scale, and pose\"`\n\n### Style Application Inaccuracy\n❌ Wrong: `\"Make it a sketch\"`\n✅ Correct: `\"Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture\"`\n\n## Core Principles\n\n1. **Be Specific and Clear** - Use precise descriptions, avoid vague terms\n2. **Step-by-step Editing** - Break complex modifications into multiple simple steps\n3. **Explicit Preservation** - State what should remain unchanged\n4. **Verb Selection** - Use \"change\", \"replace\" rather than \"transform\"\n\n## Best Practice Templates\n\n**Object Modification:**\n`\"Change [object] to [new state], keep [content to preserve] unchanged\"`\n\n**Style Transfer:**\n`\"Transform to [specific style], while maintaining [composition/character/other] unchanged\"`\n\n**Background Replacement:**\n`\"Change the background to [new background], keep the subject in the exact same position and pose\"`\n\n**Text Editing:**\n`\"Replace '[original text]' with '[new text]', maintain the same font style\"`\n\n> **Remember:** The more specific, the better. Kontext excels at understanding detailed instructions and maintaining consistency. "], "color": "#432", "bgcolor": "#653"}, {"id": 178, "type": "workflow>FLUX.1 Kontext Image Edit", "pos": [1020, 1140], "size": [400, 546], "flags": {}, "order": 12, "mode": 4, "inputs": [{"name": "LATENT", "type": "LATENT", "link": 279}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [280]}, {"name": "LATENT", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "workflow>FLUX.1 Kontext Image Edit", "models": [{"name": "flux1-dev-kontext_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors", "directory": "diffusion_models"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}, {"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": [979175714064968, "fixed", 20, 1, "euler", "simple", 1, 2.5, "ComfyUI", "there is a bright light", "", "flux1-dev-kontext_fp8_scaled.safetensors", "default", "t5xxl_fp8_e4m3fn_scaled.safetensors", "clip_l.safetensors", "flux", "default", "ae.safetensors"]}], "links": [[222, 42, 0, 124, 0, "IMAGE"], [223, 39, 0, 124, 1, "VAE"], [255, 133, 0, 150, 0, "IMAGE"], [256, 152, 0, 150, 1, "IMAGE"], [257, 150, 0, 42, 0, "IMAGE"], [273, 150, 0, 172, 0, "IMAGE"], [277, 124, 0, 175, 0, "LATENT"], [279, 175, 1, 178, 0, "LATENT"], [280, 178, 0, 157, 0, "IMAGE"], [281, 175, 0, 144, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.5054470284993222, "offset": [1245.9384802452255, -544.8330389520092]}, "frontendVersion": "1.23.4", "groupNodes": {"FLUX.1 Kontext Image Edit": {"nodes": [{"id": -1, "type": "Reroute", "pos": [2354.87890625, -127.23468780517578], "size": [75, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}, "index": 0}, {"id": -1, "type": "ReferenceLatent", "pos": [2730, -220], "size": [197.712890625, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": []}], "properties": {"Node name for S&R": "ReferenceLatent", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 1, "widgets_values": []}, {"id": -1, "type": "VAEDecode", "pos": [3270, -110], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": null}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 2, "widgets_values": []}, {"id": -1, "type": "K<PERSON><PERSON><PERSON>", "pos": [2930, -110], "size": [315, 262], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": null}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": null}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": null}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [972054013131369, "fixed", 20, 1, "euler", "simple", 1], "index": 3}, {"id": -1, "type": "FluxGuidance", "pos": [2940, -220], "size": [211.60000610351562, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "guidance", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "FluxGuidance", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [2.5], "index": 4}, {"id": -1, "type": "SaveImage", "pos": [3490, -110], "size": [985.3012084960938, 1060.3828125], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ComfyUI"], "index": 5}, {"id": -1, "type": "CLIPTextEncode", "pos": [2500, -110], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["there is a bright light"], "color": "#232", "bgcolor": "#353", "index": 6}, {"id": -1, "type": "CLIPTextEncode", "pos": [2504.1435546875, 97.9598617553711], "size": [422.84503173828125, 164.31304931640625], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533", "index": 7}, {"id": -1, "type": "UNETLoader", "pos": [2630, -370], "size": [270, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "weight_dtype", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": []}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["preliminary-dev-kontext.sft", "default"], "color": "#223", "bgcolor": "#335", "index": 8}, {"id": -1, "type": "DualCLIPLoader", "pos": [2100, -290], "size": [337.76861572265625, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}], "properties": {"Node name for S&R": "DualCLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"], "color": "#223", "bgcolor": "#335", "index": 9}, {"id": -1, "type": "VAELoader", "pos": [2960, -370], "size": [270, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "vae_name", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": []}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ae.safetensors"], "color": "#223", "bgcolor": "#335", "index": 10}], "links": [[6, 0, 1, 0, 72, "CONDITIONING"], [0, 0, 1, 1, 66, "*"], [3, 0, 2, 0, 69, "LATENT"], [10, 0, 2, 1, 76, "VAE"], [8, 0, 3, 0, 74, "MODEL"], [4, 0, 3, 1, 70, "CONDITIONING"], [7, 0, 3, 2, 73, "CONDITIONING"], [0, 0, 3, 3, 66, "*"], [1, 0, 4, 0, 67, "CONDITIONING"], [2, 0, 5, 0, 68, "IMAGE"], [9, 0, 6, 0, 75, "CLIP"], [9, 0, 7, 0, 75, "CLIP"]], "external": [], "config": {"0": {}, "1": {}, "2": {"output": {"0": {"visible": true}}}, "3": {"output": {"0": {"visible": true}}, "input": {"denoise": {"visible": false}, "cfg": {"visible": false}}}, "4": {}, "5": {}, "6": {}, "7": {"input": {"text": {"visible": false}}}, "8": {}, "9": {"input": {"type": {"visible": false}}}, "10": {}}}}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}