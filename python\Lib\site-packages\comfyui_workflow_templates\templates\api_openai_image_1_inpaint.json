{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 31, "last_link_id": 17, "nodes": [{"id": 28, "type": "SaveImage", "pos": [1290, 480], "size": [315, 270], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 25, "type": "OpenAIGPTImage1", "pos": [870, 480], "size": [400, 252], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 14}, {"name": "mask", "shape": 7, "type": "MASK", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13]}], "properties": {"Node name for S&R": "OpenAIGPTImage1"}, "widgets_values": ["Let the cat hold a big fish.", 1371116998, "randomize", "high", "opaque", "1024x1024", 1]}, {"id": 30, "type": "<PERSON>downNote", "pos": [140, 480], "size": [337.94921875, 88], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[View tuotrial here](https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/openai/gpt-image-1)"], "color": "#432", "bgcolor": "#653"}, {"id": 31, "type": "<PERSON>downNote", "pos": [144.1734619140625, 620.8964233398438], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 29, "type": "LoadImage", "pos": [500, 480], "size": [342.*************, 314], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [14]}, {"name": "MASK", "type": "MASK", "links": [17]}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-1031667.8000000119.png [input]", "image"]}], "links": [[13, 25, 0, 28, 0, "IMAGE"], [14, 29, 0, 25, 0, "IMAGE"], [17, 29, 1, 25, 1, "MASK"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [160.00231082504726, -262.22847097090255]}, "frontendVersion": "1.18.5", "node_versions": {"comfy-core": "0.3.29"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}