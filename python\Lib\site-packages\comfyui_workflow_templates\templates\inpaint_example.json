{"id": "d1248744-d69b-49f7-bc58-1db999731801", "revision": 0, "last_node_id": 34, "last_link_id": 86, "nodes": [{"id": 29, "type": "CheckpointLoaderSimple", "pos": [80, 150], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [80]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [81, 82]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [83, 84]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "512-inpainting-ema.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-inpainting/resolve/main/512-inpainting-ema.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["512-inpainting-ema.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [432, 158], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 81}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["teacup on pink tablecloth, Anime style"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [434, 371], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 82}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["watermark, text\n"], "color": "#223", "bgcolor": "#335"}, {"id": 20, "type": "LoadImage", "pos": [80, 660], "size": [385, 365], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [85]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": [79]}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["input.png", "image"]}, {"id": 33, "type": "ImageScaleToTotalPixels", "pos": [490, 660], "size": [260, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 85}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [86]}], "properties": {"Node name for S&R": "ImageScaleToTotalPixels"}, "widgets_values": ["nearest-exact", 0.25]}, {"id": 32, "type": "<PERSON>downNote", "pos": [490, 790], "size": [260, 100], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About Scale Image to Total Pixels", "properties": {}, "widgets_values": ["The model this template uses is trained based on a 512*512 image dataset. So if you use an input image that's too large, it might cause some issues. We've added the Scale image to total pixels node to scale images. If you're quite familiar with this model, you can remove it."], "color": "#432", "bgcolor": "#653"}, {"id": 34, "type": "<PERSON>downNote", "pos": [490, 930], "size": [260, 90], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "About how to draw mask", "properties": {}, "widgets_values": ["Click on the **Load Image** node, then use the right-click menu to open the mask editor to draw a mask."], "color": "#432", "bgcolor": "#653"}, {"id": 26, "type": "VAEEncodeForInpaint", "pos": [890, 650], "size": [226.8000030517578, 98], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 86}, {"name": "vae", "type": "VAE", "link": 83}, {"name": "mask", "type": "MASK", "link": 79}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [72]}], "properties": {"Node name for S&R": "VAEEncodeForInpaint", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [6]}, {"id": 8, "type": "VAEDecode", "pos": [1240, 120], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 42}, {"name": "vae", "type": "VAE", "link": 84}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [22]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": []}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [900, 120], "size": [315, 262], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 80}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 72}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [42]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [808369199502636, "randomize", 20, 8, "uni_pc_bh2", "normal", 1]}, {"id": 9, "type": "SaveImage", "pos": [1240, 220], "size": [410, 430], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 22}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["ComfyUI"]}, {"id": 31, "type": "<PERSON>downNote", "pos": [-320, 110], "size": [380, 250], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "Tutorial links", "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org/tutorials/basic/inpaint) | [教程](https://docs.comfy.org/zh-CN/tutorials/basic/inpaint)\n\n## Model link\n**checkpoint**\n\n[512-inpainting-ema.safetensors](https://huggingface.co/stabilityai/stable-diffusion-2-inpainting/resolve/main/512-inpainting-ema.safetensors)\n\n\nModel Storage Location\n\n```\n📂 ComfyUI/\n├── 📂 models/\n│   ├── 📂 checkpoints/\n│   │   └── 512-inpainting-ema.safetensors\n\n```\n\n\n"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [22, 8, 0, 9, 0, "IMAGE"], [42, 3, 0, 8, 0, "LATENT"], [72, 26, 0, 3, 3, "LATENT"], [79, 20, 1, 26, 2, "MASK"], [80, 29, 0, 3, 0, "MODEL"], [81, 29, 1, 6, 0, "CLIP"], [82, 29, 1, 7, 0, "CLIP"], [83, 29, 2, 26, 1, "VAE"], [84, 29, 2, 8, 1, "VAE"], [85, 20, 0, 33, 0, "IMAGE"], [86, 33, 0, 26, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Step2 - Load image and draw mask", "bounding": [60, 580, 810, 480], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step1 - Load model", "bounding": [70, 80, 335, 181.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3 - Prompt", "bounding": [420, 80, 450, 480], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8264462809917354, "offset": [702.1144075614629, 151.80479576308267]}, "frontendVersion": "1.24.1"}, "version": 0.4}