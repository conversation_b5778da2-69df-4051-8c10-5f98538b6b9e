{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 44, "last_link_id": 27, "nodes": [{"id": 40, "type": "LoadImage", "pos": [1226.656982421875, 1398.64013671875], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [26]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["long.png", "image"]}, {"id": 43, "type": "KlingImage2VideoNode", "pos": [1546.2130126953125, 1394.4969482421875], "size": [400, 302], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "start_frame", "type": "IMAGE", "link": 26}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [27]}, {"name": "video_id", "type": "STRING", "links": null}, {"name": "duration", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "KlingImage2VideoNode"}, "widgets_values": ["Translucent Chinese dragon, pearly white scales, delicate white whiskers, silver-white eyes, elegant claws, dragon body coiling into circular form, slowly rotating and rising, tail elegantly swaying, head slightly tilting upward, scales flowing like waves, sea of clouds surrounding dragon, white lightning flashing in background, close-up circling shot, light passing through translucent dragon body, scales refracting soft white light, mystical ethereal atmosphere, Eastern mythological style", "low quality, blurry, distorted, unnatural pose, disproportionate dragon body, Western dragon design, golden sparkles, colorful elements, orange light particles, multicolored tones, stiff movement, straight flying, simplistic clouds, flat effects, lack of details, opaque dragon body, rough texture, messy composition, overexposed, low resolution, long shot, stationary dragon", "kling-v2-master", 0, "std", "16:9", "5"]}, {"id": 38, "type": "SaveVideo", "pos": [1983.72607421875, 1392.0660400390625], "size": [270, 249.875], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 27}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 44, "type": "<PERSON>downNote", "pos": [878.6950073242188, 1405.7354736328125], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[26, 40, 0, 43, 0, "IMAGE"], [27, 43, 0, 38, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.****************, "offset": [-642.7492524223238, -1079.0486583856923]}, "frontendVersion": "1.18.5"}, "version": 0.4}