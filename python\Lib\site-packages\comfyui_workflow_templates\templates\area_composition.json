{"id": "53e00821-18fc-4a64-9dbc-bbea1f463f96", "revision": 0, "last_node_id": 49, "last_link_id": 136, "nodes": [{"id": 13, "type": "CLIPTextEncode", "pos": [20, -1080], "size": [400, 200], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 109}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [91]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) (evening:1.2) (sky:1.2) (clouds) (colorful) (HDR:1.2) (sunset:1.3)\n"], "color": "#232", "bgcolor": "#353"}, {"id": 33, "type": "CLIPTextEncode", "pos": [20, -810], "size": [400, 200], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 107}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [92]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) morning sky\n\n"], "color": "#232", "bgcolor": "#353"}, {"id": 17, "type": "CLIPTextEncode", "pos": [20, -540], "size": [400, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 108}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [90]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) (daytime:1.2) sky (blue)\n"], "color": "#232", "bgcolor": "#353"}, {"id": 35, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [800, -1080], "size": [342.6000061035156, 46], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 126}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 127}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [130]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 12, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [790, -740], "size": [342.6000061035156, 46], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 128}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 129}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [131]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 10, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [780, -290], "size": [342.6000061035156, 46], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 132}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 93}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [133]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 20, "type": "VAELoader", "pos": [-500, 150], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [36, 51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAELoader", "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [500, 0], "size": [315, 474], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 104}, {"name": "positive", "type": "CONDITIONING", "link": 133}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7, 41]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [339192168723391, "randomize", 13, 8.5, "dpmpp_sde", "normal", 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [20, -280], "size": [390, 170], "flags": {"collapsed": false}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 105}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [93]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) beautiful landscape breathtaking amazing view nature photograph forest mountains ocean (sky) national park scenery"], "color": "#232", "bgcolor": "#353"}, {"id": 8, "type": "VAEDecode", "pos": [850, 10], "size": [210, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 36}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [49]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [-500, -10], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [104, 134]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [111]}, {"name": "VAE", "type": "VAE", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "DreamShaper_8_pruned.safetensors", "url": "https://huggingface.co/Lykon/DreamShaper/resolve/main/DreamShaper_8_pruned.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["DreamShaper_8_pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 26, "type": "CLIPTextEncode", "pos": [20, 580], "size": [400, 200], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 135}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [46]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) beautiful (HDR:1.2) (realistic:1.2) landscape breathtaking amazing view nature scenery photograph forest mountains ocean daytime night evening morning, (sky:1.2)\n"], "color": "#232", "bgcolor": "#353"}, {"id": 48, "type": "<PERSON>downNote", "pos": [-760, -50], "size": [225, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/area_composition/)"], "color": "#432", "bgcolor": "#653"}, {"id": 46, "type": "CLIPSetLastLayer", "pos": [-500, -160], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 111}], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [105, 106, 107, 108, 109, 110, 135, 136]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 19, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [790, -410], "size": [342.6000061035156, 46], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 130}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 131}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [132]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 5, "type": "EmptyLatentImage", "pos": [-500, 310], "size": [315, 106], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [704, 1280, 1], "color": "#323", "bgcolor": "#535"}, {"id": 22, "type": "LatentUpscale", "pos": [-500, 570], "size": [315, 130], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 41}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [42]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LatentUpscale"}, "widgets_values": ["nearest-exact", 1088, 1920, "disabled"], "color": "#322", "bgcolor": "#533"}, {"id": 31, "type": "VAEDecode", "pos": [880, 590], "size": [210, 46], "flags": {"collapsed": false}, "order": 26, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 50}, {"name": "vae", "type": "VAE", "link": 51}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [87]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 7, "type": "CLIPTextEncode", "pos": [40, 220], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 106}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"], "color": "#223", "bgcolor": "#335"}, {"id": 27, "type": "CLIPTextEncode", "pos": [20, 830], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 136}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [47]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"], "color": "#223", "bgcolor": "#335"}, {"id": 24, "type": "K<PERSON><PERSON><PERSON>", "pos": [510, 580], "size": [315, 474.00006103515625], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 134}, {"name": "positive", "type": "CONDITIONING", "link": 46}, {"name": "negative", "type": "CONDITIONING", "link": 47}, {"name": "latent_image", "type": "LATENT", "link": 42}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [50]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [760129347151046, "randomize", 14, 7, "dpmpp_2m", "simple", 0.5]}, {"id": 32, "type": "SaveImage", "pos": [1200, 530], "size": [580, 940], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 87}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}, {"id": 14, "type": "CLIPTextEncode", "pos": [20, -1340], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 110}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [89]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) (night:1.3) (darkness) sky (black) (stars:1.2) (galaxy:1.2) (space) (universe)"], "color": "#232", "bgcolor": "#353"}, {"id": 15, "type": "ConditioningSetArea", "pos": [440, -1340], "size": [299, 154], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 89}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [126]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 0, 1.5]}, {"id": 18, "type": "ConditioningSetArea", "pos": [440, -540], "size": [312, 154], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 90}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [129]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 704, 1]}, {"id": 34, "type": "ConditioningSetArea", "pos": [440, -810], "size": [312, 154], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 92}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [128]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 512, 1]}, {"id": 11, "type": "ConditioningSetArea", "pos": [440, -1070], "size": [314, 154], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 91}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [127]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 312, 1]}, {"id": 9, "type": "SaveImage", "pos": [1200, -160], "size": [450, 640], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 49}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [36, 20, 0, 8, 1, "VAE"], [41, 3, 0, 22, 0, "LATENT"], [42, 22, 0, 24, 3, "LATENT"], [46, 26, 0, 24, 1, "CONDITIONING"], [47, 27, 0, 24, 2, "CONDITIONING"], [49, 8, 0, 9, 0, "IMAGE"], [50, 24, 0, 31, 0, "LATENT"], [51, 20, 0, 31, 1, "VAE"], [87, 31, 0, 32, 0, "IMAGE"], [89, 14, 0, 15, 0, "CONDITIONING"], [90, 17, 0, 18, 0, "CONDITIONING"], [91, 13, 0, 11, 0, "CONDITIONING"], [92, 33, 0, 34, 0, "CONDITIONING"], [93, 6, 0, 10, 1, "CONDITIONING"], [104, 44, 0, 3, 0, "MODEL"], [105, 46, 0, 6, 0, "CLIP"], [106, 46, 0, 7, 0, "CLIP"], [107, 46, 0, 33, 0, "CLIP"], [108, 46, 0, 17, 0, "CLIP"], [109, 46, 0, 13, 0, "CLIP"], [110, 46, 0, 14, 0, "CLIP"], [111, 44, 1, 46, 0, "CLIP"], [126, 15, 0, 35, 0, "CONDITIONING"], [127, 11, 0, 35, 1, "CONDITIONING"], [128, 34, 0, 12, 0, "CONDITIONING"], [129, 18, 0, 12, 1, "CONDITIONING"], [130, 35, 0, 19, 0, "CONDITIONING"], [131, 12, 0, 19, 1, "CONDITIONING"], [132, 19, 0, 10, 0, "CONDITIONING"], [133, 10, 0, 3, 1, "CONDITIONING"], [134, 44, 0, 24, 0, "MODEL"], [135, 46, 0, 26, 0, "CLIP"], [136, 46, 0, 27, 0, "CLIP"]], "groups": [{"id": 1, "title": "Step2 - Area Conditioning", "bounding": [10, -1410, 1160, 1320], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Second Sampling: Upscaled Latent Sampling", "bounding": [10, 500, 1160, 570], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step1 - Load models", "bounding": [-510, -80, 335, 301.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "First Sampling: text2img", "bounding": [10, -70, 1160, 550], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "First sampling size", "bounding": [-510, 240, 335, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Second upscaled sampling size", "bounding": [-510, 500, 340, 210], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8350482105754531, "offset": [603.139952513136, 459.71873876304414]}, "frontendVersion": "1.23.4"}, "version": 0.4}