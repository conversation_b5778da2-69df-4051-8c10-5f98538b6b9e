
方差保持调度器（VPScheduler）节点旨在基于方差保持（Variance Preserving, VP）调度方法生成一系列噪声水平（sigmas）。该序列对于引导扩散模型中的去噪过程至关重要，允许对图像或其他数据类型的生成进行控制。

## 输入

| 参数名称   | 数据类型 | 作用                                                         |
|------------|----------|--------------------------------------------------------------|
| `steps`    | `INT`    | 指定扩散过程中的步骤数，影响生成噪声水平的粒度。           |
| `beta_d`   | `FLOAT`  | 确定整体噪声水平分布，影响生成噪声水平的方差。             |
| `beta_min` | `FLOAT`  | 设置噪声水平的最小边界，确保噪声不会低于某个阈值。         |
| `eps_s`    | `FLOAT`  | 调整起始的epsilon值，微调扩散过程中的初始噪声水平。       |

## 输出

| 参数名称 | 数据类型 | 作用                                                         |
|----------|----------|--------------------------------------------------------------|
| `sigmas` | `SIGMAS`| 基于VP调度方法生成的一系列噪声水平，用于引导扩散模型中的去噪过程。 |
