
이 노드는 이미지 인페인팅 작업에 적합한 잠재 표현으로 이미지를 인코딩하기 위해 설계되었으며, VAE 모델에 의해 최적의 인코딩을 위해 입력 이미지와 마스크를 조정하는 추가 전처리 단계를 포함합니다.

## 입력

| 매개변수       | 데이터 유형 | 설명                                                                                                                                               |
| -------------- | ----------- | -------------------------------------------------------------------------------------------------------------------------------------------------- |
| `pixels`       | `IMAGE`     | 인코딩될 입력 이미지입니다. 이 이미지는 인코딩 전에 VAE 모델의 예상 입력 차원에 맞게 전처리 및 크기 조정을 거칩니다.                               |
| `vae`          | VAE         | 이미지를 잠재 표현으로 인코딩하는 데 사용되는 VAE 모델입니다. 변환 과정에서 중요한 역할을 하며, 출력 잠재 공간의 품질과 특성을 결정합니다.         |
| `mask`         | `MASK`      | 인페인팅될 입력 이미지의 영역을 나타내는 마스크입니다. 인코딩 전에 이미지를 수정하여 VAE가 관련 영역에 집중하도록 합니다.                          |
| `grow_mask_by` | `INT`       | 잠재 공간에서 매끄러운 전환을 보장하기 위해 인페인팅 마스크를 얼마나 확장할지를 지정합니다. 더 큰 값은 인페인팅에 영향을 받는 영역을 증가시킵니다. |

## 출력

| 매개변수 | 데이터 유형 | 설명                                                                                                     |
| -------- | ----------- | -------------------------------------------------------------------------------------------------------- |
| `latent` | `LATENT`    | 출력에는 이미지의 인코딩된 잠재 표현과 노이즈 마스크가 포함되며, 이는 후속 인페인팅 작업에 필수적입니다. |
