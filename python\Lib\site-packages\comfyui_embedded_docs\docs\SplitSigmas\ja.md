
SplitSigmasノードは、指定されたステップに基づいてシグマ値のシーケンスを2つの部分に分割するために設計されています。この機能は、シグマシーケンスの初期部分と後続部分を異なる方法で処理する必要がある操作にとって重要であり、これらの値をより柔軟かつターゲットを絞って操作することを可能にします。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|------|
| `sigmas`  | `SIGMAS`    | 'sigmas'パラメータは、分割されるシグマ値のシーケンスを表します。これは、分割点と結果として得られる2つのシグマ値のシーケンスを決定するために不可欠であり、ノードの実行と結果に影響を与えます。 |
| `step`    | `INT`       | 'step'パラメータは、シグマシーケンスを分割するインデックスを指定します。これは、結果として得られる2つのシグマシーケンスの境界を定義する上で重要な役割を果たし、ノードの機能と出力の特性に影響を与えます。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|------|
| `sigmas`  | `SIGMAS`    | ノードは、指定されたステップで分割された元のシーケンスの各部分を表す2つのシグマ値のシーケンスを出力します。これらの出力は、シグマ値の差別化された処理を必要とする後続の操作にとって重要です。 |
