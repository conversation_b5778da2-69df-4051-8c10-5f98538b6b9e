{"id": "eda08e7f-225b-491a-983d-820266b9cdae", "revision": 0, "last_node_id": 51, "last_link_id": 111, "nodes": [{"id": 20, "type": "VAELoader", "pos": [-320, 730], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [36, 51]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAELoader", "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 27, "type": "CLIPTextEncode", "pos": [160, 1190], "size": [400, 200], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 103}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [47]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"], "color": "#223", "bgcolor": "#335"}, {"id": 31, "type": "VAEDecode", "pos": [1290, 950], "size": [210, 46], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 50}, {"name": "vae", "type": "VAE", "link": 51}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [100]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 49, "type": "CLIPSetLastLayer", "pos": [-320, 870], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 108}], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [101, 102, 103, 104, 105]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [940, 140], "size": [315, 474.0000305175781], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 106}, {"name": "positive", "type": "CONDITIONING", "link": 111}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7, 41]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [829372918347310, "randomize", 13, 7, "dpmpp_sde", "normal", 1]}, {"id": 8, "type": "VAEDecode", "pos": [1280, 150], "size": [210, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 36}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [49]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1280, 250], "size": [380, 350], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 49}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}, {"id": 24, "type": "K<PERSON><PERSON><PERSON>", "pos": [950, 940], "size": [315, 474.00006103515625], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 107}, {"name": "positive", "type": "CONDITIONING", "link": 46}, {"name": "negative", "type": "CONDITIONING", "link": 47}, {"name": "latent_image", "type": "LATENT", "link": 42}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [50]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [16582288324285, "randomize", 7, 5, "dpmpp_sde", "simple", 0.52]}, {"id": 7, "type": "CLIPTextEncode", "pos": [160, 640], "size": [400, 180.61000061035156], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 101}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"], "color": "#223", "bgcolor": "#335"}, {"id": 48, "type": "CheckpointLoaderSimple", "pos": [-320, 580], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [106, 107]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [108]}, {"name": "VAE", "type": "VAE", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "DreamShaper_8_pruned.safetensors", "url": "https://huggingface.co/Lykon/DreamShaper/resolve/main/DreamShaper_8_pruned.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["DreamShaper_8_pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 50, "type": "<PERSON>downNote", "pos": [-580, 550], "size": [225, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/area_composition/#increasing-consistency-of-images-with-area-composition)"], "color": "#432", "bgcolor": "#653"}, {"id": 51, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [590, 400], "size": [211.060546875, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 109}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 110}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [111]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}}, {"id": 5, "type": "EmptyLatentImage", "pos": [-320, 1020], "size": [315, 106], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1], "color": "#323", "bgcolor": "#535"}, {"id": 46, "type": "ConditioningSetArea", "pos": [580, 140], "size": [317.3999938964844, 154], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 93}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [109]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "ConditioningSetArea"}, "widgets_values": [304, 304, 0, 64, 1]}, {"id": 22, "type": "LatentUpscale", "pos": [940, 690], "size": [315, 130], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 41}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [42]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LatentUpscale"}, "widgets_values": ["nearest-exact", 1024, 1024, "disabled"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [160, 400], "size": [400, 160], "flags": {"collapsed": false}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 102}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [110]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) vast landscape, rolling hills, distant mountains, blue sky, fluffy clouds, morning light, fresh air, wildflowers"], "color": "#232", "bgcolor": "#353"}, {"id": 45, "type": "CLIPTextEncode", "pos": [160, 140], "size": [400, 200], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 105}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [93]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) beautiful lake, surrounded by pine forest, snow mountain in the background, clear water, reflection, peaceful, nature, sunrise"], "color": "#232", "bgcolor": "#353"}, {"id": 26, "type": "CLIPTextEncode", "pos": [160, 940], "size": [400, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 104}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [46]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) beautiful lake, surrounded by pine forest, snow mountain in the background, clear water, reflection, peaceful, nature, sunrise"], "color": "#232", "bgcolor": "#353"}, {"id": 32, "type": "SaveImage", "pos": [1300, 1050], "size": [570, 390], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 100}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [36, 20, 0, 8, 1, "VAE"], [41, 3, 0, 22, 0, "LATENT"], [42, 22, 0, 24, 3, "LATENT"], [46, 26, 0, 24, 1, "CONDITIONING"], [47, 27, 0, 24, 2, "CONDITIONING"], [49, 8, 0, 9, 0, "IMAGE"], [50, 24, 0, 31, 0, "LATENT"], [51, 20, 0, 31, 1, "VAE"], [93, 45, 0, 46, 0, "CONDITIONING"], [100, 31, 0, 32, 0, "IMAGE"], [101, 49, 0, 7, 0, "CLIP"], [102, 49, 0, 6, 0, "CLIP"], [103, 49, 0, 27, 0, "CLIP"], [104, 49, 0, 26, 0, "CLIP"], [105, 49, 0, 45, 0, "CLIP"], [106, 48, 0, 3, 0, "MODEL"], [107, 48, 0, 24, 0, "MODEL"], [108, 48, 1, 49, 0, "CLIP"], [109, 46, 0, 51, 0, "CONDITIONING"], [110, 6, 0, 51, 1, "CONDITIONING"], [111, 51, 0, 3, 1, "CONDITIONING"]], "groups": [{"id": 1, "title": "Step1- Load models", "bounding": [-330, 510, 340, 300], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Fisrt pass sampling", "bounding": [930, 70, 740, 557.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Sencond Pass Sampling", "bounding": [940, 870, 940, 583.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Second pass prompt", "bounding": [150, 870, 420, 533.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "First pass: area conditioning", "bounding": [150, 70, 757.4000244140625, 503.6000061035156], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"id": 6, "title": "Image Size", "bounding": [-330, 950, 335, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7759730000000206, "offset": [663.9538816920744, 74.83962520087691]}, "frontendVersion": "1.23.4"}, "version": 0.4}