
VideoLinearCFGGuidanceノードは、ビデオモデルに線形条件付けガイダンススケールを適用し、指定された範囲内で条件付きおよび非条件付きコンポーネントの影響を調整します。これにより、生成プロセスを動的に制御し、望ましい条件付けレベルに基づいてモデルの出力を微調整することが可能になります。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|------|
| `model`   | MODEL     | モデルパラメータは、線形CFGガイダンスが適用されるビデオモデルを表します。これは、ガイダンススケールで修正されるベースモデルを定義するために重要です。 |
| `min_cfg` | `FLOAT`     | min_cfgパラメータは、適用される最小条件付けガイダンススケールを指定し、線形スケール調整の開始点として機能します。これは、ガイダンススケールの下限を決定し、モデルの出力に影響を与える上で重要な役割を果たします。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|------|
| `model`   | MODEL     | 出力は、線形CFGガイダンススケールが適用された入力モデルの修正バージョンです。この調整されたモデルは、指定されたガイダンススケールに基づいて、さまざまな条件付けの度合いで出力を生成することができます。 |
