
このノードは、StableZero123モデルに特化したバッチ処理で条件付け情報を処理するために設計されています。複数の条件付けデータセットを同時に効率的に処理し、バッチ処理が重要なシナリオでのワークフローを最適化します。

## 入力

| パラメータ             | Data Type | 説明 |
|----------------------|--------------|-------------|
| `clip_vision`         | `CLIP_VISION` | 条件付けプロセスに視覚的なコンテキストを提供するCLIPビジョン埋め込みです。 |
| `init_image`          | `IMAGE`      | 条件付けの基礎となる初期画像で、生成プロセスの出発点となります。 |
| `vae`                 | `VAE`        | 条件付けプロセスで画像をエンコードおよびデコードするために使用される変分オートエンコーダーです。 |
| `width`               | `INT`        | 出力画像の幅です。 |
| `height`              | `INT`        | 出力画像の高さです。 |
| `batch_size`          | `INT`        | 単一のバッチで処理される条件付けセットの数です。 |
| `elevation`           | `FLOAT`      | 3Dモデル条件付けのための仰角で、生成された画像の視点に影響を与えます。 |
| `azimuth`             | `FLOAT`      | 3Dモデル条件付けのための方位角で、生成された画像の向きに影響を与えます。 |
| `elevation_batch_increment` | `FLOAT` | バッチ全体での仰角の増分変化で、さまざまな視点を可能にします。 |
| `azimuth_batch_increment` | `FLOAT` | バッチ全体での方位角の増分変化で、さまざまな向きを可能にします。 |

## 出力

| パラメータ     | データ型 | 説明 |
|---------------|--------------|-------------|
| `positive`    | `CONDITIONING` | 生成されたコンテンツで特定の特徴や側面を促進するために調整されたポジティブ条件付け出力です。 |
| `negative`    | `CONDITIONING` | 生成されたコンテンツで特定の特徴や側面を抑制するために調整されたネガティブ条件付け出力です。 |
| `latent`      | `LATENT`     | 条件付けプロセスから得られた潜在表現で、さらなる処理や生成ステップの準備が整っています。 |
