
此节点旨在为DPM++ SDE（随机微分方程）模型生成采样器。它适配CPU和GPU执行环境，并根据可用硬件优化采样器的实现。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `eta` | FLOAT | 指定SDE求解器的步长，影响采样过程的粒度。 |
| `s_noise` | FLOAT | 确定采样过程中应用的噪声水平，影响生成样本的多样性。 |
| `r` | FLOAT | 控制采样过程中噪声减少的比例，影响生成样本的清晰度和质量。 |
| `noise_device` | COMBO[STRING] | 选择采样器的执行环境（CPU或GPU），基于可用硬件优化性能。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `sampler` | SAMPLER | 配置了指定参数的生成采样器，准备用于采样操作。 |
