裁剪遮罩节点用于从给定的遮罩中裁剪出指定的区域。它允许用户通过指定坐标和尺寸来定义感兴趣区域，有效地提取遮罩的一部分以进行进一步的处理或分析。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `mask` | MASK | 遮罩输入，代表要裁剪的遮罩图像。它对于根据指定的坐标和尺寸定义要提取的区域至关重要。 |
| `x` | INT | x坐标指定水平轴上裁剪应开始的起始点。 |
| `y` | INT | y坐标确定裁剪操作在垂直轴上的起始点。 |
| `width` | INT | 宽度定义从起始点开始裁剪区域的水平范围。 |
| `height` | INT | 高度指定从起始点开始裁剪区域的垂直范围。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `mask` | MASK | 输出是裁剪后的遮罩，它是根据指定的坐标和尺寸定义的原始遮罩的一部分。 |
