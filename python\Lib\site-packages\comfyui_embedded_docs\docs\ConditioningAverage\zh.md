此节点设计用于混合两组条件数据，根据指定的强度应用加权平均。这一过程允许动态调整条件影响，便于微调生成的内容或特征。

## 输入

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `conditioning_to` | `CONDITIONING` | 表示将应用混合的主要条件数据集。它作为加权平均操作的基础。 |
| `conditioning_from` | `CONDITIONING` | 表示将混合到主要数据集中的次要条件数据集。此数据基于指定的强度影响最终输出。 |
| `conditioning_to_strength` | `FLOAT` | 标量值，用于确定主要和次要条件数据之间混合的强度。它直接影响加权平均的平衡。 |

## 输出

| 参数名称 | 数据类型 | 作用 |
| --- | --- | --- |
| `CONDITIONING` | CONDITIONING | 混合主要和次要条件数据的结果，产生一组新的条件，反映了加权平均。 |
