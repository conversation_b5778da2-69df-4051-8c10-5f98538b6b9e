{"id": "cb4a348c-918b-42f8-b0a9-ff75063a3171", "revision": 0, "last_node_id": 9, "last_link_id": 3, "nodes": [{"id": 5, "type": "LoadImage", "pos": [-468.94561767578125, 1054.1129150390625], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 6, "type": "SaveImage", "pos": [236.90634155273438, 1056.1395263671875], "size": [270, 270], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 3}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 3, "type": "RunwayTextToImageNode", "pos": [-176.62879943847656, 1055.5472412109375], "size": [400, 200], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "reference_image", "shape": 7, "type": "IMAGE", "link": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3]}], "properties": {"Node name for S&R": "RunwayTextToImageNode"}, "widgets_values": ["Close-up portrait of a 20-year-old Nordic girl with fair skin, delicate facial features, and long flowing hair, surrounded by softly drifting strands. Her skin has a gentle, dewy glow (not overly glossy). Vibrant, scattered color spots (pastel pinks, blues, greens) in a diffused, prismatic pattern dance across her face and hair, creating a dreamy, magical atmosphere—like light filtering through a rainbow-hued crystal. The focus is on her expressive eyes (with subtle catchlights mirroring the color spots) and subtle makeup. Ethereal lighting features soft shadows, pastel tones, and a blurred, cinematic depth of field, where the background bokeh echoes the same scattered color palette. Ultra-detailed, 8K quality, fantasy-inspired, with an emotional tone and a whimsical, otherworldly ambiance.", "1024:1024"], "color": "#432", "bgcolor": "#653"}, {"id": 9, "type": "<PERSON>downNote", "pos": [-825.9852294921875, 1052.5718994140625], "size": [336.5179748535156, 343.7064514160156], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 8, "type": "<PERSON>downNote", "pos": [-464.*************, 1415.***********], "size": [263.**************, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["`Runway Text to Image` Node only support one image input."], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 0, "IMAGE"], [3, 3, 0, 6, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.08347059433885, "offset": [933.8729374048801, -805.310410725895]}, "frontendVersion": "1.21.0"}, "version": 0.4}