"""
BiRefNet 兼容性修复插件
这个插件会在 ComfyUI 启动时自动修复 BiRefNet 配置问题
"""

import os
import sys
import importlib.util
from pathlib import Path

# 节点映射
NODE_CLASS_MAPPINGS = {}
NODE_DISPLAY_NAME_MAPPINGS = {}

def patch_birefnet_config():
    """动态修复 BiRefNet 配置类"""
    try:
        # 查找 BiRefNet_config.py 文件
        cache_paths = [
            ".cache/huggingface/modules/transformers_modules/RMBG-2.0/BiRefNet_config.py",
            "ComfyUI/models/RMBG/RMBG-2.0/BiRefNet_config.py",
            "../.cache/huggingface/modules/transformers_modules/RMBG-2.0/BiRefNet_config.py",
            "../../.cache/huggingface/modules/transformers_modules/RMBG-2.0/BiRefNet_config.py",
        ]
        
        config_file = None
        for path in cache_paths:
            if os.path.exists(path):
                config_file = path
                break
        
        if not config_file:
            print("[BiRefNet Fix] 未找到 BiRefNet_config.py 文件")
            return False
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否需要修复
        if '@tie_word_embeddings.setter' in content:
            print("[BiRefNet Fix] 配置文件已经修复过了")
            return True
        
        # 应用修复
        if 'def tie_word_embeddings(self):' in content and 'return False' in content:
            # 替换只读属性为可读写属性
            old_code = '''    @property
    def tie_word_embeddings(self):
        return False'''
            
            new_code = '''    # 添加tie_word_embeddings属性以提高兼容性
    @property
    def tie_word_embeddings(self):
        return getattr(self, '_tie_word_embeddings', False)
    
    @tie_word_embeddings.setter
    def tie_word_embeddings(self, value):
        self._tie_word_embeddings = value'''
            
            fixed_content = content.replace(old_code, new_code)
            
            if fixed_content != content:
                # 备份原文件
                backup_file = config_file + '.backup'
                if not os.path.exists(backup_file):
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                
                # 写入修复后的内容
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                print(f"[BiRefNet Fix] ✓ 已修复配置文件: {config_file}")
                return True
            else:
                print("[BiRefNet Fix] 未找到需要修复的代码模式")
                return False
        else:
            print("[BiRefNet Fix] 配置文件格式不符合预期")
            return False
            
    except Exception as e:
        print(f"[BiRefNet Fix] 修复过程中出错: {e}")
        return False

def monkey_patch_transformers():
    """对 transformers 库进行 monkey patch"""
    try:
        import transformers.configuration_utils
        
        # 保存原始的 __setattr__ 方法
        original_setattr = transformers.configuration_utils.PretrainedConfig.__setattr__
        
        def patched_setattr(self, key, value):
            """修复后的 __setattr__ 方法"""
            if key == 'tie_word_embeddings' and hasattr(self, 'tie_word_embeddings'):
                # 如果是 tie_word_embeddings 属性且已经存在，尝试使用 setter
                try:
                    # 检查是否有 setter
                    prop = getattr(type(self), 'tie_word_embeddings', None)
                    if isinstance(prop, property) and prop.fset is not None:
                        prop.fset(self, value)
                        return
                    else:
                        # 如果没有 setter，直接设置私有属性
                        object.__setattr__(self, '_tie_word_embeddings', value)
                        return
                except:
                    pass
            
            # 对于其他属性，使用原始方法
            return original_setattr(self, key, value)
        
        # 应用 monkey patch
        transformers.configuration_utils.PretrainedConfig.__setattr__ = patched_setattr
        print("[BiRefNet Fix] ✓ 已应用 transformers monkey patch")
        return True
        
    except Exception as e:
        print(f"[BiRefNet Fix] Monkey patch 失败: {e}")
        return False

# 在模块加载时自动执行修复
def initialize():
    """初始化修复"""
    print("[BiRefNet Fix] 正在初始化兼容性修复...")
    
    # 1. 修复配置文件
    patch_birefnet_config()
    
    # 2. 应用 monkey patch
    monkey_patch_transformers()
    
    print("[BiRefNet Fix] 兼容性修复初始化完成")

# 自动执行初始化
try:
    initialize()
except Exception as e:
    print(f"[BiRefNet Fix] 初始化失败: {e}")

print("[BiRefNet Fix] BiRefNet 兼容性修复插件已加载")
