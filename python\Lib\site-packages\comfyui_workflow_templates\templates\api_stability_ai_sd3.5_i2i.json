{"id": "7a9b9238-d961-4305-9894-bf4ae73f5bd2", "revision": 0, "last_node_id": 13, "last_link_id": 7, "nodes": [{"id": 11, "type": "LoadImage", "pos": [-134.7727508544922, 632.3964233398438], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [7]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["astronaut.webp", "image"]}, {"id": 8, "type": "PrimitiveStringMultiline", "pos": [-133.6640625, 1006.9987182617188], "size": [309.25, 266.54998779296875], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [5]}], "properties": {"Node name for S&R": "PrimitiveStringMultiline"}, "widgets_values": [""]}, {"id": 9, "type": "SaveImage", "pos": [735.2999267578125, 633.3601684570312], "size": [315, 270], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 6}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 6, "type": "StabilityStableImageSD_3_5Node", "pos": [223.26992797851562, 629.5799560546875], "size": [496.2400207519531, 346.17999267578125], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 7}, {"name": "negative_prompt", "shape": 7, "type": "STRING", "link": 5}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [6]}], "properties": {"Node name for S&R": "StabilityStableImageSD_3_5Node"}, "widgets_values": ["Micro-scale diorama style, An astronaut in a white spacesuit with red stripes, walking on a grassy field dotted with colorful miniature flowers, A large, bright full moon looms in the background of a clear blue sky,The scene has a toy-like, miniature aesthetic with sharp focus and exaggeratedly vivid colors.\n", "sd3.5-large", "1:1", "isometric", 4, 2535425653, "randomize", 0.6000000000000001]}, {"id": 13, "type": "<PERSON>downNote", "pos": [-495.7705383300781, 1175.3555908203125], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 12, "type": "<PERSON>downNote", "pos": [-489.*************, 634.*************], "size": [328.**************, 403.*************], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "About Stable Diffusion 3.5 Image Node", "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org/tutorials/api-nodes/stability-ai/stable-diffusion-3-5-image) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/stability-ai/stable-diffusion-3-5-image)\n\n### Required Parameters\n- **prompt**: Describes desired image content; detailed descriptions yield better results\n- **model**: Selects specific SD 3.5 model version\n- **aspect_ratio**: Sets output image ratio, only effective in text-to-image mode\n- **style_preset**: Chooses preset style affecting overall artistic style\n- **cfg_scale**: Controls adherence to prompt (1.0-10.0), higher values = closer to prompt\n- **seed**: Controls randomness; same seed produces similar image structures\n\n### Optional Parameters\n- **image**: Provides reference image for image-to-image mode\n- **negative_prompt**: Specifies unwanted elements in the output\n- **image_denoise**: Controls how much original image is preserved (0.0-1.0)\n  - 0.0: Completely preserves original image\n  - 1.0: Almost ignores original image, similar to text-to-image\n\n---\n\n### 必要参数\n- **prompt**: 描述想要生成的图像内容，详细的描述会带来更好效果\n- **model**: 选择SD 3.5的具体模型版本\n- **aspect_ratio**: 设置输出图像宽高比，仅在文生图模式有效\n- **style_preset**: 选择预设风格，影响整体艺术风格\n- **cfg_scale**: 控制对提示词的遵循程度(1.0-10.0)，值越高越接近提示词描述\n- **seed**: 控制随机性，相同seed能产生相似结构的图像\n\n### 可选参数\n- **image**: 提供参考图像进入图生图模式\n- **negative_prompt**: 指定不希望在图像中出现的内容\n- **image_denoise**: 图生图模式中控制保留原图特征程度(0.0-1.0)\n  - 0.0: 完全保留原图\n  - 1.0: 几乎忽略原图，类似纯文生图"], "color": "#432", "bgcolor": "#653"}], "links": [[5, 8, 0, 6, 1, "STRING"], [6, 6, 0, 9, 0, "IMAGE"], [7, 11, 0, 6, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1000000000000005, "offset": [856.6505253543542, -407.59259972806706]}, "frontendVersion": "1.17.11"}, "version": 0.4}