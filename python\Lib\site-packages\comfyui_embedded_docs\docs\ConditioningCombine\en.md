This node combines two conditioning inputs into a single output, effectively merging their information.

## Inputs

| Parameter            | Comfy dtype        | Description |
|----------------------|--------------------|-------------|
| `conditioning_1`      | `CONDITIONING`     | The first conditioning input to be combined. It plays an equal role with conditioning_2 in the combination process. |
| `conditioning_2`      | `CONDITIONING`     | The second conditioning input to be combined. It is equally important as conditioning_1 in the merging process. |

## Outputs

| Parameter            | Comfy dtype        | Description |
|----------------------|--------------------|-------------|
| `conditioning`        | `CONDITIONING`     | The result of combining conditioning_1 and conditioning_2, encapsulating the merged information. |
