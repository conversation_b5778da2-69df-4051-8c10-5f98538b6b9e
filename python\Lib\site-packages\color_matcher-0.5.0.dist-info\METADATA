Metadata-Version: 2.1
Name: color-matcher
Version: 0.5.0
Summary: Package enabling color transfer across images
Home-page: http://github.com/hahnec/color-matcher
Author: <PERSON>
Author-email: <EMAIL>
License: GNU GPL V3.0
Keywords: color match histogram matching image colour transfer monge kantorovich mkl pitie reinhardrecolor re-color recolour re-colour paintings film movie sequence automatic grading color-grading colour-grading equalize equalise equalization equalisation light-field lightfield stop-motion stopmotion
Platform: UNKNOWN
Requires-Python: >=3
Description-Content-Type: text/x-rst
Requires-Dist: numpy
Requires-Dist: imageio
Requires-Dist: docutils
Requires-Dist: ddt

color-matcher enables color transfer across images which comes in handy for automatic color-grading
of photographs, paintings and film sequences as well as light-field and stopmotion corrections. The methods behind
the mappings are based on the approach from <PERSON><PERSON><PERSON> et al., the Monge-Kantorovich Linearization (MKL) as proposed by
<PERSON><PERSON> et al. and our analytical solution to a Multi-Variate Gaussian Distribution (MVGD) transfer in conjunction with
classical histogram matching. As shown below our HM-MVGD-HM compound outperforms existing methods.

