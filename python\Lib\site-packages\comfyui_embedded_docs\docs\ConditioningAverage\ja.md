ConditioningAverageノードは、2つのコンディショニングデータセットをブレンドし、指定された強度に基づいて加重平均を適用するように設計されています。このプロセスにより、コンディショニングの影響を動的に調整し、生成されたコンテンツや特徴の微調整を可能にします。

## 入力

| パラメータ             | Comfy dtype        | 説明 |
|----------------------|--------------------|-------------|
| `conditioning_to`     | `CONDITIONING`     | ブレンドが適用される主要なコンディショニングデータセットを表します。これは加重平均操作の基礎として機能します。 |
| `conditioning_from`   | `CONDITIONING`     | 主要なデータセットにブレンドされる二次的なコンディショニングデータセットを示します。このデータは、指定された強度に基づいて最終出力に影響を与えます。 |
| `conditioning_to_strength` | `FLOAT` | 主要および二次的なコンディショニングデータ間のブレンドの強度を決定するスカラー値です。加重平均のバランスに直接影響を与えます。 |

## 出力

| パラメータ            | Comfy dtype        | 説明 |
|----------------------|--------------------|-------------|
| `conditioning`        | `CONDITIONING`     | 主要および二次的なコンディショニングデータをブレンドした結果で、加重平均を反映した新しいコンディショニングセットを生成します。 |
