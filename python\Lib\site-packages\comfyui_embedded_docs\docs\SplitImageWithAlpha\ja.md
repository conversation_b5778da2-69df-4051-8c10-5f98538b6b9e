
SplitImageWithAlphaノードは、画像の色成分とアルファ成分を分離するために設計されています。入力画像テンソルを処理し、RGBチャンネルを色成分として抽出し、アルファチャンネルを透明度成分として抽出します。これにより、これらの異なる画像要素を操作する必要がある操作を容易にします。

## 入力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 'image'パラメータは、RGBとアルファチャンネルを分離するための入力画像テンソルを表します。この操作にとって重要であり、分割のためのソースデータを提供します。 |

## 出力

| パラメータ | データ型 | 説明 |
|-----------|-------------|-------------|
| `image`   | `IMAGE`     | 'image'出力は、入力画像の分離されたRGBチャンネルを表し、透明度情報を含まない色成分を提供します。 |
| `mask`    | `MASK`      | 'mask'出力は、入力画像の分離されたアルファチャンネルを表し、透明度情報を提供します。 |
