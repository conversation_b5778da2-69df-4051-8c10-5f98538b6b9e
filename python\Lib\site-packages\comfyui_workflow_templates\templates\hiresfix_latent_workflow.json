{"id": "f677fc90-c65f-4c6f-9750-911b779df9e4", "revision": 0, "last_node_id": 18, "last_link_id": 24, "nodes": [{"id": 5, "type": "EmptyLatentImage", "pos": [-360, 300], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1], "color": "#323", "bgcolor": "#535"}, {"id": 10, "type": "LatentUpscale", "pos": [-360, 670], "size": [315, 130], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 10}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [14]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "LatentUpscale"}, "widgets_values": ["nearest-exact", 768, 768, "disabled"], "color": "#322", "bgcolor": "#533"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [510, 80], "size": [315, 474], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 18}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7, 10]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [873141143997374, "randomize", 12, 8, "dpmpp_sde", "normal", 1]}, {"id": 11, "type": "K<PERSON><PERSON><PERSON>", "pos": [80, 680], "size": [315, 474], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 23}, {"name": "positive", "type": "CONDITIONING", "link": 12}, {"name": "negative", "type": "CONDITIONING", "link": 13}, {"name": "latent_image", "type": "LATENT", "link": 14}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [15]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [32484370206415, "randomize", 14, 8, "dpmpp_2m", "simple", 0.5]}, {"id": 16, "type": "CheckpointLoaderSimple", "pos": [-360, 100], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [18, 23]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [19, 20]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [21, 22]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "DreamShaper_8_pruned.safetensors", "url": "https://huggingface.co/Lykon/DreamShaper/resolve/main/DreamShaper_8_pruned.safetensors", "directory": "checkpoints"}]}, "widgets_values": ["DreamShaper_8_pruned.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 17, "type": "<PERSON>downNote", "pos": [-710, 60], "size": [328, 128], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [Hires Fix - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/) — Overview\n> \n> [ComfyUI Image Upscale - docs.comfy.org](https://docs.comfy.org/tutorials/basic/upscale) — Upscaling step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}, {"id": 7, "type": "CLIPTextEncode", "pos": [20, 330], "size": [420, 180], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 20}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6, 13]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad hands, text, watermark,nsfw\n"], "color": "#223", "bgcolor": "#335"}, {"id": 8, "type": "VAEDecode", "pos": [870, 110], "size": [210, 46], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 21}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [24]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 18, "type": "PreviewImage", "pos": [870, 150], "size": [560, 400], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 24}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 13, "type": "VAEDecode", "pos": [510, 680], "size": [210, 46], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 15}, {"name": "vae", "type": "VAE", "link": 22}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [17]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 12, "type": "SaveImage", "pos": [520, 720], "size": [920, 560], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 17}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["ComfyUI"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [20, 110], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 19}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4, 12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["masterpiece HDR victorian portrait painting of woman, blonde hair, mountain nature, blue sky, (oil painting:1.1)"], "color": "#232", "bgcolor": "#353"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [10, 3, 0, 10, 0, "LATENT"], [12, 6, 0, 11, 1, "CONDITIONING"], [13, 7, 0, 11, 2, "CONDITIONING"], [14, 10, 0, 11, 3, "LATENT"], [15, 11, 0, 13, 0, "LATENT"], [17, 13, 0, 12, 0, "IMAGE"], [18, 16, 0, 3, 0, "MODEL"], [19, 16, 1, 6, 0, "CLIP"], [20, 16, 1, 7, 0, "CLIP"], [21, 16, 2, 8, 1, "VAE"], [22, 16, 2, 13, 1, "VAE"], [23, 16, 0, 11, 0, "MODEL"], [24, 8, 0, 18, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Step3 - Prompt", "bounding": [-10, 30, 490, 540], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Preview Intermediate Image", "bounding": [850, 40, 600, 530], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "<PERSON><PERSON>", "bounding": [-10, 600, 490, 570], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Save Final Image", "bounding": [510, 600, 950, 700], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Step1 - Load model", "bounding": [-370, 30, 335, 181.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Step2 - Image size", "bounding": [-370, 230, 335, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Step4 - Upscale latent", "bounding": [-370, 600, 335, 213.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7964670458514774, "offset": [1101.0055356008468, 217.40634717412536]}, "frontendVersion": "1.23.4"}, "version": 0.4}