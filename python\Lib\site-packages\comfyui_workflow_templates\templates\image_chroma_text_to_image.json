{"id": "242a6140-7341-49ca-876b-c01366b39b84", "revision": 0, "last_node_id": 20, "last_link_id": 23, "nodes": [{"id": 14, "type": "Note", "pos": [170, 570], "size": [260, 90], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["min_padding 1 is supposed to be the official way to inference chroma but I think the results are better with min_padding 0"], "color": "#432", "bgcolor": "#653"}, {"id": 11, "type": "T5TokenizerOptions", "pos": [170, 430], "size": [270, 82], "flags": {}, "order": 9, "mode": 4, "inputs": [{"name": "clip", "type": "CLIP", "link": 14}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [12, 20]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "T5TokenizerOptions"}, "widgets_values": [1, 0]}, {"id": 13, "type": "UNETLoader", "pos": [-221.24722290039062, 114.00627899169922], "size": [350, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [21]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "UNETLoader", "models": [{"name": "chroma-unlocked-v33.safetensors", "url": "https://huggingface.co/lodestones/Chroma/resolve/main/chroma-unlocked-v33.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["chroma-unlocked-v33.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "CLIPTextEncode", "pos": [470, 320], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 12}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, bad anatomy, extra digits, missing digits, extra limbs, missing limbs"], "color": "#323", "bgcolor": "#535"}, {"id": 17, "type": "Note", "pos": [170, 110], "size": [268.4952392578125, 107.76524353027344], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["This FreSca node makes anime images look better, if you are generating realistic images I recommend disabling it (CTRL-B)"], "color": "#432", "bgcolor": "#653"}, {"id": 16, "type": "FreSca", "pos": [170, 270], "size": [270, 110], "flags": {}, "order": 8, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 21}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [22]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "FreSca"}, "widgets_values": [1, 2.5000000000000004, 30]}, {"id": 10, "type": "CLIPLoader", "pos": [-221.24722290039062, 248.00628662109375], "size": [350, 110], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [14]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "CLIPLoader", "models": [{"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["t5xxl_fp8_e4m3fn_scaled.safetensors", "chroma", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 15, "type": "VAELoader", "pos": [-220, 570], "size": [350, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [17]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "EmptySD3LatentImage", "pos": [-220, 740], "size": [350, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [15]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 20, "type": "CLIPLoader", "pos": [-220, 410], "size": [350, 110], "flags": {}, "order": 6, "mode": 4, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "CLIPLoader", "models": [{"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["t5xxl_fp16.safetensors", "chroma", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 9, "type": "SaveImage", "pos": [950, 70], "size": [900, 840], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39"}, "widgets_values": ["ComfyUI"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [470, 600], "size": [280, 262], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 22}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 15}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [277064067047703, "randomize", 30, 4, "euler", "simple", 1]}, {"id": 8, "type": "VAEDecode", "pos": [770, 610], "size": [140, 46], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 6, "type": "CLIPTextEncode", "pos": [470, 110], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 20}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A majestic leopard resting in a vibrant jungle during the day, surrounded by lush plants with colorful leaves and scattered pink flowers. The sky above is bright blue, and sunlight filters through the foliage. The scene is cinematic, outdoors, focused on the animal, with no humans present. High detail, natural lighting, wildlife photography style.\n"], "color": "#232", "bgcolor": "#353"}, {"id": 19, "type": "<PERSON>downNote", "pos": [-700, 70], "size": [450, 440], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[English]\nChroma is still continuously updating their model. You can visit [here](https://huggingface.co/lodestones/Chroma/tree/main) to get the latest version for download. For other models, if you have used models related to Flux, then you probably don't need to download them again.\n\n[中文]\nChroma 还在不断更新他们的模型，你可以访问 [这里](https://huggingface.co/lodestones/Chroma/tree/main)获取到最新的版本下载，对于其它的模型，如果你有使用过 Flux 相关的模型，那么你应该不用重复下载了\n\n## Diffusion model\n\n[chroma-unlocked-v33.safetensors\n](https://huggingface.co/lodestones/Chroma/resolve/main/chroma-unlocked-v33.safetensors)\n\n## Text encoder\n\n[t5xxl_fp16.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors)\n\n**For low vram | 低显存**  : [t5xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors)\n\n## VAE\n\n[ae.safetensors](https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors)\n\n```\n📂 ComfyUI/\n├─ 📂 models/\n│   ├─ 📂 diffusion_models/\n│   │     └── chroma-unlocked-v33.safetensors # or latest model\n│   ├─ 📂 text_encoders/\n│   │     └── t5xxl_fp8_e4m3fn_scaled.safetensors  # or fp16\n│   └─ 📂 vae/\n│            └──  ae.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [12, 11, 0, 7, 0, "CLIP"], [14, 10, 0, 11, 0, "CLIP"], [15, 12, 0, 3, 3, "LATENT"], [17, 15, 0, 8, 1, "VAE"], [20, 11, 0, 6, 0, "CLIP"], [21, 13, 0, 16, 0, "MODEL"], [22, 16, 0, 3, 0, "MODEL"]], "groups": [{"id": 1, "title": "Step1: load models here", "bounding": [-230, 40, 370, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Step2: image size", "bounding": [-230, 670, 370, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Step3: prompts", "bounding": [460, 40, 460, 480], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Optional", "bounding": [160, 40, 290, 633.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Sampling & Decoding", "bounding": [460, 530, 460, 340], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.21.3", "ds": {"scale": 0.7943908024617633, "offset": [759.9134559408822, 199.49814168465738]}}, "version": 0.4}