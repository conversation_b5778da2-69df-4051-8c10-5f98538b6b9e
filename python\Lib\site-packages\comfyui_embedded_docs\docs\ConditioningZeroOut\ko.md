이 노드는 컨디셔닝 데이터 구조 내 특정 요소를 0으로 설정하여 후속 처리 단계에서 그들의 영향을 효과적으로 중화합니다. 이는 컨디셔닝의 내부 표현을 직접 조작해야 하는 고급 컨디셔닝 작업을 위해 설계되었습니다.

## 입력

| 매개변수 | Comfy dtype                | 설명 |
|-----------|----------------------------|-------------|
| `CONDITIONING` | CONDITIONING | 수정할 컨디셔닝 데이터 구조입니다. 이 노드는 각 컨디셔닝 항목 내 'pooled_output' 요소를 0으로 설정합니다. |

## 출력

| 매개변수 | Comfy dtype                | 설명 |
|-----------|----------------------------|-------------|
| `CONDITIONING` | CONDITIONING | 'pooled_output' 요소가 해당되는 경우 0으로 설정된 수정된 컨디셔닝 데이터 구조입니다. |
