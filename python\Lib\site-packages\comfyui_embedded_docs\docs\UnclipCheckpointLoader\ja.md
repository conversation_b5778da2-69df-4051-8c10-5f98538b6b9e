このノードは、`ComfyUI/models/checkpoints` フォルダーにあるモデルを検出し、
また、extra_model_paths.yaml ファイルで設定された追加パスのモデルも読み込みます。
場合によっては、**ComfyUIのインターフェースを更新する**必要があるかもしれませんので、対応するフォルダー内のモデルファイルを読み込むことができます。

unCLIPCheckpointLoaderノードは、unCLIPモデルに特化したチェックポイントをロードするために設計されています。指定されたチェックポイントからモデル、CLIPビジョンモジュール、VAEを取得し、初期化するプロセスを簡素化し、さらなる操作や分析のためのセットアップを効率化します。

## 入力

| フィールド      | Comfy dtype       | 説明                                                                       |
|----------------|-------------------|----------------------------------------------------------------------------|
| `ckpt_name`    | `COMBO[STRING]`   | ロードするチェックポイントの名前を指定し、事前定義されたディレクトリから正しいチェックポイントファイルを特定して取得し、モデルと設定の初期化を決定します。 |

## 出力

| フィールド       | Comfy dtype   | 説明                                                              | Python dtype         |
|-----------------|---------------|-------------------------------------------------------------------|----------------------|
| `model`         | `MODEL`       | チェックポイントからロードされた主要なモデルを表します。            | `torch.nn.Module`    |
| `clip`          | `CLIP`        | チェックポイントからロードされたCLIPモジュールを表します（利用可能な場合）。 | `torch.nn.Module`    |
| `vae`           | `VAE`         | チェックポイントからロードされたVAEモジュールを表します（利用可能な場合）。  | `torch.nn.Module`    |
| `clip_vision`   | `CLIP_VISION` | チェックポイントからロードされたCLIPビジョンモジュールを表します（利用可能な場合）。| `torch.nn.Module`    |
